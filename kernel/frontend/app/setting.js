import _ from 'lodash';
import fastCopy from 'fast-copy';

export default async function (app) {
    // Reactive setting on frontend!
    app.setting = key => {
        const settings = app.store.getters['session/settings'];
        const entry = fastCopy(settings[key]);

        if (_.isObject(entry)) {
            return !_.isUndefined(entry.value) ? entry.value : entry.default;
        }
    };

    // Real-time settings
    const listener = _.debounce(
        async () => {
            const user = app.get('user');

            if (_.isObject(user)) {
                app.store.commit('session/setSettings', {
                    settings: await app.rpc('kernel.common.get-settings')
                });

                app.emit('session-data-refreshed');
            }
        },
        300,
        {leading: false, trailing: true}
    );
    app.collection('kernel.settings').on('all', listener);
}
