import _ from 'lodash';
import Vue from 'vue';
import fastCopy from 'fast-copy';
import LoadingComponent from '@/kernel/frontend/components/elements/loading.vue';

let routeIterator = (app, p, item, parentItem = null) => {
    if (item.name.indexOf('.') === -1) {
        item.name = _.isNull(parentItem) ? `${p.name}.${item.name}` : `${parentItem.name}.${item.name}`;
    }

    if (Array.isArray(item.items)) {
        item.isGroup = true;

        item.items.forEach(subItem => {
            routeIterator(app, p, subItem, item);
        });
    } else {
        const extensionsKey = `route.${item.name}`;
        const extensions = app.extensionsMap[extensionsKey];
        if (Array.isArray(extensions)) {
            for (const extension of extensions) {
                if (_.isPlainObject(extension)) {
                    item = _.assign(item, extension);
                } else if (_.isFunction(extension)) {
                    item = extension(app, item);
                }
            }
        }

        let componentName = item.name.replace(new RegExp(`^${p.name}`), `${p.name}-views`).replace(/\./g, '-');
        if (componentName.indexOf('detail') === -1 && p.components.indexOf(componentName) === -1) {
            componentName = componentName + '-master';
        }

        item.component = componentName;
    }
};

export default async function (app) {
    const initFns = [];

    app.programs = {};

    window.entererp.programs.forEach(p => {
        if (Array.isArray(p.routes)) {
            const extensionsKey = `routes.${p.name}`;
            const extensions = app.extensionsMap[extensionsKey];
            if (Array.isArray(extensions)) {
                for (const extension of extensions) {
                    if (_.isPlainObject(extension)) {
                        p.routes = _.assign(p.routes, extension);
                    } else if (_.isFunction(extension)) {
                        p.routes = extension(app, fastCopy(p.routes));
                    }
                }
            }

            p.routes = p.routes.map(item => {
                // Normalize item
                routeIterator(app, p, item);

                return item;
            });

            p.hasSubRoutes = p.routes.some(
                r =>
                    r.isGroup && r.items.filter(i => i.name.indexOf('-detail') === -1 && _.isString(i.title)).length > 1
            );
        }

        if (_.isFunction(p.init)) {
            initFns.push(p.init);
        }

        app.programs[p.name] = _.omit(p, 'init');
    });

    for (const key of Object.keys(app.extensionsMap)) {
        if (key.indexOf('-components-') !== -1 || key.indexOf('-views-') !== -1) {
            const type = key.indexOf('-views-') !== -1 ? 'view' : 'component';
            const extensions = app.extensionsMap[key];

            if (Array.isArray(extensions) && extensions.length > 0) {
                const extension = extensions[extensions.length - 1];

                if (type === 'view') {
                    Vue.component(key.replace('view.', ''), () => ({
                        component: extension,
                        loading: LoadingComponent,
                        delay: 0
                    }));
                } else {
                    Vue.component(key.replace('component.', ''), extension);
                }
            }
        }
    }

    for (let fn of initFns) {
        await fn(app);
    }

    app.store.commit('shell/setPrograms', fastCopy(app.programs));
}
