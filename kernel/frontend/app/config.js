import _ from 'lodash';
import config from 'framework/config';

export default function (app) {
    // Initialize app config.
    config.setStore(window.entererp.config);

    const configMap = {};

    app.config = (key, value) => {
        if (_.isUndefined(value)) {
            if (!_.isUndefined(configMap[key])) return configMap[key];

            const hasKey = config.has(key);
            let result = null;

            if (!hasKey) result = null;
            else result = config.get(key);

            configMap[key] = result;

            return result;
        } else {
            config.set(key, value);

            return this;
        }
    };
}
