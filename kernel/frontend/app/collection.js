import _ from 'lodash';
import memoryService from 'framework/services/memory';
import Collection from 'framework/collections/collection';
import TreeCollection from 'framework/collections/tree-collection';

export default function (app) {
    app.collection = (name, definition = null) => {
        // Is tree collection.
        let isTree = false;
        const programName = name.split('.')[0];
        const program = _.find(window.entererp.programs, {name: programName});
        if (_.isObject(program)) {
            const collectionDefinition = _.find(program.collections, {
                name: name
            });

            isTree = _.isObject(collectionDefinition) && collectionDefinition.tree;
        }

        if (name.indexOf('.') !== -1) {
            name = name.split('.').join('/');
        }

        if (!_.isObject(definition)) {
            if (isTree) {
                return new TreeCollection(app, app.service(name));
            } else {
                return new Collection(app, app.service(name));
            }
        } else {
            const options = {};

            options.id = '_id';
            options.multi = true;
            options.paginate = _.isUndefined(definition.paginate) ? false : definition.paginate;

            app.use(name, memoryService(options));

            const service = app.service(name);
            service.noDB = true;
            service.isCollection = true;

            if (_.isObject(definition.hooks)) {
                service.hooks(definition.hooks);
            }
        }
    };
}
