import feathers from '@feathersjs/feathers';
import * as errors from '@feathersjs/errors';

// import appSchema from 'framework/schema/app-schema';
import appHooks from '../hooks';
import config from './config';
import schema from './schema';
import general from './general';
import datetime from './datetime';
import colors from './colors';
import collection from './collection';
import method from './method';
import initSocket from './init-socket';
import auth from './auth';
import setting from './setting';
import registry from './registry';
import translation from './translation';
import permission from './permission';
import rounding from './rounding';
import formatter from './formatter';
import converter from './converter';
import cubeApi from './cube-api';
import vue from './vue';
import extensions from './extensions';
import programs from './programs';
import '@/programs.json';

export default async function () {
    // Create app instance.
    const app = feathers();

    // Common vars
    app.set('arch', 'frontend');
    app.set('isDevelopment', process.env.NODE_ENV === 'development');
    app.set('isProduction', process.env.NODE_ENV !== 'development');
    app.set('hasReportDb', !!window.entererp.hasReportDb);

    // Initialize app errors.
    app.errors = errors;

    // Initialize app config.
    app.configure(config);

    // Initialize general app methods.
    app.configure(general);

    // Initialize app date time.
    app.configure(datetime);

    // Initialize schema.
    // app.configure(appSchema);
    app.configure(schema);
    // if (process.env.NODE_ENV === 'development') {
    //     app.configure(schema);
    // } else {
    //     app.configure(appSchema);
    // }

    // Initialize colors system.
    app.configure(colors);

    // Initialize collection system.
    app.configure(collection);

    // Initialize method system.
    app.configure(method);

    // Initialize socket.
    app.configure(initSocket);

    // Initialize auth.
    app.configure(auth);

    // Initialize persistent setting system.
    app.configure(setting);

    // Initialize persistent registry system.
    app.configure(registry);

    // Initialize translation system.
    app.configure(translation);

    // Initialize permission system.
    app.configure(permission);

    // Initialize rounding.
    app.configure(rounding);

    // Initialize formatter.
    app.configure(formatter);

    // Initialize converter.
    app.configure(converter);

    // Initialize cubejs api.
    app.configure(cubeApi);

    // Initialize vue.
    app.configure(vue);

    // Initialize application level hooks.
    app.hooks(appHooks);

    // Initialize extensions.
    app.configure(extensions);

    // Initialize programs.
    await app.configure(programs);

    // Mount app
    app.vm.$mount('#app');
}
