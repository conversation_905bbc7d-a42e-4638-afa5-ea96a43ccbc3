import _ from 'lodash';
import EJSON from 'framework/ejson';

export default function (app) {
    // Reactive registry on frontend!
    app.registry = (key, value, persist = true) => {
        if (!_.isUndefined(value)) {
            app.store.commit('registry/setEntry', {
                key,
                value: EJSON.stringify(value)
            });

            // Persist change.
            if (persist) {
                (async () => {
                    const existing = await app.collection('kernel.registry').findOne({
                        userId: app.get('user')._id,
                        key,
                        $select: ['_id']
                    });

                    if (_.isObject(existing)) {
                        app.collection('kernel.registry').patch({_id: existing._id}, {value: EJSON.stringify(value)});
                    } else {
                        app.collection('kernel.registry').create({
                            userId: app.get('user')._id,
                            key,
                            value: EJSON.stringify(value)
                        });
                    }
                })();
            }
        } else {
            const entries = app.store.getters['registry/entries'];
            const entry = entries[key];

            if (entry) {
                return EJSON.parse(entry);
            }

            return null;
        }
    };

    // Real-time registry
    const listener = _.debounce(
        async data => {
            const user = app.get('user');

            if (!_.isPlainObject(user) || !_.isPlainObject(data)) return;

            const existing = app.registry(data.key);

            if (!_.isNull(existing) && EJSON.stringify(existing) === data.value) return;

            app.registry(data.key, EJSON.parse(data.value), false);
        },
        300,
        {leading: false, trailing: true}
    );
    app.collection('kernel.registry').on('all', listener);
}
