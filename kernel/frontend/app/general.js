import _ from 'lodash';
import Big from 'big.js';
import LocalStorage from 'framework/local-storage';
import {trim} from 'framework/helpers';

export default function (app) {
    app.set('clientVersion', window.entererp.clientVersion);

    app.hasModule = module => {
        const license = app.get('license');

        if (!!license) {
            const licenseModule = (license.modules || []).find(m => m.name === module);

            if (!!licenseModule) {
                return !!licenseModule.isActive;
            }

            return true;
        }

        return true;
    };
    app.hasFeature = feature => {
        const license = app.get('license');

        if (!!license) {
            const licenseFeature = (license.features || []).find(f => f.name === feature);

            if (!!licenseFeature) {
                return !!licenseFeature.isActive;
            }

            return true;
        }

        return true;
    };

    const existingClientVersion = localStorage.getItem('clientVersion');
    if (
        _.isNull(existingClientVersion) ||
        (_.isString(existingClientVersion) && existingClientVersion !== app.get('clientVersion'))
    ) {
        localStorage.clear();
        localStorage.setItem('clientVersion', app.get('clientVersion'));
    }

    app.localStorage = new LocalStorage(app.get('clientVersion'));

    // Delete cached collection records.
    // @TODO -> This is a temporary fix. Better implementation would be by using pouchDB and collection record versions.
    // if (app.get('isPackaged')) {
    //     app.localStorage.remove('cachedCollectionRecords');
    // }

    app.absoluteUrl = (url = null) => {
        let str = `${app.config('server.protocol')}://${app.config('server.host')}`;

        if (app.config('server.port') !== 80) {
            str += `:${app.config('server.port')}`;
        }

        if (process.env.NODE_ENV === 'development') {
            let isDevServer = false;

            try {
                isDevServer = !!process.env.DEV_SERVER;
            } catch (error) {}

            if (isDevServer) {
                str = 'http://**************:3000';
            }
        }

        if (url) {
            str += `/${trim(url, '/')}`;
        }

        return str;
    };

    app.roundNumber = (number, precision = 0) => {
        precision = _.isNumber(precision) ? precision : app.setting('system.amountPrecision');

        return Number(Big(number).round(precision));
    };
    app.roundNumberComplex = (number, precision = 0, rounding = null) => {
        precision = _.isNumber(precision) ? precision : app.setting('system.amountPrecision');

        if (rounding === 'up') {
            return Number(Big(number).round(precision, Big.roundUp));
        } else if (rounding === 'down') {
            return Number(Big(number).round(precision, Big.roundDown));
        }

        return Number(Big(number).round(precision));
    };

    app.defaultAccountingAccount = (key, module = null) => {
        const defaultAccounts = app.setting('system.defaultAccounts');
        const entry =
            module !== null
                ? defaultAccounts.find(account => account.key === key && account.module === module)
                : defaultAccounts.find(account => account.key === key);

        if (_.isObject(entry)) {
            return entry.accountId;
        }
    };

    app.playSound = async (name, wait = true) => {
        if (!app.get('isDevelopment')) {
            const module = await import(`../assets/sounds/${name}.mp3`);
            const sound = new Audio(module.default);

            if (wait) {
                await sound.play();
            }

            sound.play();
        }
    };

    // if (app.get('isDevelopment')) {
    //     app.localStorage.remove('cachedCollectionRecords');
    // }
    app.setCachedCollectionRecords = payload => {
        app.set('cachedCollectionRecords', payload);
        // app.localStorage.set('cachedCollectionRecords', payload);
    };
    app.getCachedCollectionRecords = () => {
        const existing = app.get('cachedCollectionRecords');

        if (_.isPlainObject(existing)) {
            return existing;
        }

        // return app.localStorage.get('cachedCollectionRecords');
    };
    app.reFetchCachedCollectionRecords = async payload => {
        if (_.isObject(payload) && payload.collectionName) {
            const cachedCollectionRecords = app.getCachedCollectionRecords();
            const id = payload.id || payload._id;

            if (payload.type === 'create') {
                const record = await app.rpc('kernel.common.get-cached-collection-records', {
                    collectionName: payload.collectionName,
                    _id: id
                });

                cachedCollectionRecords[payload.collectionName].push(record);
            } else if (
                (!!payload.type && (payload.type === 'update' || payload.type === 'patch')) ||
                (!!id && !payload.type)
            ) {
                const record = await app.rpc('kernel.common.get-cached-collection-records', {
                    collectionName: payload.collectionName,
                    _id: id
                });
                const index = _.findIndex(
                    cachedCollectionRecords[payload.collectionName].filter(r => _.isPlainObject(r)),
                    r => r._id === id
                );

                cachedCollectionRecords[payload.collectionName][index] = record;
            } else {
                cachedCollectionRecords[payload.collectionName] = await app.rpc(
                    'kernel.common.get-cached-collection-records',
                    {
                        collectionName: payload.collectionName
                    }
                );
            }

            app.setCachedCollectionRecords(cachedCollectionRecords);
        }
    };
}
