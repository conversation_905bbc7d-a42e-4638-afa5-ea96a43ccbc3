import Vue from 'vue';
import ElementUI from 'element-ui';

import plugins from '../plugins';
import mixins from '../mixins';
import filters from '../filters';
import directives from '../directives';
import elements from '../components/elements';
import ui from '../components/ui';
import widgets from '../components/widgets';
import common from '../components/common';
import AppComponent from '../app.vue';

// Disable production tip.
Vue.config.devtools = true;
Vue.config.productionTip = false;
Vue.config.performance = true;

export default function (app) {
    // Initialize Element UI.
    Vue.use(ElementUI, {size: 'mini', zIndex: 3000});

    // Initialize plugins.
    Vue.use(plugins, {app});

    // Initialize global mixins.
    Vue.use(mixins, {app});

    // Initialize global filters.
    Vue.use(filters, {app});

    // Initialize global directives
    Vue.use(directives, {app});

    // Initialize element components.
    Vue.use(elements, {app});

    // Initialize ui components.
    Vue.use(ui, {app});

    // Initialize widget components.
    Vue.use(widgets, {app});

    // Initialize common components.
    Vue.use(common, {app});

    // Initialize Vue.
    app.vm = new Vue({
        store: app.store,
        router: app.router,
        render: h => h(AppComponent)
    });
}
