import _ from 'lodash';
import Base64 from 'framework/base64';
import authentication from '@feathersjs/authentication-client';

export default function (app) {
    const storageKey = 'entererp-jwt';

    // Set up authentication.
    app.configure(
        authentication({
            path: '/authentication',
            storageKey,
            storage: window.localStorage
        })
    );

    app.auth = {};

    app.auth.check = async () => {
        let result = false;

        try {
            await app.reAuthenticate();

            result = true;
        } catch {
            result = false;
        }

        return result;
    };

    app.auth.getUser = async () => {
        let user = null;

        if (_.isObject(app.get('user'))) {
            user = app.get('user');
        }

        if (_.isNull(user)) {
            try {
                const response = await app.reAuthenticate();
                const sessionData = await getSessionData(response.user._id);

                refreshSessionData(sessionData);

                user = sessionData.user;
            } catch {}
        }

        return user;
    };

    app.auth.lock = async () => {
        const user = app.get('user');

        const payload = {};
        if (user.avatar) payload.avatar = app.absoluteUrl(`files/${user.avatar}`);
        payload.name = user.name;
        payload.email = user.email;
        payload.passwordPlaceholder = app.translate('Password');
        payload.buttonLabel = app.translate('Unlock');
        app.localStorage.set('lockedPayload', payload);

        const programs = Array.from(_.values(app.store.state.shell.activeProgramMap));
        app.set('programsBeforeLock', programs);

        await app.logout();
        refreshSessionData(null);

        programs.forEach(p => app.store.commit('shell/removeActiveProgram', {id: p.id}));

        app.router.push({path: '/auth/locked'});
    };

    app.auth.login = async (email, password) => {
        const {user} = await app.authenticate({
            strategy: 'local',
            email,
            password
        });
        const sessionData = await getSessionData(user._id);

        refreshSessionData(sessionData);

        return sessionData.user;
    };

    app.auth.logout = async () => {
        await app.logout();
        refreshSessionData(null);

        const programs = Array.from(_.values(app.store.state.shell.activeProgramMap));
        programs.forEach(p => app.store.commit('shell/removeActiveProgram', {id: p.id}));

        app.router.push({path: '/auth/login'});
    };

    app.auth.refreshSessionData = async () => {
        const sessionData = await getSessionData(app.get('user')._id, true);

        refreshSessionData(sessionData);

        app.emit('session-data-refreshed');
    };

    app.auth.resetPassword = email => {
        return Promise.resolve(true);
    };

    app.auth.setUserLocation = () => {
        if (!!navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                result => {
                    const location = {
                        latitude: result.coords.latitude,
                        longitude: result.coords.longitude
                    };
                    const user = {
                        ...app.get('user'),
                        location
                    };

                    app.set('user', user);
                    app.store.commit('session/setUser', {user});
                },
                error => {
                    //console.log(error);
                }
            );
        }
    };

    async function getSessionData(userId, forceRefresh = false) {
        // Get session data.
        const sessionData = await app.rpc('kernel.common.get-session-data', {
            userId
        });
        const isoCode = sessionData.user.language.isoCode;

        const permissions = {};
        const recordPermissions = {};
        const menus = {};
        for (const permission of sessionData.user.permissions || []) {
            permissions[permission.name] = permission;
        }
        for (const recordPermission of sessionData.user.recordPermissions || []) {
            recordPermissions[recordPermission.name] = recordPermission;
        }
        for (const menu of sessionData.user.menus || []) {
            menus[menu.name] = menu;
        }
        sessionData.user.permissions = permissions;
        sessionData.user.recordPermissions = recordPermissions;
        sessionData.user.menus = menus;

        if (forceRefresh) {
            // Get translations.
            sessionData.translations = await app.rpc('kernel.common.get-translations', {isoCode});
        } else {
            // Get translations.
            sessionData.translations = window.entererp.translations[isoCode];
        }

        // Get cached collection records.
        const existingCachedCollectionRecords = app.getCachedCollectionRecords();
        if (_.isPlainObject(existingCachedCollectionRecords)) {
            sessionData.cachedCollectionRecords = existingCachedCollectionRecords;
        } else {
            sessionData.cachedCollectionRecords = await app.rpc('kernel.common.get-cached-collection-records');
        }

        // Initialize cached collection listeners.
        Object.keys(sessionData.cachedCollectionRecords).forEach(collectionName => {
            const collection = app.collection(collectionName);

            // Remove existing cached collection listeners.
            collection.removeListener('all', reFetchCachedCollectionRecords);

            // Setup cached collection listeners.
            collection.on('all', reFetchCachedCollectionRecords);
        });

        if (!!sessionData.elm) {
            try {
                app.set('license', JSON.parse(String.fromCharCode.apply(null, Base64.decode(sessionData.elm))));
            } catch (e) {
                console.error(e);
            }

            delete sessionData.elm;
        }

        return sessionData;
    }

    function refreshSessionData(sessionData) {
        if (!_.isNull(sessionData)) {
            app.store.commit('session/setUser', {user: sessionData.user});
            app.store.commit('session/setCompany', {
                company: sessionData.company
            });
            app.store.commit('session/setSettings', {
                settings: sessionData.settings
            });
            app.store.commit('session/setFormatterOptions', {
                formatterOptions: sessionData.formatterOptions
            });
            app.store.commit('session/setTranslations', {
                translations: sessionData.translations
            });
            app.store.commit('session/setUnReadNotificationCount', {
                count: sessionData.unReadNotificationCount
            });
            app.store.commit('registry/setEntries', {
                entries: sessionData.registry
            });
            app.set('user', app.store.getters['session/user']);
            app.setCachedCollectionRecords(sessionData.cachedCollectionRecords);
            app.initLocale();

            setTimeout(() => {
                app.auth.setUserLocation();
            }, 1000);
        } else {
            app.store.commit('session/setUser', {user: null});
            app.store.commit('session/setCompany', {company: null});
            app.store.commit('session/setSettings', {settings: null});
            app.store.commit('session/setFormatterOptions', {
                formatterOptions: null
            });
            app.store.commit('session/setTranslations', {translations: null});
            app.store.commit('session/setUnReadNotificationCount', {count: 0});
            app.store.commit('registry/setEntries', {entries: null});
            app.set('user', null);
            app.set('cachedCollectionRecords', null);
            app.set('locale', null);
        }
    }

    async function reFetchCachedCollectionRecords(payload) {
        if (!!payload.collectionName && !!payload.type) {
            await app.reFetchCachedCollectionRecords(payload);

            if (payload.type === 'create') {
                app.collection(payload.collectionName).emit('created');
            } else if (payload.type === 'update') {
                app.collection(payload.collectionName).emit('updated');
            } else if (payload.type === 'patch') {
                app.collection(payload.collectionName).emit('patched');
            } else if (payload.type === 'remove') {
                app.collection(payload.collectionName).emit('removed');
            }
        }
    }

    // Real-time user sync.
    const usersUpdateListener = _.debounce(
        async data => {
            if (_.isObject(app.get('user')) && _.isObject(data)) {
                if (data._id === app.get('user')._id) {
                    await app.auth.logout();

                    window.location.reload();
                    // const user = _.pick(data, [
                    //     'name', 'email', 'languageId', 'timezone',
                    //     'groupIds', 'branchIds', 'inactiveSessionTimeout', 'partnerId',
                    //     'avatar', 'isRoot', 'isActive',
                    //     'recordPermissions', 'menus', 'language'
                    // ]);
                    //
                    // app.set('user', user);
                    // app.store.commit('session/setUser', {user});

                    // await app.auth.refreshSessionData();
                }
            }
        },
        300,
        {leading: false, trailing: true}
    );
    app.collection('kernel.users').on('updated', usersUpdateListener);
    app.collection('kernel.users').on('patched', usersUpdateListener);

    // Session life.
    (() => {
        let sessionStartTime = null;
        let sessionTimerIdx = null;
        const resetTimer = () => (sessionStartTime = app.datetime.local().toJSDate());
        const resetTimerDebounced = _.debounce(resetTimer, 1000, {
            leading: true,
            trailing: false
        });

        app.on('authenticated', () => {
            resetTimer();

            sessionTimerIdx = setInterval(() => {
                if (!_.isNull(sessionStartTime)) {
                    const user = app.get('user');
                    const inactiveSessionTimeout = user.inactiveSessionTimeout;
                    const now = app.datetime.local();
                    const sessionStart = app.datetime.fromJSDate(sessionStartTime);
                    const diff = now.diff(sessionStart).as('second');

                    if (diff > inactiveSessionTimeout * 60 && !app.get('disableScreenLock')) {
                        app.auth.lock();

                        clearInterval(sessionTimerIdx);
                    }
                }
            }, 1000 * 30);

            window.addEventListener('mousemove', resetTimerDebounced, false);
            window.addEventListener('mousedown', resetTimerDebounced, false);
            window.addEventListener('keypress', resetTimerDebounced, false);
            window.addEventListener('touchmove', resetTimerDebounced, false);
        });

        app.on('logout', () => {
            sessionStartTime = null;
            clearInterval(sessionTimerIdx);

            window.removeEventListener('mousemove', resetTimerDebounced, false);
            window.removeEventListener('mousedown', resetTimerDebounced, false);
            window.removeEventListener('keypress', resetTimerDebounced, false);
            window.removeEventListener('touchmove', resetTimerDebounced, false);
        });
    })();
}
