import _ from 'lodash';
import {setLocale as setDateTimeLocale} from 'framework/date';
import {template} from 'framework/helpers';

import ElementUILangEN from 'element-ui/lib/locale/lang/en';
import ElementUILangTR from 'element-ui/lib/locale/lang/tr-TR';
import ElementUILocale from 'element-ui/lib/locale';

export default function (app) {
    // const reportedKeys = [];
    //
    // const reportUntranslated = async (key, program) => {
    //     const collection = app.collection('kernel.translations');
    //     const user = app.get('user');
    //
    //     if ((await collection.count({key})) < 1 && _.isObject(user)) {
    //         collection.create({
    //             locale: user.language.isoCode,
    //             program: program ? program : 'unknown',
    //             key
    //         });
    //     }
    // };

    // Reactive translation on frontend!
    app.translate = (key, params) => {
        const translation = app.store.getters['session/translation'](key);
        let message = '';

        if (typeof translation === 'object') {
            message = !!translation.message ? translation.message : translation.key;
        } else {
            message = key;
        }

        if (typeof params !== 'object') {
            return message;
        }

        return template(message, params);
    };

    // Initialize locales of used the libraries
    // and plugins according to the logged-in used language.
    app.initLocale = () => {
        const user = app.get('user');
        const locale = user.language.isoCode;

        app.set('locale', locale);

        // Date time locale.
        setDateTimeLocale(locale);

        // Element UI locale.
        const elementUILocales = {
            en: ElementUILangEN,
            tr: ElementUILangTR
        };
        ElementUILocale.use(elementUILocales[locale]);

        // Translate schema messages.
        app.translateSchemaMessages();
    };

    // Real-time translations
    // const listener = _.debounce(async data => {
    //     const user = app.get('user');
    //
    //     if (_.isObject(user)) {
    //         const isoCode = user.language.isoCode;
    //
    //         app.store.commit('session/setTranslations', {translations: await app.rpc('kernel.common.get-translations', {isoCode})});
    //     }
    // }, 300, {leading: true, trailing: false});
    // app.collection('kernel.translations').on('all', listener);
}
