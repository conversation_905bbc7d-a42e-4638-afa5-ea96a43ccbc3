import _ from 'lodash';

export default function (app) {
    app.extensionsMap = {};
    app.extend = params => {
        if (params.type === 'view') {
            const programName = params.name.split('.')[0];
            const complement = params.name.replace(new RegExp(`^${programName}.`), '');

            params.name = `${programName}.views.${complement}`.replace(/\./g, '-');
        }

        if (params.type === 'component') {
            const programName = params.name.split('.')[0];
            const complement = params.name.replace(new RegExp(`^${programName}.`), '');

            params.name = `${programName}.components.${complement}`.replace(/\./g, '-');
        }

        const key = `${params.type}.${params.name}`;

        if (!Array.isArray(app.extensionsMap[key])) {
            app.extensionsMap[key] = [];
        }

        app.extensionsMap[key].push(params.payload);
    };

    for (const program of window.entererp.programs) {
        if (_.isFunction(program.extend)) {
            program.extend(app);
        }
    }
}
