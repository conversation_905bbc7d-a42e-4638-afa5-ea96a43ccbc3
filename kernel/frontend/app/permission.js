import _ from 'lodash';

export default function (app) {
    app.checkPermission = payload => {
        const user = app.get('user');

        if (!_.isObject(user)) return false;
        else if (user.isRoot) return true;

        const permissions = user.permissions || [];
        const recordPermissions = user.recordPermissions || [];
        const menus = user.menus || [];
        const type = payload.type || 'record';

        if (type === 'permission') {
            const permissionName = payload.name || null;

            if (_.isNull(permissionName)) {
                throw new Error('Invalid payload supplied to checkPermission!');
            }

            const permission = permissions[permissionName];
            if (!!permission) {
                return !!permission.canDo;
            }

            return false;
        } else if (type === 'record') {
            const collectionName = payload.name || null;
            const method = payload.method || null;

            if (_.isNull(collectionName) || _.isNull(method)) {
                throw new Error('Invalid payload supplied to checkPermission!');
            }

            const recordPermission = recordPermissions[collectionName];
            if (!!recordPermission) {
                return recordPermission[method] === 'yes' || recordPermission[method] === 'owned';
            }

            return false;
        } else if (type === 'menu') {
            const menuName = payload.name || null;

            if (_.isNull(menuName)) {
                throw new Error('Invalid payload supplied to checkPermission!');
            }

            const menu = menus[menuName];
            if (!!menu) {
                return !!menu.canAccess;
            }

            return false;
        }

        return false;
    };

    app.hasPermission = payload => {
        let hasPermission = false;

        try {
            hasPermission = !!app.checkPermission(payload);
        } catch (error) {}

        return hasPermission;
    };
}
