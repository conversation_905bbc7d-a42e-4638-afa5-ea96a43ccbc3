import _ from 'lodash';

export default function (app) {
    app.rpc = (name, payload = {}) => {
        if (name.indexOf('.') !== -1) {
            name = name.split('.').join('/');
        }

        const service = app.service(`method-${name}`);

        // Fix feathers errors "First parameter for 'create' must be an object".
        if (!_.isObject(payload)) {
            payload = {
                __value: payload
            };
        }

        return service.create(payload);
    };
}
