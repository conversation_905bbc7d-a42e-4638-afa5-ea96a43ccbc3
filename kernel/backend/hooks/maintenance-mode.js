import _ from 'lodash';

export default function (context) {
    if (!!context.params && !!context.params.user) {
        const app = context.app;
        const maintenanceMode = app.setting('system.maintenanceMode');
        const user = context.params.user;

        if (
            _.isBoolean(maintenanceMode) &&
            maintenanceMode &&
            user.email !== '<EMAIL>' &&
            user.email !== '<EMAIL>'
        ) {
            throw new app.errors.Unprocessable(
                app.translate(
                    'Your operation could not be performed because the system is currently in maintenance mode!'
                )
            );
        }
    }

    return context;
}
