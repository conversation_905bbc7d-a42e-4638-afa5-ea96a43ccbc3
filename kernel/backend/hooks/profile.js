export default function (context) {
    if (process.env.NODE_ENV === 'development') {
        if (context.type === 'before') {
            context.__startTime = new Date();
        } else if (context.type === 'after') {
            const method = context.method;
            const path = context.path;
            const start = context.__startTime;
            const now = new Date();

            console.log(`${method} ${path} ${now.getTime() - start.getTime()} ms`);
        }
    }

    return context;
}
