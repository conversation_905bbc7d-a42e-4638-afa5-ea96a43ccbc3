import {RateLimiterRedis} from 'rate-limiter-flexible';

// const userLimiter = new RateLimiterRedis({
//     storeClient: redisClient,
//     points: 50,
//     duration: 1,
//     execEvenly: false,
//     blockDuration: 5,
//     keyPrefix: 'userRateLimiter'
// });

let guestLimiter = null;

export default async function (context) {
    const errors = context.app.errors;

    if (context.params.provider === 'socketio' && context.params.socketId) {
        if (guestLimiter === null) {
            guestLimiter = new RateLimiterRedis({
                storeClient: context.app.cache.client,
                points: 5,
                duration: 1,
                execEvenly: false,
                blockDuration: 10,
                keyPrefix: 'guestRateLimiter'
            });
        }

        const socketId = context.params.socketId;

        try {
            if (!!context.params.authentication && Object.keys(context.params.authentication).length > 0) {
                // await userLimiter.consume(socketId);
            } else {
                await guestLimiter.consume(socketId);
            }
        } catch (error) {
            if (error instanceof Error) {
                throw new errors.Unprocessable();
            } else {
                const seconds = Math.round(error.msBeforeNext / 1000) || 1;

                throw new errors.TooManyRequests('Too many requests', {
                    reTryAfter: seconds
                });
            }
        }
    }

    return context;
}
