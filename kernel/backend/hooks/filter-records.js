import _ from 'lodash';

export default async function (context) {
    const app = context.app;
    const user = context.params.user;
    const checkPermission = !!context.params.checkPermission;

    // Don't check permission for the server request.
    if (!checkPermission && !context.params.provider) {
        return context;
    }

    // If check disabled, skip.
    if (!!context.params.disablePermissionCheck) {
        return context;
    }

    // Check only collection permissions.
    if (!context.isCollection) {
        return context;
    }

    // Disallow un-authenticated request.
    if (!user || Object.keys(user).length < 1) {
        throw new app.errors.Forbidden(
            context.translate('You do not have sufficient permissions to perform this operation!')
        );
    }

    // Root user allowed all to do all operations.
    if (user.isRoot) {
        return context;
    }

    // Check permission only collection that has title.
    if (!context.service.title) {
        return context;
    }

    // Get collection name.
    const collectionName = context.path.indexOf('/') !== -1 ? context.path.split('/').join('.') : context.path;

    // Get permissions.
    const mappedRecordPermissions = context.app.get('mappedRecordPermissions') || {};
    let permission = null;
    if (_.isPlainObject(mappedRecordPermissions[user._id])) {
        permission = mappedRecordPermissions[user._id][collectionName];

        if (!_.isPlainObject(permission)) {
            permission = (user.recordPermissions || []).find(p => p.name === collectionName);
            mappedRecordPermissions[user._id][collectionName] = permission;

            context.app.set('mappedRecordPermissions', mappedRecordPermissions);
        }
    } else {
        permission = (user.recordPermissions || []).find(p => p.name === collectionName);
        mappedRecordPermissions[user._id] = {};
        mappedRecordPermissions[user._id][collectionName] = permission;

        context.app.set('mappedRecordPermissions', mappedRecordPermissions);
    }

    // Check if the user has any permission for this collection.
    if (!permission) {
        throw new app.errors.Forbidden(
            context.translate('You do not have sufficient permissions to perform this operation!')
        );
    }

    // Check read permission.
    if (context.method === 'find') {
        const hasId = !!context.params.query._id && context.params.query._id.length > 0;

        if (!hasId) {
            if (permission.read === 'no') {
                throw new app.errors.Forbidden(
                    context.translate('You do not have sufficient permissions to perform this operation!')
                );
            } else if (permission.read === 'owned') {
                if (!context.params.query) context.params.query = {};

                if (context.service.assignable) {
                    const q = {$or: [{createdBy: user._id}, {assignedBy: user._id}, {assignedTo: user._id}]};

                    if (!Array.isArray(context.params.query.$and)) context.params.query.$and = [];
                    context.params.query.$and.push(q);
                } else {
                    context.params.query.createdBy = user._id;
                }
            }
        }
    }

    // Check create permission.
    else if (context.method === 'create') {
        if (permission.create === 'no') {
            throw new app.errors.Forbidden(
                context.translate('You do not have sufficient permissions to perform this operation!')
            );
        }
    }

    // Check update permission.
    else if (context.method === 'update' || context.method === 'patch') {
        const id = context.id;
        const query = context.params.query;

        if (_.isString(id) && id) {
            if (permission.update === 'no') {
                throw new app.errors.Forbidden(
                    context.translate('You do not have sufficient permissions to perform this operation!')
                );
            } else if (permission.update === 'owned') {
                let countQuery = {
                    _id: id,
                    createdBy: user._id
                };

                if (context.service.assignable) {
                    countQuery = {
                        _id: id,
                        $or: [{createdBy: user._id}, {assignedBy: user._id}, {assignedTo: user._id}]
                    };
                }

                const userRecord = await app.collection(collectionName).findOne({...countQuery, $select: ['_id']});

                if (!userRecord) {
                    throw new app.errors.Forbidden(
                        context.translate('You do not have sufficient permissions to perform this operation!')
                    );
                }
            }
        } else if (_.isPlainObject(query)) {
            if (permission.update === 'no') {
                throw new app.errors.Forbidden(
                    context.translate('You do not have sufficient permissions to perform this operation!')
                );
            } else if (permission.update === 'owned') {
                let countQuery = {
                    _id: id || query._id,
                    createdBy: user._id
                };

                if (context.service.assignable) {
                    countQuery = {
                        _id: id || query._id,
                        $or: [{createdBy: user._id}, {assignedBy: user._id}, {assignedTo: user._id}]
                    };
                }

                const userRecord = await app.collection(collectionName).findOne({...countQuery, $select: ['_id']});

                if (!userRecord) {
                    throw new app.errors.Forbidden(
                        context.translate('You do not have sufficient permissions to perform this operation!')
                    );
                }

                if (!_.isPlainObject(context.params.query)) context.params.query = {};

                if (context.service.assignable) {
                    const q = {$or: [{createdBy: user._id}, {assignedBy: user._id}, {assignedTo: user._id}]};

                    if (!Array.isArray(context.params.query.$and)) context.params.query.$and = [];
                    context.params.query.$and.push(q);
                } else {
                    context.params.query.createdBy = user._id;
                }
            }
        }
    }

    // Check delete permission.
    else if (context.method === 'remove') {
        const id = context.id;
        const query = context.params.query;

        if (_.isString(id) && id) {
            if (permission.delete === 'no') {
                throw new app.errors.Forbidden(
                    context.translate('You do not have sufficient permissions to perform this operation!')
                );
            } else if (permission.delete === 'owned') {
                let countQuery = {
                    _id: id,
                    createdBy: user._id
                };

                if (context.service.assignable) {
                    countQuery = {
                        _id: id,
                        $or: [{createdBy: user._id}, {assignedBy: user._id}, {assignedTo: user._id}]
                    };
                }

                const userRecord = await app.collection(collectionName).findOne({...countQuery, $select: ['_id']});

                if (!userRecord) {
                    throw new app.errors.Forbidden(
                        context.translate('You do not have sufficient permissions to perform this operation!')
                    );
                }
            }
        } else if (_.isPlainObject(query)) {
            if (permission.delete === 'no') {
                throw new app.errors.Forbidden(
                    context.translate('You do not have sufficient permissions to perform this operation!')
                );
            } else if (permission.delete === 'owned') {
                let countQuery = {
                    _id: id || query._id,
                    createdBy: user._id
                };

                if (context.service.assignable) {
                    countQuery = {
                        _id: id || query._id,
                        $or: [{createdBy: user._id}, {assignedBy: user._id}, {assignedTo: user._id}]
                    };
                }

                const userRecord = await app.collection(collectionName).findOne({...countQuery, $select: ['_id']});

                if (!userRecord) {
                    throw new app.errors.Forbidden(
                        context.translate('You do not have sufficient permissions to perform this operation!')
                    );
                }

                if (!_.isPlainObject(context.params.query)) context.params.query = {};

                if (context.service.assignable) {
                    const q = {$or: [{createdBy: user._id}, {assignedBy: user._id}, {assignedTo: user._id}]};

                    if (!Array.isArray(context.params.query.$and)) context.params.query.$and = [];
                    context.params.query.$and.push(q);
                } else {
                    context.params.query.createdBy = user._id;
                }
            }
        }
    }

    return context;
}
