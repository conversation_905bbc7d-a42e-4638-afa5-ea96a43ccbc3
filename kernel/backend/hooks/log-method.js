export default async function (context) {
    if (!!context.service && context.service.isMethod) {
        const app = context.app;

        if (context.type === 'before') {
            context.__methodStartTime = Date.now();

            let log = {};
            log.status = 'started';
            log.startDate = app.datetime.local().toJSDate();
            log.name = context.path.replace('method-', '');
            if (!!context.params.user) {
                log.userId = context.params.user._id;
                log.user = {
                    code: context.params.user.code,
                    name: context.params.user.name
                };
            }
            if (log.name === 'kernel/database/bulk-write' && !!context.data && !!context.data.servicePath) {
                log.name = `${log.name}/${context.data.servicePath}`;
            }
            log = await app.collection('kernel.method-logs').create(log);

            context.__methodLogId = log._id;
        } else if (context.type === 'after') {
            await app.collection('kernel.method-logs').patch(
                {
                    _id: context.__methodLogId
                },
                {
                    status: 'completed',
                    endDate: app.datetime.local().toJSDate(),
                    time: Date.now() - context.__methodStartTime
                }
            );
        } else if (context.type === 'error') {
            await app.collection('kernel.method-logs').patch(
                {
                    _id: context.__methodLogId
                },
                {
                    status: 'errored',
                    endDate: app.datetime.local().toJSDate(),
                    time: Date.now() - context.__methodStartTime,
                    error: context.error.message
                }
            );
        } else {
            await app.collection('kernel.method-logs').patch(
                {
                    _id: context.__methodLogId
                },
                {
                    status: 'errored',
                    endDate: app.datetime.local().toJSDate(),
                    time: Date.now() - context.__methodStartTime,
                    error: 'Unknown'
                }
            );
        }
    }

    return context;
}
