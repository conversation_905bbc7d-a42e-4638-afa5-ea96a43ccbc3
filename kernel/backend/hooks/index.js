import {paramsFromClient, when} from 'feathers-hooks-common';
import authenticateJWT from 'framework/hooks/authenticate-jwt';

import rateLimit from './rate-limit';
import fixQueryParams from './fix-query-params';
import decorateContext from './decorate-context';
import disallowUnverified from './disallow-unverified';
import filterRecords from './filter-records';
import maintenanceMode from './maintenance-mode';
import validate from './validate';
import profile from './profile';
import logMethod from './log-method';

// Application hooks that run for every service.
export default {
    before: {
        all: [
            logMethod,
            // profile,
            rateLimit,
            when(context => {
                if (!context.params || !context.params.provider || !!context.params.authenticated) {
                    return false;
                }

                if (context.service.isMethod) {
                    return context.service.authenticate;
                }

                return context.path !== 'authentication';
            }, authenticateJWT()),
            paramsFromClient(
                'paginate',
                'userLocation',
                'disableActiveCheck',
                'disableInUseCheck',
                'disableTranslation',
                'disableBranchCheck',
                'disableSoftDelete',
                'disableWholeCount',
                'fromMobile',
                'currencyRate'
            ),
            fixQueryParams,
            decorateContext,
            when(context => {
                if (context.service.isMethod) {
                    return context.service.authenticate;
                }

                return context.path !== 'authentication';
            }, disallowUnverified),
            maintenanceMode,
            filterRecords
            // async context => {
            //     if (context.path === 'sale/orders') {
            //         return new Promise(resolve => {
            //             setTimeout(() => {
            //                 resolve(context);
            //             }, 60000);
            //         });
            //     }
            //
            //     return context;
            // }
        ],
        find: [],
        get: [],
        create: [validate],
        update: [validate],
        patch: [validate],
        remove: []
    },

    after: {
        all: [
            logMethod
            // profile
        ],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: []
    },

    error: {
        all: [logMethod],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: []
    }
};
