import _ from 'lodash';
import {getItems, replaceItems} from 'feathers-hooks-common';

export default async function (context) {
    if (!!context.params.disableValidation || context.method === 'update') {
        return context;
    }

    const app = context.app;
    let schema = context.service.schema;

    if (_.isPlainObject(schema) && Object.keys(schema).length > 0) {
        const schemaKey = context.service.collectionName;
        let isCreate = context.method === 'create',
            rawData = getItems(context),
            data = null,
            validation = null;

        if (isCreate && Array.isArray(rawData)) {
            data = [];

            if (rawData.length < 1) {
                throw new app.errors.Unprocessable('Empty array of records can not be created!');
            }

            for (let rd of rawData) {
                if (!!rd._id) {
                    delete rd._id;
                }

                const {
                    isValid,
                    errors,
                    data: d
                } = await app.schema.validate(schema, rd, {
                    clean: true,
                    isModifier: false,
                    schemaKey
                });
                if (!isValid) {
                    throw new app.errors.Unprocessable(errors[0]);
                }

                validation = {isValid, errors};

                data.push(d);
            }
        } else if (isCreate) {
            if (!!rawData._id) {
                delete rawData._id;
            }

            const {
                isValid,
                errors,
                data: d
            } = await app.schema.validate(schema, rawData, {
                clean: true,
                isModifier: false,
                schemaKey
            });

            data = d;
            validation = {isValid, errors};
        } else {
            // let patchData = {};
            //
            // for (let key in rawData) {
            //     if (key.charAt(0) !== '$') {
            //         patchData[key] = rawData[key];
            //     }
            //
            //     if (key === '$set') {
            //         patchData = {...patchData, ...(rawData[key] || {})};
            //     }
            // }
            //
            // if (!!patchData._id) {
            //     delete patchData._id;
            // }

            if (!!rawData._id) {
                delete rawData._id;
            }

            const {
                isValid,
                errors,
                data: d
            } = await app.schema.validate(schema, rawData, {
                clean: false,
                isModifier: true,
                schemaKey
            });

            data = {$set: d};
            validation = {isValid, errors};
        }

        if (!validation.isValid) {
            throw new app.errors.Unprocessable(validation.errors[0]);
        }

        replaceItems(context, data);
    }

    return context;
}
