import _ from 'lodash';
import {rawMongoQuery} from 'framework/helpers';

export default function (context) {
    if (!!context.params && !!context.params.query) {
        const paramsFields = [
            '$disableActiveCheck',
            '$disableInUseCheck',
            '$disableSoftDelete',
            '$disableTranslation',
            '$disableOrdering',
            '$disableBranchCheck',
            '$disablePermissionCheck',
            '$disableWholeCount'
        ];

        for (const paramsField of paramsFields) {
            if (!_.isUndefined(context.params.query[paramsField])) {
                context.params[paramsField.slice(1)] = context.params.query[paramsField];
                delete context.params.query[paramsField];
            }
        }

        if (Array.isArray(context.params.query.$and)) {
            context.params.query.$and = context.params.query.$and.map(q => {
                q = rawMongoQuery(q);

                delete q.$populate;

                return q;
            });
        }

        if (Array.isArray(context.params.query.$or)) {
            context.params.query.$or = context.params.query.$or.map(q => {
                q = rawMongoQuery(q);

                delete q.$populate;

                return q;
            });
        }
    }

    return context;
}
