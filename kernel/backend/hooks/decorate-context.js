import _ from 'lodash';

export default function (context) {
    const app = context.app;

    if (context.service.isCollection) {
        context.isCollection = true;
    } else if (context.service.isMethod) {
        context.isMethod = true;
    }

    // Find locale.
    let locale = app.config('app.locale');
    if (!!context.params && !!context.params.user && !!context.params.user.locale) {
        locale = context.params.user.locale;
    }

    // Translation context method.
    context.translate = (key, params = {}) => {
        return app.translate(key, locale, params);
    };

    // Translation on method service.
    if (!!context.isMethod) {
        context.service.translate = context.translate;
    }

    // Translate schema messages.
    // if (
    //     locale !== 'en' &&
    //     (context.isMethod || context.method === 'create' || context.method === 'update' || context.method === 'patch')
    // ) {
    //     // Translate schema messages.
    //     app.translateSchemaMessages(locale);
    // }

    return context;
}
