import _ from 'lodash';

export default {
    name: 'backups',
    title: 'Backups',
    softDelete: false,
    sync: false,
    noCache: true,
    schema: {
        date: {
            type: 'datetime',
            label: 'Date',
            index: true
        },
        name: {
            type: 'string',
            label: 'Name',
            index: true
        },
        description: {
            type: 'string',
            label: 'Name',
            index: true
        },
        userName: {
            type: 'string',
            label: 'User name',
            index: true
        },
        email: {
            type: 'string',
            label: 'Email address',
            regexp: 'EmailWithTLD',
            required: false
        },
        path: {
            type: 'string',
            label: 'Path'
        }
    },
    publish(app, data, context) {
        return app
            .channel('authenticated')
            .filter(connection => _.isPlainObject(connection.user) && !!connection.user.isRoot);
    }
};
