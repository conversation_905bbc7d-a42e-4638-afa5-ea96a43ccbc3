import _ from 'lodash';

export default {
    name: 'financial-projects',
    title: 'Financial Projects',
    cache: true,
    schema: {
        code: {
            type: 'string',
            label: 'Code'
        },
        name: {
            type: 'string',
            label: 'Name'
        },
        validFrom: {
            type: 'date',
            label: 'Valid from',
            required: false
        },
        validTo: {
            type: 'date',
            label: 'Valid to',
            required: false
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true
        }
    },
    hooks: {
        after: {
            update: [syncProject],
            patch: [syncProject]
        }
    }
};

async function syncProject(context) {
    const app = context.app;
    const items = Array.isArray(context.data) ? context.data : [context.data];
    const projects = await app.collection('project.projects').find({
        projectCode: {$in: items.map(item => item.code)},
        $select: ['_id', 'projectCode'],
        $disableSoftDelete: true
    });
    const operations = [];
    const transactionOperations = [];

    for (const item of items) {
        const project = projects.find(project => project.projectCode === item.code);

        if (!!project) {
            operations.push({
                updateOne: {
                    filter: {_id: project._id},
                    update: {
                        $set: {projectCode: item.code}
                    }
                }
            });
        }

        transactionOperations.push({
            updateOne: {
                filter: {financialProjectId: item._id},
                update: {
                    $set: {
                        financialProject: {
                            _id: item._id,
                            code: item.code,
                            name: item.name
                        }
                    }
                }
            }
        });
    }

    if (operations.length > 0) {
        await app.collection('project.projects').bulkWrite(operations);
    }

    if (transactionOperations.length > 0) {
        await app.collection('accounting.transactions').bulkWrite(transactionOperations);
    }

    return context;
}
