export default {
    name: 'countries',
    title: 'Countries',
    order: true,
    localCache: true,
    cache: true,
    schema: {
        name: {
            type: 'string',
            label: 'Name',
            translate: true,
            unique: true
        },
        code: {
            type: 'string',
            label: 'Code',
            unique: true
        },
        additionalCode: {
            type: 'string',
            label: 'Additional code',
            required: false,
            unique: true
        },
        currencyId: {
            type: 'string',
            label: 'Currency'
        },
        phoneCode: {
            type: 'string',
            label: 'Phone country code',
            required: false
        },
        vatLabel: {
            type: 'string',
            label: 'Vat label',
            required: false
        },
        addressFormat: {
            type: 'string',
            label: 'Address format'
        },
        phoneNumberFormats: [
            {
                type: 'object',
                blackbox: true,
                required: false
            }
        ],
        identityCharacterCount: {
            type: 'integer',
            label: 'Identity character count',
            min: 1,
            required: false
        },
        tinCharacterCount: {
            type: 'integer',
            label: 'TIN character count',
            min: 1,
            required: false
        },
        divisions: {
            type: [
                {
                    level: {
                        type: 'integer',
                        label: 'Level',
                        unique: true
                    },
                    code: {
                        type: 'string',
                        label: 'Code',
                        unique: true
                    },
                    name: {
                        type: 'string',
                        label: 'Name'
                    },
                    showInAddress: {
                        type: 'boolean',
                        label: 'Show in address',
                        default: true
                    },
                    showInLocations: {
                        type: 'boolean',
                        label: 'Show in locations',
                        default: true
                    },
                    suggestions: {
                        type: 'boolean',
                        label: 'Suggestions',
                        default: true
                    }
                }
            ],
            default: []
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true
        }
    },
    attributes: {
        currency: {
            collection: 'kernel.currencies',
            parentField: 'currencyId',
            childField: '_id',
            auto: true
        }
    },
    hooks: {
        after: {
            create: [deleteMobileHash],
            update: [deleteMobileHash],
            patch: [deleteMobileHash],
            remove: [deleteMobileHash]
        }
    }
};

async function deleteMobileHash(context) {
    const app = context.app;

    await app.cache.set('mobile-countries-hash', null);

    return context;
}
