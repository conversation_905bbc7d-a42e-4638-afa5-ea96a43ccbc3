import _ from 'lodash';

export default {
    name: 'resources',
    title: 'Resources',
    view: 'system.management.configuration.resources',
    labelParams: {
        from: 'name'
    },
    schema: {
        code: {
            type: 'string',
            label: 'Code',
            unique: true,
            index: true
        },
        name: {
            type: 'string',
            label: 'Name',
            index: true
        },
        scope: {
            type: ['string'],
            label: 'Scope',
            required: false,
            index: true
        },
        type: {
            type: 'string',
            label: 'Type',
            default: 'equipment',
            index: true
        },
        partnerId: {
            type: 'string',
            label: 'Partner',
            required: false,
            index: true
        },
        fixtureId: {
            type: 'string',
            label: 'Fixture',
            required: false,
            index: true
        },
        workingHoursId: {
            type: 'string',
            label: 'Working hours',
            required: false
        },
        capacityUnit: {
            type: 'string',
            label: 'Capacity unit',
            default: 'hour'
        },
        capacityUtilization: {
            type: 'decimal',
            label: 'Capacity utilization',
            default: 100
        },
        currencyId: {
            type: 'string',
            label: 'Currency'
        },
        costType: {
            type: 'string',
            label: 'Cost type',
            default: 'per-hour'
        },
        cost: {
            type: 'decimal',
            label: 'Cost',
            default: 0
        },
        evaluateInCostCalculation: {
            type: 'boolean',
            label: 'Evaluate in cost calculation',
            default: true
        },
        evaluateInCapacityPlanning: {
            type: 'boolean',
            label: 'Evaluate in capacity planning',
            default: true
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true,
            index: true
        },
        services: {
            type: [
                {
                    id: {
                        type: 'string',
                        required: false
                    },
                    productId: {
                        type: 'string',
                        label: 'Product'
                    },
                    productCode: {
                        type: 'string',
                        label: 'Product code'
                    },
                    productDefinition: {
                        type: 'string',
                        label: 'Product definition'
                    },
                    taskType: {
                        type: 'string',
                        label: 'Task type',
                        required: false
                    },
                    calculationMethod: {
                        type: 'string',
                        label: 'Calculation method',
                        default: 'fixed'
                    },
                    unitId: {
                        type: 'string',
                        label: 'Unit'
                    },
                    quantity: {
                        type: 'decimal',
                        label: 'Quantity',
                        default: 1
                    },
                    unitCost: {
                        type: 'decimal',
                        label: 'Unit cost',
                        default: 0
                    },
                    cost: {
                        type: 'decimal',
                        label: 'Cost',
                        default: 0
                    }
                }
            ],
            default: []
        }
    },
    attributes: {
        fixture: {
            collection: 'am.fixtures',
            parentField: 'fixtureId',
            childField: '_id'
        },
        partner: {
            collection: 'kernel.partners',
            parentField: 'partnerId',
            childField: '_id'
        },
        workingHours: {
            collection: 'kernel.working-hours',
            parentField: 'workingHoursId',
            childField: '_id'
        },
        currency: {
            collection: 'kernel.currencies',
            parentField: 'currencyId',
            childField: '_id'
        }
    }
};
