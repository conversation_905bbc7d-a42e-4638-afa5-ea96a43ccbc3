import _ from 'lodash';
import fs from 'fs-extra';
import {toLower} from 'framework/helpers';

export default {
    name: 'files',
    uid: false,
    softDelete: false,
    schema: {
        name: {
            type: 'string',
            label: 'Name',
            index: true
        },
        extension: {
            type: 'string',
            label: 'Extension',
            required: false,
            index: true
        },
        contentType: {
            type: 'string',
            label: 'Content type',
            index: true
        },
        size: {
            // Bytes
            type: 'decimal',
            label: 'Size',
            index: true
        },
        path: {
            type: 'string',
            label: 'Path',
            index: true
        },
        uploadedAt: {
            type: 'datetime',
            label: 'Uploaded at',
            required: false,
            index: true
        },
        description: {
            type: 'string',
            label: 'Description',
            required: false,
            index: true
        },
        categoryId: {
            type: 'string',
            label: 'Category',
            required: false,
            index: true
        },
        tags: {
            type: [
                {
                    label: {
                        type: 'string',
                        index: true
                    },
                    color: {
                        type: 'string',
                        index: true
                    }
                }
            ],
            label: 'Tags',
            default: [],
            index: true
        },
        isTemporary: {
            type: 'boolean',
            label: 'Is temporary',
            default: false,
            index: true
        },
        params: {
            type: 'object',
            blackbox: true,
            required: false
        }
    },
    attributes: {
        category: {
            collection: 'kernel.file-categories',
            parentField: 'categoryId',
            childField: '_id'
        }
    },
    hooks: {
        after: {
            create: [updateTagsCache],
            update: [updateTagsCache],
            patch: [updateTagsCache],
            remove: [deleteFile, updateTagsCache]
        }
    },
    publish(app, data, context) {
        // return app.channel('authenticated').filter(connection => _.isPlainObject(connection.user) && !!connection.user.isRoot);
        return false;
    }
};

function deleteFile(context) {
    (async () => {
        const files = Array.isArray(context.result) ? context.result : [context.result];

        for (const file of files) {
            await fs.remove(file.path);
        }
    })();

    return context;
}

async function updateTagsCache(context) {
    const app = context.app;
    const files = Array.isArray(context.result) ? context.result : [context.result];
    let tags = await app.cache.get('attachment-tags');

    if (Array.isArray(tags)) {
        for (const file of files) {
            tags.push(...(file.tags || []));
        }

        const processed = [];
        for (const tag of tags) {
            if (processed.findIndex(p => toLower(p.label).trim() === toLower(tag.label).trim()) === -1) {
                processed.push(tag);
            }
        }
        tags = processed;

        await app.cache.set('attachment-tags', tags);
    }

    return context;
}
