import {hooks as authHooks} from '@feathersjs/authentication-local';
import _ from 'lodash';

const {hashPassword, protect} = authHooks;

export default {
    name: 'users',
    title: 'Users',
    branch: 'multiple',
    cache: true,
    schema: {
        code: {
            type: 'string',
            label: 'Code',
            index: true,
            unique: true
        },
        name: {
            type: 'string',
            label: 'Name',
            index: true
        },
        email: {
            type: 'string',
            label: 'Email address',
            regexp: 'EmailWithTLD',
            index: true,
            unique: true
        },
        validityPeriod: {
            type: 'integer',
            label: 'Period of validity',
            required: false
        },
        isVerified: {
            type: 'boolean',
            label: 'Is verified',
            default: false
        },
        passwordResetToken: {
            type: 'string',
            required: false
        },
        passwordResetExpires: {
            type: 'datetime',
            required: false
        },
        password: {
            type: 'string',
            label: 'Password',
            required: false
        },
        groupIds: {
            type: ['string'],
            label: 'Groups',
            default: [],
            index: true
        },
        languageId: {
            type: 'string',
            label: 'Language',
            index: true
        },
        timezone: {
            type: 'string',
            label: 'Timezone',
            required: false
        },
        inactiveSessionTimeout: {
            type: 'integer',
            label: 'Inactive session timeout',
            min: 1
        },
        partnerId: {
            type: 'string',
            label: 'Partner',
            index: true,
            required: false
        },
        note: {
            type: 'string',
            label: 'Note',
            required: false
        },
        avatar: {
            type: 'string',
            label: 'Avatar',
            required: false
        },
        isRoot: {
            type: 'boolean',
            default: false
        },
        apiUser: {
            type: 'boolean',
            label: 'Api user',
            default: false
        },
        isAdviser: {
            type: 'boolean',
            label: 'Is adviser',
            default: false
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true,
            index: true
        },
        permissions: {
            type: [
                {
                    name: 'string',
                    canDo: 'boolean'
                }
            ],
            default: []
        },
        recordPermissions: {
            type: [
                {
                    name: 'string',
                    read: 'string',
                    create: 'string',
                    update: 'string',
                    delete: 'string'
                }
            ],
            default: []
        },
        menus: {
            type: [
                {
                    name: 'string',
                    canAccess: 'boolean'
                }
            ],
            default: []
        },
        canLogInWithMultipleDevices: {
            type: 'boolean',
            label: 'Can login with multiple different devices',
            default: false
        },
        devices: {
            type: [
                {
                    deviceId: {
                        type: 'string'
                    },
                    deviceName: {
                        type: 'string'
                    },
                    registrationDate: {
                        type: 'datetime'
                    }
                }
            ],
            default: []
        },

        // Internal.
        mobileNotificationsToken: {
            type: 'string',
            required: false
        }
    },
    attributes: {
        async language(user) {
            return await this.app.collection('kernel.languages').findOne({_id: user.languageId});
        },
        partner: {
            collection: 'kernel.partners',
            parentField: '_id',
            childField: 'userAccountId'
        }
    },
    hooks: {
        before: {
            get: [],
            create: [setTimezone, setHashedPassword],
            update: [setHashedPassword],
            patch: [setHashedPassword]
        },

        after: {
            all: [
                // Make sure the password field is never sent to the client
                // Always must be the last hook.
                protect('password')
            ],
            get: [],
            create: [sendInvitation, setPermissions, syncGroups, removeMappedRecordPermissions, deleteMobileHash],
            update: [setPermissions, syncGroups, syncPartner, removeMappedRecordPermissions, deleteMobileHash],
            patch: [setPermissions, syncGroups, syncPartner, removeMappedRecordPermissions, deleteMobileHash],
            remove: [removeMappedRecordPermissions, deleteMobileHash]
        }
    }
};

function setTimezone(context) {
    for (const data of Array.isArray(context.data) ? context.data : [context.data]) {
        if (!data.timezone) {
            data.timezone = context.app.config('app.timezone');
        }
    }

    return context;
}

async function setHashedPassword(context) {
    const hash = hashPassword('password');

    if (context.method === 'create' && _.isObject(context.data) && _.isString(context.data.password)) {
        for (const data of Array.isArray(context.data) ? context.data : [context.data]) {
            const result = await hash({
                app: context.app,
                data: {password: data.password},
                type: 'before'
            });

            data.password = result.data.password;
        }
    } else if (_.isObject(context.data) && _.isObject(context.data.$set) && _.isString(context.data.$set.password)) {
        const result = await hash({
            app: context.app,
            data: {password: context.data.$set.password},
            type: 'before'
        });

        context.data.$set.password = result.data.password;
    }

    return context;
}

function setPermissions(context) {
    (async () => {
        const users = Array.isArray(context.result) ? context.result : [context.result];
        const operations = [];

        for (const user of users) {
            if (Array.isArray(user.groupIds) && user.groupIds.length > 0) {
                const groups = await context.app.collection('kernel.user-groups').find({
                    _id: {$in: user.groupIds},
                    $select: ['isRoot']
                });

                const data = {};
                data.isRoot = groups.some(group => group.isRoot);
                if (data.isRoot) {
                    data.permissions = [];
                    data.recordPermissions = [];
                    data.menus = [];
                }

                operations.push({
                    updateOne: {
                        filter: {_id: user._id},
                        update: {$set: data}
                    }
                });
            }
        }

        if (operations.length > 0) {
            await context.app.collection('kernel.users').bulkWrite(operations, {ordered: false});
        }
    })();

    return context;
}

async function sendInvitation(context) {
    const users = Array.isArray(context.result) ? context.result : [context.result];

    for (const user of users) {
        context.app.rpc('kernel.common.send-user-invitation', user, {
            user: context.params.user
        });
    }

    return context;
}

function syncGroups(context) {
    if (!_.isObject(context.result)) return context;

    (async () => {
        const groups = await context.app.collection('kernel.user-groups').find({$select: ['_id', 'userIds']});
        const user = context.result;
        const operations = [];

        for (const group of groups) {
            if (group.userIds.includes(user._id) && !user.groupIds.includes(group._id)) {
                operations.push({
                    updateOne: {
                        filter: {_id: group._id},
                        update: {$pull: {userIds: user._id}}
                    }
                });
            }

            if (!group.userIds.includes(user._id) && user.groupIds.includes(group._id)) {
                operations.push({
                    updateOne: {
                        filter: {_id: group._id},
                        update: {$addToSet: {userIds: user._id}}
                    }
                });
            }
        }

        if (operations.length > 0) {
            await context.app.collection('kernel.user-groups').bulkWrite(operations, {ordered: false});
        }
    })();

    return context;
}

async function syncPartner(context) {
    const app = context.app;

    if (context.params.disableSyncPartner) {
        delete context.params.disableSyncPartner;

        return context;
    }

    const result = context.result;
    const partner = await app.collection('kernel.partners').findOne(
        {
            userAccountId: result._id,
            $select: ['_id']
        },
        {
            disableActiveCheck: true,
            disableSoftDelete: true
        }
    );

    // if (!result.partnerId) {
    //     if (_.isPlainObject(partner)) {
    //         app.collection('kernel.partners').patch({_id: partner._id}, {
    //             userAccountId: null
    //         }, {
    //             disableSyncUser: true
    //         });
    //     }
    //
    //     return context;
    // }

    if (_.isPlainObject(partner)) {
        app.collection('kernel.partners').patch(
            {_id: partner._id},
            {
                name: result.name,
                email: result.email,
                languageId: result.languageId,
                avatar: result.avatar
            },
            {
                disableSyncUser: true
            }
        );
    }

    return context;
}

function removeMappedRecordPermissions(context) {
    const users = Array.isArray(context.result) ? context.result : [context.result];

    for (const user of users) {
        const mappedRecordPermissions = context.app.get('mappedRecordPermissions') || {};

        mappedRecordPermissions[user._id] = null;

        context.app.set('mappedRecordPermissions', mappedRecordPermissions);
    }

    return context;
}

async function deleteMobileHash(context) {
    const app = context.app;
    const users = Array.isArray(context.result) ? context.result : [context.result];

    for (const user of users) {
        await app.cache.set(`mobile-user-hash-${user._id}`, null);
    }

    return context;
}
