export default {
    name: 'copied-document-records',
    softDelete: false,
    timestamps: false,
    sync: false,
    noCache: true,
    schema: {
        partnerId: {type: 'string', index: true},
        sourceDocumentId: {type: 'string', index: true},
        sourceDocumentCollection: {type: 'string', index: true},
        sourceDocumentCollectionLabel: {type: 'string', index: true},
        sourceDocumentView: {type: 'string', index: true},
        sourceDocumentCode: {type: 'string', index: true},
        destinationDocumentId: {type: 'string', index: true},
        destinationDocumentCollection: {type: 'string', index: true},
        destinationDocumentCollectionLabel: {type: 'string', index: true},
        destinationDocumentView: {type: 'string', index: true},
        destinationDocumentCode: {type: 'string', index: true},
        date: {type: 'datetime', index: true}
    },
    publish(app, data, context) {
        return false;
    }
};
