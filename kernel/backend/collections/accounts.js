export default {
    name: 'accounts',
    title: 'Accounting Accounts',
    tree: true,
    // cache: true,
    softDelete: false,
    schema: {
        code: {
            type: 'string',
            label: 'Code',
            unique: true
        },
        name: {
            type: 'string',
            label: 'Name'
        },
        type: {
            type: 'string',
            label: 'Type',
            allowed: [
                'assets',
                'liabilities',
                'equity',
                'sales',
                'costOfSales',
                'operatingExpenses',
                'financialExpenses',
                'extraordinaryRevenuesAndExpenses'
            ]
        },
        currencyId: {
            type: 'string',
            label: 'Currency'
        },
        scope: {
            type: 'string',
            label: 'Scope',
            required: false
        },
        reconciliation: {
            type: 'boolean',
            label: 'Allow reconciliation',
            default: false
        },
        regulatory: {
            type: 'boolean',
            label: 'Regulatory account',
            default: false
        },
        inverseExpense: {
            type: 'boolean',
            label: 'Inverse expense',
            default: false
        },
        isPartnerAccount: {
            type: 'boolean',
            label: 'Is partner account',
            default: false
        },
        system: {
            type: 'boolean',
            default: false
        }
    },
    attributes: {
        currency: {
            collection: 'kernel.currencies',
            parentField: 'currencyId',
            childField: '_id',
            auto: true
        }
    }
};
