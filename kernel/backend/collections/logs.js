import _ from 'lodash';

export default {
    name: 'logs',
    softDelete: false,
    timestamps: false,
    sync: false,
    noCache: true,
    schema: {
        level: {
            type: 'string',
            default: 'info',
            allowed: ['success', 'error', 'info', 'warning'],
            index: true
        },
        message: {
            type: 'string',
            required: false,
            index: true
        },
        date: {
            type: 'datetime',
            index: true
        },
        userId: {
            type: 'string',
            required: false,
            index: true
        },
        user: {
            name: {
                type: 'string',
                default: 'System',
                index: true
            },
            email: {
                type: 'string',
                default: '',
                index: true
            }
        },
        method: {
            type: 'string',
            required: false,
            index: true
        },
        collection: {
            type: 'string',
            required: false,
            index: true
        },
        collectionName: {
            type: 'string',
            required: false,
            index: true
        },
        documentId: {
            type: 'string',
            required: false,
            index: true
        },
        documentIdentifier: {
            type: 'string',
            required: false,
            index: true
        },
        props: {
            type: 'object',
            required: false,
            blackbox: true
        }
    },
    publish(app, data, context) {
        // return app
        //     .channel('authenticated')
        //     .filter(connection => _.isPlainObject(connection.user) && !!connection.user.isRoot);
        return false;
    }
};
