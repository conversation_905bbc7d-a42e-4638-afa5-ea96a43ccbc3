import _ from 'lodash';

export default {
    name: 'user-scopes',
    timestamps: false,
    uid: false,
    softDelete: false,
    cache: true,
    schema: {
        scopeId: {
            type: 'string',
            index: true
        },
        userId: {
            type: 'string',
            index: true
        },
        title: 'string',
        viewMode: 'string',
        query: 'string',
        stringQueries: {
            type: ['string'],
            default: []
        },
        groups: 'string'
    },
    publish(app, data, context) {
        if (data.userId) {
            return app
                .channel('authenticated')
                .filter(connection => _.isObject(connection.user) && connection.user._id === data.userId);
        }
    }
};
