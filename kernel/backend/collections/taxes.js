export default {
    name: 'taxes',
    title: 'Taxes',
    order: true,
    cache: true,
    localCache: true,
    schema: {
        name: {
            type: 'string',
            label: 'Name'
        },
        scope: {
            type: 'string',
            label: 'Scope',
            required: false
        },
        computation: {
            type: 'string',
            label: 'Tax computation',
            allowed: ['percent', 'fixed', 'group'],
            default: 'percent'
        },
        amount: {
            type: 'decimal',
            label: 'Amount',
            default: 0,
            min: 0
        },
        accountId: {
            type: 'string',
            label: 'Tax account',
            required: false
        },
        creditNoteAccountId: {
            type: 'string',
            label: 'Tax account on credit notes',
            required: false
        },
        label: {
            type: 'string',
            label: 'Label on invoices',
            required: false
        },
        groupId: {
            type: 'string',
            label: 'Tax group'
        },
        isIncludedInPrice: {
            type: 'boolean',
            label: 'Included in price',
            default: false
        },
        affectBaseOfSubsequentTaxes: {
            type: 'boolean',
            label: 'Affect base of subsequent taxes',
            default: false
        },
        doesntAffectedByPreviousTaxes: {
            type: 'boolean',
            label: "Doesn't affected by previous taxes",
            default: false
        },
        isDeduction: {
            type: 'boolean',
            label: 'Is deduction',
            default: false
        },
        isStoppage: {
            type: 'boolean',
            label: 'Is stoppage',
            default: false
        },
        applyToPrevious: {
            type: 'boolean',
            label: 'Apply to previous',
            default: false
        },
        useQuantity: {
            type: 'boolean',
            label: 'Use quantity',
            default: false
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true
        },

        subTaxIds: {
            type: ['string'],
            required: false
        }
    }
};
