export default {
    name: 'partner-groups',
    title: 'Partner Groups',
    order: true,
    cache: true,
    localCache: true,
    schema: {
        type: {
            type: 'string',
            label: 'Type',
            allowed: ['customer', 'vendor', 'employee']
        },
        code: {
            type: 'string',
            label: 'Code',
            required: false
        },
        name: {
            type: 'string',
            label: 'Name'
        },
        system: {
            type: 'boolean',
            default: false
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true
        }
    }
};
