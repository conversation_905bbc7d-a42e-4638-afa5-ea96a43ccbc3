export default {
    name: 'revisions',
    timestamps: false,
    uid: false,
    softDelete: false,
    sync: false,
    noCache: true,
    schema: {
        collection: {
            type: 'string',
            label: 'Collection',
            index: true
        },
        documentId: {
            type: 'string',
            label: 'Document',
            index: true
        },
        order: {
            type: 'integer',
            default: 0,
            index: true
        },
        name: {
            type: 'string',
            label: 'Name',
            index: true
        },
        reason: {
            type: 'string',
            label: 'Reason'
        },
        userId: {
            type: 'string',
            label: 'User',
            index: true
        },
        date: {
            type: 'datetime',
            label: 'Date',
            default: 'date:now',
            index: true
        },
        document: {
            type: 'object',
            blackbox: true,
            required: false
        }
    },
    attributes: {
        user: {
            collection: 'kernel.users',
            parentField: 'userId',
            childField: '_id'
        }
    }
};
