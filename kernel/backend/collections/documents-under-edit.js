export default {
    name: 'documents-under-edit',
    timestamps: false,
    uid: false,
    softDelete: false,
    sync: false,
    noCache: true,
    schema: {
        date: {
            type: 'datetime',
            index: true
        },
        document: {
            type: 'string',
            index: true
        },
        documentId: {
            type: 'string',
            index: true
        },
        userId: {
            type: 'string',
            index: true
        },
        userCode: {
            type: 'string',
            index: true
        },
        userName: {
            type: 'string',
            index: true
        }
    },
    publish(app, data, context) {
        return false;
    }
};
