export default {
    name: 'migration-history',
    timestamps: false,
    uid: false,
    softDelete: false,
    sync: false,
    noCache: true,
    schema: {
        name: {
            type: 'string',
            index: true
        },
        programName: {
            type: 'string',
            index: true
        },
        programTitle: {
            type: 'string',
            index: true
        },
        version: {
            type: 'string',
            index: true
        },
        date: {
            type: 'datetime',
            index: true
        }
    },
    publish(app, data, context) {
        return false;
    }
};
