import _ from 'lodash';
import {ObjectId} from 'mongodb';

export default {
    name: 'branches',
    title: 'Branch Offices',
    order: true,
    localCache: true,
    cache: true,
    schema: {
        companyId: {
            type: 'string',
            label: 'Company',
            required: false
        },
        code: {
            type: 'string',
            label: 'Code',
            unique: true
        },
        name: {
            type: 'string',
            label: 'Name',
            unique: true
        },
        email: {
            type: 'string',
            label: 'Email address',
            regexp: 'EmailWithTLD',
            required: false
        },
        website: {
            type: 'string',
            label: 'Website',
            required: false
        },
        eInvoiceNumberingId: {
            type: 'string',
            label: 'E-Invoice numbering',
            required: false
        },
        eArchiveInvoiceNumberingId: {
            type: 'string',
            label: 'E-Archive invoice numbering',
            required: false
        },
        eWaybillNumberingId: {
            type: 'string',
            label: 'E-Waybill numbering',
            required: false
        },
        isScopesActive: {
            type: 'boolean',
            label: 'Scopes',
            default: false
        },
        scope1Rate: {
            type: 'integer',
            label: 'Scope 1 rare',
            default: 100,
            min: 0,
            max: 100
        },
        scope2Rate: {
            type: 'integer',
            label: 'Scope 2 rare',
            default: 0,
            min: 0,
            max: 100
        },
        scopePriority: {
            type: 'string',
            label: 'Scope priority',
            default: 'none'
        },
        scopeEvaluation: {
            type: 'string',
            label: ' Evaluation',
            default: 'optimum'
        },
        scopeReference: {
            type: 'string',
            label: 'Reference',
            default: 'product-cost'
        },
        phone: {
            type: 'string',
            label: 'Phone',
            required: false
        },
        phoneCountryCode: {
            type: 'string',
            label: 'Phone country code',
            required: false
        },
        phoneNumbers: {
            type: [
                {
                    type: 'object',
                    blackbox: true
                }
            ],
            label: 'Phone Numbers',
            default: []
        },
        address: {
            type: 'object',
            blackbox: true,
            required: false
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true
        },
        system: {
            type: 'boolean',
            default: false
        },
        webServices: {
            type: 'boolean',
            label: 'Web services',
            default: true
        },
        images: {
            type: ['string'],
            label: 'Images',
            default: []
        }
    },
    hooks: {
        before: {
            create: [setDefaults],
            update: [],
            patch: [],
            remove: []
        },
        after: {
            create: [setPrimaryPhone],
            update: [setPrimaryPhone],
            patch: [setPrimaryPhone],
            remove: []
        }
    }
};

async function setDefaults(context) {
    const app = context.app;

    return context;
}

async function setPrimaryPhone(context) {
    const app = context.app;
    const company = await app.collection('kernel.company').findOne({$select: ['address.countryId']});
    const defaultCountry = await app.collection('kernel.countries').findOne({
        _id: company.address.countryId,
        $select: ['code', 'phoneCode']
    });

    for (const result of Array.isArray(context.result) ? context.result : [context.result]) {
        if (Array.isArray(result.phoneNumbers) && result.phoneNumbers.length > 0) {
            const phoneCountryCode = result.phoneNumbers[0].countryCode || defaultCountry.code;
            const phoneCode = result.phoneNumbers[0].phoneCode || defaultCountry.phoneCode;
            const phone = `${phoneCode} ${result.phoneNumbers[0].number}`;

            await context.app.db.collection('kernel_branches').updateOne(
                {_id: new ObjectId(result._id)},
                {
                    $set: {
                        phone,
                        phoneCountryCode
                    }
                },
                {
                    collation: {locale: context.app.config('app.locale')}
                }
            );

            result.phone = phone;
            result.phoneCountryCode = phoneCountryCode;
        }
    }

    try {
        app.memoryCache.clear(`aggregation-caches-kernel.branches`);
        app.memoryCache.clear(`records-caches-kernel.branches`);
    } catch (e) {
        console.log(e);
    }

    return context;
}
