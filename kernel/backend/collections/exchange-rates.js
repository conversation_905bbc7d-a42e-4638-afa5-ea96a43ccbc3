export default {
    name: 'exchange-rates',
    timestamps: false,
    uid: false,
    softDelete: false,
    sync: false,
    noCache: true,
    schema: {
        currencyId: {
            type: 'string',
            index: true
        },
        currencyName: {
            type: 'string',
            label: 'Currency',
            index: true
        },
        date: {
            type: 'date',
            label: 'Date',
            index: true
        },
        rate: {
            type: 'decimal',
            label: 'Rate',
            required: false
        },
        forexBuying: {
            type: 'decimal',
            label: 'Forex Buying',
            required: false
        },
        forexSelling: {
            type: 'decimal',
            label: 'Forex Selling',
            required: false
        },
        banknoteBuying: {
            type: 'decimal',
            label: 'Banknote Buying',
            required: false
        },
        banknoteSelling: {
            type: 'decimal',
            label: 'Banknote Selling',
            required: false
        }
    },
    publish(app, data, context) {
        return false;
    }
};
