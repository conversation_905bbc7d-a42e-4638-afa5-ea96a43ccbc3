import _ from 'lodash';

export default {
    name: 'working-hours',
    title: 'Working Hours',
    order: true,
    cache: true,
    schema: {
        code: {
            type: 'string',
            label: 'Code',
            unique: true
        },
        name: {
            type: 'string',
            label: 'Name'
        },
        type: {
            type: 'string',
            label: 'Type',
            default: 'general'
        },
        timezone: {
            type: 'string',
            label: 'Timezone'
        },
        system: {
            type: 'boolean',
            default: false
        },
        totalWorking: {
            hours: {
                type: 'integer',
                label: 'Total working hours',
                default: 0
            },
            minutes: {
                type: 'integer',
                label: 'Total working minutes',
                default: 0
            }
        },
        averageWorking: {
            hours: {
                type: 'integer',
                label: 'Average working hours',
                default: 0
            },
            minutes: {
                type: 'integer',
                label: 'Average working minutes',
                default: 0
            }
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true
        },
        hours: {
            type: [
                {
                    name: {
                        type: 'string',
                        label: 'Name'
                    },
                    dayOfWeek: {
                        type: 'string',
                        label: 'Day of week'
                    },
                    type: {
                        type: 'string',
                        label: 'Type'
                    },
                    workFrom: {
                        type: 'time',
                        label: 'Work from'
                    },
                    workTo: {
                        type: 'time',
                        label: 'Work to'
                    },
                    dayPeriod: {
                        type: 'string',
                        label: 'Day period'
                    },
                    startDate: {
                        type: 'date',
                        label: 'Start date',
                        required: false
                    },
                    endDate: {
                        type: 'date',
                        label: 'End date',
                        required: false
                    }
                }
            ],
            default: []
        }
    }
};
