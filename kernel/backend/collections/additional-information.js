export default {
    name: 'additional-information',
    title: 'Additional Information',
    order: true,
    timestamps: false,
    uid: false,
    cache: true,
    localCache: true,
    schema: {
        code: {
            type: 'string',
            label: 'Code',
            unique: true
        },
        name: {
            type: 'string',
            label: 'Name'
        },
        type: {
            type: 'string',
            label: 'Type',
            unique: true
        },
        groupingType: {
            type: 'string',
            label: 'Grouping type',
            default: 'none'
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true
        },
        system: {
            type: 'boolean',
            default: false
        },
        fields: {
            type: [
                {
                    code: {
                        type: 'string',
                        label: 'Code'
                    },
                    label: {
                        type: 'string',
                        label: 'Label'
                    },
                    alignment: {
                        type: 'string',
                        label: 'Alignment',
                        default: 'left'
                    },
                    fieldType: {
                        type: 'string',
                        label: 'Field type'
                    },
                    isRequired: {
                        type: 'boolean',
                        label: 'Is required',
                        default: true
                    },
                    defaultValue: {
                        type: 'all',
                        required: false
                    },
                    selectOptions: [
                        {
                            value: {
                                type: 'string',
                                label: 'Value',
                                required: false
                            }
                        }
                    ],
                    group: {
                        type: 'string',
                        label: 'Group',
                        required: false
                    }
                }
            ],
            default: []
        }
    }
};
