import _ from 'lodash';

export default {
    name: 'copyable-items',
    softDelete: false,
    timestamps: false,
    sync: false,
    noCache: true,
    schema: {
        type: {
            type: 'string',
            allowed: [
                'sale-quotation',
                'sale-order',
                'customer-invoice',
                'customer-return-invoice',
                'purchase-order',
                'vendor-invoice',
                'vendor-return-invoice',
                'goods-receipt',
                'return-goods-receipt',
                'delivery-order',
                'return-delivery-order'
            ],
            index: true
        },
        documentId: {type: 'string', index: true},
        documentCode: {type: 'string'},
        documentNo: {type: 'string', required: false},
        documentCollection: {type: 'string'},
        documentView: {type: 'string'},
        documentTitle: {type: 'string'},
        partnerId: {type: 'string', index: true},
        partnerCode: {type: 'string', index: true},
        partnerName: {type: 'string', index: true},
        productId: {type: 'string'},
        productCode: {type: 'string'},
        productDefinition: {type: 'string'},
        barcode: {type: 'string', required: false},
        unitId: {type: 'string'},
        unitName: {type: 'string'},
        baseUnitId: {type: 'string'},
        baseUnitName: {type: 'string'},
        quantity: {type: 'decimal'},
        remainingQuantity: {type: 'decimal', index: true},
        unitRatio: {type: 'decimal'},
        branchId: {type: 'string'},
        currencyId: {type: 'string'},
        currencyRate: {type: 'decimal'},
        unitPrice: {type: 'decimal'},
        discount: {type: 'decimal'},
        taxIds: {type: ['string']},
        issueDate: {type: 'date'},
        dueDate: {type: 'date', required: false},
        usages: {
            type: [
                {
                    documentId: {type: 'string'},
                    documentCollection: {type: 'string'},
                    quantity: {type: 'decimal'}
                }
            ],
            default: []
        }
    },
    async searchTerms(document) {
        const values = Object.values(
            _.pick(document, ['documentCode', 'partnerCode', 'partnerName', 'productCode', 'productDefinition'])
        );

        return values;
    }
};
