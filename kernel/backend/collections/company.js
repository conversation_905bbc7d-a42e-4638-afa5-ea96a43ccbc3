import _ from 'lodash';
import {ObjectId} from 'mongodb';

export default {
    name: 'company',
    timestamps: false,
    uid: false,
    localCache: true,
    cache: true,
    schema: {
        name: {
            type: 'string',
            label: 'Name'
        },
        tagline: {
            type: 'string',
            label: 'Tagline',
            required: false
        },
        email: {
            type: 'string',
            label: 'Email address',
            regexp: 'EmailWithTLD'
        },
        languageId: {
            type: 'string',
            label: 'Language'
        },
        currencyId: {
            type: 'string',
            label: 'Currency'
        },
        website: {
            type: 'string',
            label: 'Website',
            required: false
        },
        workingHoursId: {
            type: 'string',
            label: 'Working hours'
        },

        legalName: {
            type: 'string',
            label: 'Legal name',
            required: false
        },
        tin: {
            type: 'string',
            label: 'TIN'
        },
        taxDepartment: {
            type: 'string',
            label: 'Tax department',
            required: false
        },
        firstName: {
            type: 'string',
            label: 'First name',
            required: false
        },
        lastName: {
            type: 'string',
            label: 'Last name',
            required: false
        },
        identity: {
            type: 'string',
            label: 'Identity no',
            required: false
        },

        scope1Rate: {
            type: 'integer',
            label: 'Scope 1 rare',
            default: 100,
            min: 0,
            max: 100
        },
        scope2Rate: {
            type: 'integer',
            label: 'Scope 2 rare',
            default: 0,
            min: 0,
            max: 100
        },
        scopePriority: {
            type: 'string',
            label: 'Priority',
            default: 'none'
        },
        scopeEvaluation: {
            type: 'string',
            label: 'Evaluation',
            default: 'optimum'
        },
        scopeReference: {
            type: 'string',
            label: 'Scope reference',
            default: 'product-cost'
        },

        phone: {
            type: 'string',
            label: 'Phone',
            required: false
        },
        phoneCountryCode: {
            type: 'string',
            label: 'Phone country code',
            required: false
        },
        phoneNumbers: {
            type: [
                {
                    type: 'object',
                    blackbox: true
                }
            ],
            label: 'Phone Numbers',
            default: []
        },

        address: {
            type: 'object',
            blackbox: true,
            required: false
        },

        branchIds: {
            type: ['string'],
            required: false
        },

        logo: {
            type: 'string',
            label: 'Company Logo',
            required: false
        },

        subCompanies: {
            type: [
                {
                    id: {
                        type: 'string',
                        label: 'ID',
                        required: false
                    },
                    name: {
                        type: 'string',
                        label: 'Name'
                    },
                    tagline: {
                        type: 'string',
                        label: 'Tagline',
                        required: false
                    },
                    email: {
                        type: 'string',
                        label: 'Email address',
                        regexp: 'EmailWithTLD'
                    },
                    languageId: {
                        type: 'string',
                        label: 'Language'
                    },
                    currencyId: {
                        type: 'string',
                        label: 'Currency'
                    },
                    website: {
                        type: 'string',
                        label: 'Website',
                        required: false
                    },
                    workingHoursId: {
                        type: 'string',
                        label: 'Working hours'
                    },
                    legalName: {
                        type: 'string',
                        label: 'Legal name',
                        required: false
                    },
                    tin: {
                        type: 'string',
                        label: 'TIN'
                    },
                    taxDepartment: {
                        type: 'string',
                        label: 'Tax department',
                        required: false
                    },
                    firstName: {
                        type: 'string',
                        label: 'First name',
                        required: false
                    },
                    lastName: {
                        type: 'string',
                        label: 'Last name',
                        required: false
                    },
                    identity: {
                        type: 'string',
                        label: 'Identity no',
                        required: false
                    },
                    eInvoiceUsername: {
                        type: 'string',
                        label: 'Username',
                        required: false
                    },
                    eInvoicePassword: {
                        type: 'string',
                        label: 'Password',
                        required: false
                    },
                    eInvoiceSULabel: {
                        type: 'string',
                        label: 'Sender unit label',
                        required: false
                    },
                    eInvoicePBLabel: {
                        type: 'string',
                        label: 'Postbox label',
                        required: false
                    },
                    eInvoiceTemplateIds: {
                        type: ['string'],
                        label: 'Templates',
                        required: []
                    },
                    eWaybillUsername: {
                        type: 'string',
                        label: 'Username',
                        required: false
                    },
                    eWaybillPassword: {
                        type: 'string',
                        label: 'Password',
                        required: false
                    },
                    eWaybillSULabel: {
                        type: 'string',
                        label: 'Sender unit label',
                        required: false
                    },
                    eWaybillPBLabel: {
                        type: 'string',
                        label: 'Postbox label',
                        required: false
                    },
                    eWaybillTemplateIds: {
                        type: ['string'],
                        label: 'Templates',
                        required: []
                    },
                    mersisNo: {
                        type: 'string',
                        label: 'Mersis no',
                        required: false
                    },
                    ticaretSicilNo: {
                        type: 'string',
                        label: 'Ticaret sicil no',
                        required: false
                    },
                    phone: {
                        type: 'string',
                        label: 'Phone',
                        required: false
                    },
                    phoneCountryCode: {
                        type: 'string',
                        label: 'Phone country code',
                        required: false
                    },
                    phoneNumbers: {
                        type: [
                            {
                                type: 'object',
                                blackbox: true
                            }
                        ],
                        label: 'Phone Numbers',
                        default: []
                    },
                    address: {
                        type: 'object',
                        blackbox: true,
                        required: false
                    },
                    logo: {
                        type: 'string',
                        label: 'Company Logo',
                        required: false
                    }
                }
            ],
            default: []
        }
    },
    attributes: {
        language: {
            collection: 'kernel.languages',
            parentField: 'languageId',
            childField: '_id',
            auto: true
        },
        currency: {
            collection: 'kernel.currencies',
            parentField: 'currencyId',
            childField: '_id',
            auto: true
        },
        country: {
            collection: 'kernel.countries',
            parentField: 'address.countryId',
            childField: '_id',
            auto: true
        }
    },
    hooks: {
        before: {
            create: [setDefaults],
            update: [],
            patch: [],
            remove: []
        },
        after: {
            create: [setPrimaryPhone, deleteMobileHash],
            update: [setPrimaryPhone, deleteMobileHash],
            patch: [setPrimaryPhone, deleteMobileHash],
            remove: [deleteMobileHash]
        }
    }
};

async function setDefaults(context) {
    const app = context.app;

    return context;
}

async function setPrimaryPhone(context) {
    const app = context.app;
    const company = await app.collection('kernel.company').findOne({$select: ['address.countryId']});
    const defaultCountry = await app.collection('kernel.countries').findOne({
        _id: company.address.countryId,
        $select: ['code', 'phoneCode']
    });

    for (const result of Array.isArray(context.result) ? context.result : [context.result]) {
        if (Array.isArray(result.phoneNumbers) && result.phoneNumbers.length > 0) {
            const phoneCountryCode = result.phoneNumbers[0].countryCode || defaultCountry.code;
            const phoneCode = result.phoneNumbers[0].phoneCode || defaultCountry.phoneCode;
            const phone = `${phoneCode} ${result.phoneNumbers[0].number}`;

            await context.app.db.collection('kernel_company').updateOne(
                {_id: new ObjectId(result._id)},
                {
                    $set: {
                        phone,
                        phoneCountryCode
                    }
                },
                {
                    collation: {locale: context.app.config('app.locale')}
                }
            );

            result.phone = phone;
            result.phoneCountryCode = phoneCountryCode;
        }
    }

    try {
        app.memoryCache.clear(`aggregation-caches-kernel.company`);
        app.memoryCache.clear(`records-caches-kernel.company`);
    } catch (e) {
        console.log(e);
    }

    return context;
}

async function deleteMobileHash(context) {
    const app = context.app;

    await app.cache.set('mobile-company-hash', null);

    return context;
}
