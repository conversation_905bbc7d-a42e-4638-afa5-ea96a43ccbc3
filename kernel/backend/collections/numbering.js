import _ from 'lodash';

export default {
    name: 'numbering',
    title: 'Numbering',
    noCache: true,
    schema: {
        name: {
            type: 'string',
            label: 'Name'
        },
        code: {
            type: 'string',
            label: 'Code',
            required: false,
            index: true
        },
        prefix: {
            type: 'string',
            label: 'Prefix',
            required: false
        },
        suffix: {
            type: 'string',
            label: 'Suffix',
            required: false
        },
        separator: {
            type: 'string',
            label: 'Separator',
            required: false
        },
        useNumberingVolumes: {
            type: 'boolean',
            label: 'Use numbering volumes',
            default: false
        },
        includeDay: {
            type: 'boolean',
            label: 'Include day',
            default: false
        },
        includeMonth: {
            type: 'boolean',
            label: 'Include month',
            default: false
        },
        includeYear: {
            type: 'boolean',
            label: 'Include year',
            default: false
        },
        leadingZeros: {
            type: 'integer',
            label: 'Leading zeros',
            default: 0
        },
        refresh: {
            type: 'string',
            label: 'Refresh',
            allowed: ['daily', 'monthly', 'yearly'],
            required: false
        },
        nextNumber: {
            type: 'integer',
            label: 'Next number',
            default: 1,
            index: true
        },
        lastNumber: {
            type: 'integer',
            label: 'Last number',
            required: false,
            index: true
        },
        inUse: {
            type: 'boolean',
            label: 'In use',
            default: false,
            index: true
        },
        system: {
            type: 'boolean',
            label: 'System',
            default: false,
            index: true
        },
        numberingVolumes: {
            type: [
                {
                    type: 'object',
                    blackbox: true,
                    required: false
                }
            ],
            default: []
        }
    },
    hooks: {
        before: {
            find(context) {
                if (!_.isObject(context.params.query)) context.params.query = {};

                if (context.params.disableInUseCheck || !context.params.provider) {
                    delete context.params.disableInUseCheck;

                    return context;
                }

                if (context.params.query.$disableInUseCheck || !context.params.provider) {
                    delete context.params.query.$disableInUseCheck;

                    return context;
                }

                if (_.isString(context.params.query._id)) {
                    return context;
                }

                context.params.query.inUse = false;

                return context;
            }
        }
    },
    publish(app, data, context) {
        return false;
    }
};
