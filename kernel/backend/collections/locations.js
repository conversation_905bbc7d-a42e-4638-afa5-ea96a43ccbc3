export default {
    name: 'locations',
    title: 'Country Locations',
    timestamps: false,
    uid: false,
    schema: {
        countryId: {
            type: 'string',
            label: 'Country',
            index: true
        },
        code: {
            type: 'string',
            label: 'Code',
            index: true,
            required: false
        },
        state: {
            type: 'string',
            label: 'State',
            index: true,
            required: false
        },
        city: {
            type: 'string',
            label: 'City',
            index: true,
            required: false
        },
        district: {
            type: 'string',
            label: 'District',
            index: true,
            required: false
        },
        subDistrict: {
            type: 'string',
            label: 'Sub-District',
            index: true,
            required: false
        },
        postalCode: {
            type: 'string',
            label: 'Postal code',
            index: true,
            required: false
        }
    }
};
