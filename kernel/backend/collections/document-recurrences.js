import _ from 'lodash';

export default {
    name: 'document-recurrences',
    title: 'Document Recurrences',
    softDelete: false,
    sync: false,
    noCache: true,
    schema: {
        documentType: {
            type: 'string',
            label: 'Document type',
            index: true
        },
        documentTypeName: {
            type: 'string',
            label: 'Document type name',
            required: false
        },
        documentId: {
            type: 'string',
            label: 'Document',
            index: true
        },
        documentCode: {
            type: 'string',
            label: 'Document code',
            required: false,
            index: true
        },
        documentView: {
            type: 'string',
            label: 'Document view',
            required: false
        },
        description: {
            type: 'string',
            label: 'Description',
            required: false,
            index: true
        },
        startDate: {
            type: 'date',
            label: 'Start date',
            default: 'date:now',
            index: true
        },
        endDate: {
            type: 'date',
            label: 'End date',
            index: true
        },
        repeat: {
            type: 'string',
            label: 'Repeat',
            index: true
        },
        repeatStr: {
            type: 'string',
            label: 'Repeat',
            required: false
        },
        repeatData: {
            type: 'object',
            blackbox: true,
            required: false
        },
        recurrences: {
            type: 'integer',
            label: 'Recurrences',
            required: false
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true,
            index: true
        }
    },
    async searchTerms(document) {
        return Object.values(_.pick(document, ['documentTypeName', 'documentCode', 'description']));
    }
};
