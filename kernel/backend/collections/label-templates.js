export default {
    name: 'label-templates',
    title: 'Label Templates',
    schema: {
        code: {
            type: 'string',
            label: 'Code'
        },
        name: {
            type: 'string',
            label: 'Name'
        },
        unitOfMeasurement: {
            type: 'string',
            label: 'Unit of measurement',
            allowed: ['cm', 'mm', 'px'],
            default: 'cm'
        },
        printDirection: {
            type: 'string',
            label: 'Print direction',
            allowed: ['horizontal', 'vertical'],
            default: 'horizontal'
        },
        dataSource: {
            type: 'string',
            label: 'Data source',
            required: false
        },
        targetDocument: {
            type: 'string',
            label: 'Target document',
            required: false,
            index: true
        },
        documentIdentifierField: {
            type: 'string',
            label: 'Document identifier field',
            required: false
        },
        multiplierField: {
            type: 'string',
            label: 'Multiplier field',
            required: false
        },
        paperType: {
            type: 'string',
            label: 'Paper type',
            allowed: ['A4', 'A5', 'custom'],
            default: 'A4'
        },
        paperLayout: {
            type: 'string',
            label: 'Paper layout',
            allowed: ['portrait', 'landscape'],
            default: 'portrait'
        },
        paperWidth: {
            type: 'decimal',
            label: 'Paper width',
            default: 21.0
        },
        paperHeight: {
            type: 'decimal',
            label: 'Paper height',
            default: 29.7
        },
        paperMarginLeft: {
            type: 'decimal',
            label: 'Margin left',
            default: 0
        },
        paperMarginTop: {
            type: 'decimal',
            label: 'Margin top',
            default: 0
        },
        paperMarginRight: {
            type: 'decimal',
            label: 'Margin right',
            default: 0
        },
        paperMarginBottom: {
            type: 'decimal',
            label: 'Margin bottom',
            default: 0
        },
        labelWidth: {
            type: 'decimal',
            label: 'Label width',
            default: 21.0
        },
        labelHeight: {
            type: 'decimal',
            label: 'Label height',
            default: 29.7
        },
        horizontalQuantity: {
            type: 'integer',
            label: 'Horizontal quantity',
            default: 1
        },
        verticalQuantity: {
            type: 'integer',
            label: 'Vertical quantity',
            default: 1
        },
        horizontalMargin: {
            type: 'decimal',
            label: 'Horizontal margin',
            default: 0
        },
        verticalMargin: {
            type: 'decimal',
            label: 'Vertical margin',
            default: 0
        },
        labelPayload: {
            type: 'object',
            blackbox: true,
            required: false
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true,
            index: true
        }
    }
};
