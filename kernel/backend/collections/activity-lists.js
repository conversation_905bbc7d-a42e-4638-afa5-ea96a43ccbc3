export default {
    name: 'activity-lists',
    title: 'Activity Lists',
    schema: {
        id: {
            type: 'string',
            label: 'ID',
            required: false,
            index: true
        },
        title: {
            type: 'string',
            label: 'Title',
            index: true
        },
        description: {
            type: 'string',
            label: 'Description',
            required: false
        },
        color: {
            type: 'string',
            label: 'Color',
            default: '#2563eb'
        },
        isPublic: {
            type: 'boolean',
            label: 'Is public',
            default: false,
            index: true
        },
        preparedForMe: {
            type: 'boolean',
            label: 'Prepared for me',
            default: true,
            index: true
        },
        userIds: {
            type: ['string'],
            label: 'Users',
            default: [],
            index: true
        },
        userGroupIds: {
            type: ['string'],
            label: 'User groups',
            default: [],
            index: true
        }
    }
};
