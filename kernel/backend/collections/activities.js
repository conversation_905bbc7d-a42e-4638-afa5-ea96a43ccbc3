import _ from 'lodash';

export default {
    name: 'activities',
    title: 'Activities',
    view: 'calendar.activities',
    labelParams: {
        from: 'code'
    },
    locationFrom: 'address',
    extraIndexes: [
        {type: 'normal', key: 'repeatData.start', value: 1},
        {type: 'normal', key: 'params.assignedTo.id', value: 1},
        {type: 'normal', key: 'params.assignedTo.code', value: 1},
        {type: 'normal', key: 'params.assignedTo.name', value: 1}
    ],
    schema: {
        listId: {
            type: 'string',
            label: 'List ID',
            required: false,
            index: true
        },
        status: {
            type: 'string',
            label: 'Status',
            default: 'open',
            allowed: ['open', 'started', 'stopped', 'closed', 'canceled'],
            index: true
        },
        code: {
            type: 'string',
            label: 'Code',
            index: true,
            unique: true
        },
        type: {
            type: 'string',
            label: 'Type',
            default: 'task',
            allowed: ['task', 'meeting', 'appointment', 'phone-call', 'event'],
            index: true
        },
        isImportant: {
            type: 'boolean',
            label: 'Important',
            default: false
        },
        color: {
            type: 'string',
            label: 'Color',
            allowed: ['blue', 'green', 'orange', 'purple', 'pink']
        },
        subject: {
            type: 'string',
            label: 'Subject',
            required: false,
            index: true
        },
        isPersonal: {
            type: 'boolean',
            label: 'Is personal',
            default: false,
            index: true
        },
        assignedToType: {
            type: 'string',
            label: 'Assigned type',
            required: false,
            index: true
        },
        assignedToUserIds: {
            type: ['string'],
            label: 'Assigned to',
            default: [],
            index: true
        },
        assignedToEmployeeIds: {
            type: ['string'],
            label: 'Assigned to',
            default: [],
            index: true
        },
        assignedToRecipientListId: {
            type: 'string',
            label: 'Assigned to',
            required: false,
            index: true
        },
        assignedById: {
            type: 'string',
            label: 'Assigned by',
            index: true
        },
        meetingLocationId: {
            type: 'string',
            label: 'Meeting location',
            required: false,
            index: true
        },
        priority: {
            type: 'string',
            label: 'Priority',
            allowed: ['not-urgent', 'normal', 'urgent', 'very-urgent'],
            default: 'normal',
            index: true
        },
        description: {
            type: 'string',
            label: 'Description',
            required: false
        },
        phoneCallType: {
            type: 'string',
            label: 'Phone call type',
            default: 'completed',
            index: true
        },
        phoneCallDirection: {
            type: 'string',
            label: 'Phone call direction',
            default: 'outbound',
            index: true
        },
        phoneCallStartTime: {
            type: 'datetime',
            label: 'Phone call start time',
            default: 'date:now',
            index: true
        },
        phoneCallDurationMinutes: {
            type: 'integer',
            label: 'Phone call duration minutes',
            min: 0,
            default: 0,
            index: true
        },
        phoneCallDurationSeconds: {
            type: 'integer',
            label: 'Phone call duration seconds',
            min: 0,
            default: 0,
            index: true
        },
        phoneCallResult: {
            type: 'string',
            label: 'Phone call result',
            required: false,
            index: true
        },
        allDay: {
            type: 'boolean',
            label: 'All day',
            default: false,
            index: true
        },
        startDate: {
            type: 'datetime',
            label: 'Start date',
            index: true
        },
        endDate: {
            type: 'datetime',
            label: 'End date',
            index: true
        },
        isScheduled: {
            type: 'boolean',
            label: 'Scheduled',
            default: false,
            index: true
        },
        endDateCalculationData: {
            type: 'object',
            blackbox: true,
            required: false
        },
        partnerType: {
            type: 'string',
            label: 'Partner type',
            required: false,
            index: true
        },
        partnerId: {
            type: 'string',
            label: 'Partner',
            required: false,
            index: true
        },
        leadId: {
            type: 'string',
            label: 'Lead',
            required: false,
            index: true
        },
        contactPersonId: {
            type: 'string',
            label: 'Contact person',
            required: false
        },
        addressId: {
            type: 'string',
            label: 'Address',
            required: false
        },
        address: {
            type: 'object',
            blackbox: true,
            required: false
        },
        remind: {
            type: 'string',
            label: 'Remind',
            default: 'at-the-time-of-event',
            index: true
        },
        repeat: {
            type: 'string',
            label: 'Repeat',
            default: 'never',
            index: true
        },
        repeatingEndDate: {
            type: 'datetime',
            label: 'Repeating end date',
            required: false
        },
        repeatStr: {
            type: 'string',
            label: 'Repeat',
            required: false
        },
        repeatData: {
            type: 'object',
            blackbox: true,
            required: false
        },
        isRecurrence: {
            type: 'boolean',
            label: 'Is recurrence',
            required: false
        },
        originalActivityId: {
            type: 'string',
            required: false,
            index: true
        },
        sendInvitation: {
            type: 'boolean',
            label: 'Send invitation',
            default: false
        },
        tags: {
            type: ['string'],
            label: 'Tags',
            required: false,
            index: true
        },
        attendees: {
            type: [
                {
                    role: {
                        type: 'string',
                        label: 'Role',
                        index: true
                    },
                    attendeeId: {
                        type: 'string',
                        index: true
                    },
                    attendance: {
                        type: 'boolean',
                        label: 'Attendance',
                        default: false,
                        index: true
                    }
                }
            ],
            default: []
        },
        linkedDocumentType: {
            type: 'string',
            label: 'Document type',
            required: false,
            index: true
        },
        linkedDocumentId: {
            type: 'string',
            label: 'Document',
            required: false,
            index: true
        },
        note: {
            type: 'string',
            label: 'Note',
            required: false
        },
        attachments: {
            type: ['string'],
            default: []
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true,
            index: true
        },

        // Internal.
        order: {
            type: 'integer',
            default: 0
        },
        notificationJobId: {
            type: 'string',
            required: false,
            index: true
        },
        params: {
            type: 'object',
            blackbox: true,
            required: false
        }
    },
    attributes: {
        list: {
            collection: 'kernel.activity-lists',
            parentField: 'listId',
            childField: '_id'
        },
        assignedBy: {
            collection: 'kernel.users',
            parentField: 'assignedById',
            childField: '_id'
        },
        partner: {
            collection: 'kernel.partners',
            parentField: 'partnerId',
            childField: '_id'
        },
        lead: {
            collection: 'crm.leads',
            parentField: 'leadId',
            childField: '_id'
        },
        contactPerson: {
            collection: 'kernel.contacts',
            parentField: 'contactPersonId',
            childField: '_id'
        },
        meetingLocation: {
            collection: 'kernel.meeting-locations',
            parentField: 'meetingLocationId',
            childField: '_id'
        }
    },
    async searchTerms(document) {
        const values = Object.values(_.pick(document, ['code', 'subject', 'description']));

        if (_.isPlainObject(document.partner)) {
            values.push(document.partner.code);
            values.push(document.partner.name);
        }

        if (_.isPlainObject(document.lead)) {
            values.push(document.lead.code);
            values.push(document.lead.name);
        }

        if (_.isPlainObject(document.contactPerson)) {
            values.push(document.contactPerson.name);
        }

        if (_.isPlainObject(document.assignedBy)) {
            values.push(document.assignedBy.code);
            values.push(document.assignedBy.name);
        }

        if (_.isPlainObject(document.address)) {
            values.push(document.address.address);
        }

        if (_.isPlainObject(document.params) && _.isPlainObject(document.params.assignedTo)) {
            values.push(document.params.assignedTo.code);
            values.push(document.params.assignedTo.name);
        }

        return values;
    },
    hooks: {
        before: {
            create: [],
            update: [],
            patch: [],
            remove: []
        },
        after: {
            create: [clearCaches],
            update: [clearCaches],
            patch: [clearCaches],
            remove: [clearCaches]
        }
    }
};

function clearCaches(context) {
    context.app.cache.delete('activity-task-subjects');
    context.app.cache.delete('activity-meeting-subjects');
    context.app.cache.delete('activity-appointment-subjects');
    context.app.cache.delete('activity-phone-call-subjects');
    context.app.cache.delete('activity-event-subjects');
    context.app.cache.delete('activity-phone-call-results');
    context.app.cache.delete('activity-tags');

    return context;
}
