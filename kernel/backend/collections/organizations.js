export default {
    name: 'organizations',
    title: 'Organizations',
    order: true,
    tree: true,
    localCache: true,
    cache: true,
    schema: {
        code: {
            type: 'string',
            label: 'Code'
        },
        name: {
            type: 'string',
            label: 'Name'
        },
        scope: {
            type: ['string'],
            label: 'Scope',
            required: false
        },
        path: {
            type: 'string',
            default: ''
        },
        note: {
            type: 'string',
            label: 'Note',
            required: false
        },
        team: [
            {
                managerId: {
                    type: 'string',
                    label: 'Manager',
                    required: false
                },
                partnerId: {
                    type: 'string',
                    label: 'Employee'
                },
                positionId: {
                    type: 'string',
                    label: 'Position'
                },
                path: {
                    type: 'string',
                    required: false
                },
                manager: {
                    type: 'object',
                    blackbox: true,
                    required: false
                },
                partner: {
                    type: 'object',
                    blackbox: true,
                    required: false
                },
                position: {
                    type: 'object',
                    blackbox: true,
                    required: false
                },
                settings: {
                    type: 'object',
                    blackbox: true,
                    default: {}
                }
            }
        ],
        settings: {
            type: 'object',
            blackbox: true,
            default: {}
        },
        system: {
            type: 'boolean',
            default: false
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true
        }
    }
};
