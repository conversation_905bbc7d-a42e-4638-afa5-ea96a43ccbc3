import _ from 'lodash';
import {ObjectId} from 'mongodb';

export default {
    name: 'bank-branches',
    title: 'Bank Branch Offices',
    order: true,
    cache: true,
    schema: {
        code: {
            type: 'string',
            label: 'Code'
        },
        name: {
            type: 'string',
            label: 'Name'
        },
        email: {
            type: 'string',
            label: 'Email address',
            regexp: 'EmailWithTLD',
            required: false
        },
        phone: {
            type: 'string',
            label: 'Phone',
            required: false
        },
        phoneCountryCode: {
            type: 'string',
            label: 'Phone code',
            required: false
        },
        phoneNumbers: {
            type: [
                {
                    type: 'object',
                    blackbox: true
                }
            ],
            label: 'Phone Numbers',
            default: []
        },
        address: {
            type: 'object',
            blackbox: true,
            required: false
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true
        }
    },
    hooks: {
        before: {
            create: [setDefaults],
            update: [],
            patch: [],
            remove: []
        },
        after: {
            create: [setPrimaryPhone],
            update: [setPrimaryPhone],
            patch: [setPrimaryPhone],
            remove: []
        }
    }
};

async function setDefaults(context) {
    const app = context.app;

    return context;
}

async function setPrimaryPhone(context) {
    const app = context.app;
    const company = await app.collection('kernel.company').findOne({$select: ['address.countryId']});
    const defaultCountry = await app.collection('kernel.countries').findOne({
        _id: company.address.countryId,
        $select: ['code', 'phoneCode']
    });

    for (const result of Array.isArray(context.result) ? context.result : [context.result]) {
        if (Array.isArray(result.phoneNumbers) && result.phoneNumbers.length > 0) {
            const phoneCountryCode = result.phoneNumbers[0].countryCode || defaultCountry.code;
            const phoneCode = result.phoneNumbers[0].phoneCode || defaultCountry.phoneCode;
            const phone = `${phoneCode} ${result.phoneNumbers[0].number}`;

            await context.app.db.collection('kernel_bank-branches').updateOne(
                {_id: new ObjectId(result._id)},
                {
                    $set: {
                        phone,
                        phoneCountryCode
                    }
                },
                {
                    collation: {locale: context.app.config('app.locale')}
                }
            );

            result.phone = phone;
            result.phoneCountryCode = phoneCountryCode;
        }
    }

    try {
        app.memoryCache.clear(`aggregation-caches-kernel.bank-branches`);
        app.memoryCache.clear(`records-caches-kernel.bank-branches`);
    } catch (e) {
        console.log(e);
    }

    return context;
}
