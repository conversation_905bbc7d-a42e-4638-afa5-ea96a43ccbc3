import _ from 'lodash';

export default {
    name: 'sessions',
    timestamps: false,
    uid: false,
    softDelete: false,
    noCache: true,
    schema: {
        userId: {
            type: 'string',
            required: false,
            index: true
        },
        user: {
            code: {
                type: 'string',
                required: false,
                index: true
            },
            name: {
                type: 'string',
                default: 'System',
                index: true
            },
            email: {
                type: 'string',
                default: '',
                index: true
            }
        },
        startDate: {
            type: 'datetime',
            default: 'date:now',
            index: true
        },
        endDate: {
            type: 'datetime',
            required: false,
            index: true
        },
        lastActiveDate: {
            type: 'datetime',
            required: false,
            index: true
        },
        browserName: {
            type: 'string',
            required: false,
            index: true
        },
        browserVersion: {
            type: 'string',
            required: false,
            index: true
        },
        deviceType: {
            type: 'string',
            required: false,
            index: true
        },
        deviceVendor: {
            type: 'string',
            required: false,
            index: true
        },
        deviceModel: {
            type: 'string',
            required: false,
            index: true
        },
        osName: {
            type: 'string',
            required: false,
            index: true
        },
        osVersion: {
            type: 'string',
            required: false,
            index: true
        },
        isp: {
            type: 'string',
            required: false,
            index: true
        },
        countryCode: {
            type: 'string',
            required: false,
            index: true
        },
        city: {
            type: 'string',
            required: false,
            index: true
        },
        ip: {
            type: 'string',
            required: false,
            index: true
        },
        socketId: {
            type: 'string',
            required: false,
            index: true
        },
        isOnline: {
            type: 'boolean',
            default: true,
            index: true
        }
    },
    publish(app, data, context) {
        return app.channel('authenticated').send({_id: data._id});
    }
};
