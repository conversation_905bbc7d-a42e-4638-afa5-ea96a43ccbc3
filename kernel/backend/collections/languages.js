export default {
    name: 'languages',
    title: 'Languages',
    order: true,
    localCache: true,
    cache: true,
    schema: {
        name: {
            type: 'string',
            label: 'Name',
            translate: true,
            unique: true
        },
        localeCode: {
            type: 'string',
            label: 'Locale code',
            unique: true
        },
        isoCode: {
            type: 'string',
            label: 'ISO code',
            unique: true
        },
        decimalSeparator: {
            type: 'string',
            label: 'Decimal separator'
        },
        thousandsSeparator: {
            type: 'string',
            label: 'Thousands separator'
        },
        dateFormat: {
            type: 'string',
            label: 'Date format'
        },
        timeFormat: {
            type: 'string',
            label: 'Time format'
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true
        }
    }
};
