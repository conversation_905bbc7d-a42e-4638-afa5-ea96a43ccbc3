import _ from 'lodash';

export default {
    name: 'currencies',
    title: 'Currencies',
    order: true,
    localCache: true,
    cache: true,
    schema: {
        name: {
            type: 'string',
            label: 'Name',
            unique: true
        },
        unit: {
            type: 'string',
            label: 'Unit'
        },
        subUnit: {
            type: 'string',
            label: 'Subunit'
        },
        symbol: {
            type: 'string',
            label: 'Symbol'
        },
        symbolPosition: {
            type: 'string',
            label: 'Symbol position',
            default: 'after'
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true
        }
    },
    hooks: {
        after: {
            create: [deleteMobileHash],
            update: [deleteMobileHash],
            patch: [deleteMobileHash],
            remove: [deleteMobileHash]
        }
    }
};

async function deleteMobileHash(context) {
    const app = context.app;

    await app.cache.set('mobile-currencies-hash', null);

    return context;
}
