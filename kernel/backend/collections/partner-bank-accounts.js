export default {
    name: 'partner-bank-accounts',
    title: 'Partner Bank Accounts',
    order: true,
    softDelete: false,
    schema: {
        name: {
            type: 'string',
            label: 'Name'
        },
        bankId: {
            type: 'string',
            label: 'Bank'
        },
        bankBranchId: {
            type: 'string',
            label: 'Bank branch office'
        },
        accountNumber: {
            type: 'string',
            label: 'Account number'
        },
        iban: {
            type: 'string',
            label: 'IBAN',
            required: false
        },
        bank: {
            type: 'object',
            blackbox: true,
            required: false
        },
        bankBranch: {
            type: 'object',
            blackbox: true,
            required: false
        },
        isDefault: {
            type: 'boolean',
            label: 'Is default',
            default: false
        }
    }
};
