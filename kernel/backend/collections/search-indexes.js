export default {
    name: 'search-indexes',
    softDelete: false,
    timestamps: false,
    uid: false,
    sync: false,
    noCache: true,
    schema: {
        collection: {
            type: 'string',
            index: true
        },
        collectionName: {
            type: 'string',
            index: true
        },
        view: {
            type: 'string',
            index: true
        },
        documentId: {
            type: 'string',
            index: true
        },
        documentCreatedAt: {
            type: 'string',
            required: false,
            index: true
        },
        documentResponsibleId: {
            type: 'string',
            required: false,
            index: true
        },
        documentIdentifier: {
            type: 'string',
            index: true
        },
        branchIds: {
            type: ['string'],
            required: false,
            index: true
        },
        createdBy: {
            type: 'string',
            required: false,
            index: true
        },
        responsibleId: {
            type: 'string',
            required: false,
            index: true
        },
        terms: {
            type: ['string'],
            text: true
        },
        text: {
            type: 'string',
            index: true
        }
    },
    publish(app, data, context) {
        return false;
    }
};
