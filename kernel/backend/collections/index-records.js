import _ from 'lodash';

export default {
    name: 'index-records',
    softDelete: false,
    timestamps: false,
    sync: false,
    noCache: true,
    schema: {
        status: {
            type: 'string',
            label: 'Status',
            allowed: ['active', 'deleted'],
            default: 'active'
        },
        collectionName: {
            type: 'string',
            label: 'Collection name'
        },
        indexName: {
            type: 'string',
            label: 'Index name'
        },
        documentCount: {
            type: 'integer',
            label: 'Document count'
        },
        usageCount: {
            type: 'integer',
            label: 'Usage count'
        },
        createAt: {
            type: 'datetime',
            label: 'Create at'
        },
        updatedAt: {
            type: 'datetime',
            label: 'Update at'
        }
    },
    publish(app, data, context) {
        // return app
        //     .channel('authenticated')
        //     .filter(connection => _.isPlainObject(connection.user) && !!connection.user.isRoot);
        return false;
    }
};
