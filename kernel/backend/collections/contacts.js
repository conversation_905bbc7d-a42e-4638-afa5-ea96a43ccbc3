import _ from 'lodash';
import {ObjectId} from 'mongodb';

export default {
    name: 'contacts',
    title: 'Partner Contacts',
    view: 'partners.contacts',
    labelParams: {
        from: 'name'
    },
    locationFrom: 'address',
    schema: {
        type: {
            type: 'string',
            label: 'Type',
            allowed: ['contact', 'invoice-address', 'delivery-address', 'other-address'],
            index: true
        },
        code: {
            type: 'string',
            label: 'Code',
            required: false,
            index: true
        },
        name: {
            type: 'string',
            label: 'Name',
            index: true
        },
        partnerId: {
            type: 'string',
            label: 'Partner',
            index: true,
            required: false
        },
        partnerType: {
            type: 'string',
            label: 'Partner type',
            required: false
        },
        relevantContactId: {
            type: 'string',
            label: 'Relevant person',
            required: false,
            index: true
        },
        appellationId: {
            type: 'string',
            label: 'Appellation',
            required: false
        },
        email: {
            type: 'string',
            label: 'Email address',
            regexp: 'EmailWithTLD',
            index: true,
            required: false
        },
        website: {
            type: 'string',
            label: 'Website',
            required: false
        },
        gender: {
            type: 'string',
            label: 'Gender',
            allowed: ['male', 'female'],
            required: false
        },
        department: {
            type: 'string',
            label: 'Department',
            required: false
        },
        positionId: {
            type: 'string',
            label: 'Job position',
            required: false
        },
        occupationId: {
            type: 'string',
            label: 'Occupation',
            required: false
        },
        birthdate: {
            type: 'date',
            label: 'Birthdate',
            required: false
        },
        birthPlace: {
            type: 'string',
            label: 'Place of birth',
            required: false
        },
        birthCountryId: {
            type: 'string',
            label: 'Country of birth',
            required: false
        },
        isEmergencyContact: {
            type: 'boolean',
            label: 'Emergency contact',
            required: false
        },
        isKithAndKin: {
            type: 'boolean',
            label: 'Kith and kin',
            required: false
        },
        degreeOfKinshipId: {
            type: 'string',
            label: 'Degree of kinship',
            required: false
        },
        phone: {
            type: 'string',
            label: 'Phone',
            required: false,
            index: true
        },
        phoneCountryCode: {
            type: 'string',
            label: 'Phone country code',
            required: false
        },
        phoneNumbers: {
            type: [
                {
                    type: 'object',
                    blackbox: true
                }
            ],
            label: 'Phone numbers',
            default: []
        },
        address: {
            type: 'object',
            blackbox: true,
            required: false
        },
        gln: {
            type: 'string',
            label: 'GLN',
            required: false,
            index: true
        },
        invoiceType: {
            type: 'string',
            label: 'Invoice type',
            required: false
        },
        companyName: {
            type: 'string',
            label: 'Company name',
            required: false
        },
        tin: {
            type: 'string',
            label: 'TIN',
            required: false
        },
        taxDepartment: {
            type: 'string',
            label: 'Tax department',
            required: false
        },
        legalName: {
            type: 'string',
            label: 'Legal name',
            required: false
        },
        identity: {
            type: 'string',
            label: 'Identity no',
            required: false
        },
        passportNo: {
            type: 'string',
            label: 'Passport no',
            required: false
        },
        note: {
            type: 'string',
            label: 'Note',
            required: false
        },
        params: {
            type: 'object',
            blackbox: true,
            required: false
        },
        // Used by ecommerce (Trendyol, Amazon etc.)
        defaultDeliveryOptionId: {
            type: 'string',
            label: 'Default delivery option',
            required: false
        }
    },

    attributes: {
        partner: {
            collection: 'kernel.partners',
            parentField: 'partnerId',
            childField: '_id'
        },
        appellation: {
            collection: 'kernel.appellations',
            parentField: 'appellationId',
            childField: '_id'
        },
        position: {
            collection: 'kernel.positions',
            parentField: 'positionId',
            childField: '_id'
        },
        occupation: {
            collection: 'kernel.occupations',
            parentField: 'occupationId',
            childField: '_id'
        },
        degreeOfKinship: {
            collection: 'hr.kinship-degrees',
            parentField: 'degreeOfKinshipId',
            childField: '_id'
        }
    },

    async searchTerms(document) {
        const values = Object.values(_.pick(document, ['name', 'email', 'phone']));

        if (_.isPlainObject(document.partner)) {
            values.push(document.partner.code);
            values.push(document.partner.name);
        }

        return values;
    },

    hooks: {
        after: {
            create: [setPrimaryPhone, syncPartner],
            update: [setPrimaryPhone],
            patch: [setPrimaryPhone]
        }
    }
};

async function setPrimaryPhone(context) {
    const app = context.app;
    const company = await app.collection('kernel.company').findOne({$select: ['address.countryId']});
    const defaultCountry = await app.collection('kernel.countries').findOne({
        _id: company.address.countryId,
        $select: ['code', 'phoneCode']
    });

    for (const result of Array.isArray(context.result) ? context.result : [context.result]) {
        if (Array.isArray(result.phoneNumbers) && result.phoneNumbers.length > 0) {
            const phoneCountryCode = result.phoneNumbers[0].countryCode || defaultCountry.code;
            const phoneCode = result.phoneNumbers[0].phoneCode || defaultCountry.phoneCode;
            const phone = `${phoneCode} ${result.phoneNumbers[0].number}`;

            await context.app.db.collection('kernel_contacts').updateOne(
                {_id: new ObjectId(result._id)},
                {
                    $set: {
                        phone,
                        phoneCountryCode
                    }
                },
                {
                    collation: {locale: context.app.config('app.locale')}
                }
            );

            result.phone = phone;
            result.phoneCountryCode = phoneCountryCode;
        }
    }

    try {
        app.memoryCache.clear(`aggregation-caches-kernel.contacts`);
        app.memoryCache.clear(`records-caches-kernel.contacts`);
    } catch (e) {
        console.log(e);
    }

    return context;
}

async function syncPartner(context) {
    const contact = context.result;

    if (contact.partnerId) {
        const partner = await context.app.collection('kernel.partners').findOne({
            _id: contact.partnerId,
            $select: ['_id', 'contactIds']
        });

        if (
            _.isObject(partner) &&
            (!Array.isArray(partner.contactIds) || partner.contactIds.indexOf(contact._id) === -1)
        ) {
            await context.app.collection('kernel.partners').update(partner._id, {
                $addToSet: {
                    contactIds: contact._id
                }
            });
        }
    }

    return context;
}
