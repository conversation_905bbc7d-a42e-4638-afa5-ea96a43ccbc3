export default {
    name: 'account-set-groups',
    title: 'Account Set Groups',
    order: true,
    cache: true,
    schema: {
        code: {
            type: 'string',
            label: 'Code',
            unique: true
        },
        name: {
            type: 'string',
            label: 'Name'
        },
        type: {
            type: 'string',
            label: 'Type',
            allowed: ['product', 'service', 'warehouse', 'fixture', 'fixed-asset'],
            default: 'product'
        },
        startDate: {
            type: 'date',
            label: 'Start date',
            required: false
        },
        endDate: {
            type: 'date',
            label: 'End date',
            required: false
        },
        note: {
            type: 'string',
            label: 'Note',
            required: false
        },
        accounts: {
            type: [
                {
                    key: {
                        type: 'string',
                        label: 'Key',
                        required: false
                    },
                    accountId: {
                        type: 'string',
                        label: 'Account',
                        required: false
                    }
                }
            ],
            default: []
        },
        system: {
            type: 'boolean',
            default: false
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true
        }
    }
};
