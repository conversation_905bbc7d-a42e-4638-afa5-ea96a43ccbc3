import _ from 'lodash';

export default {
    name: 'registry',
    timestamps: false,
    uid: false,
    softDelete: false,
    noCache: true,
    schema: {
        appName: {
            type: 'string',
            label: 'App name',
            default: 'entererp',
            index: true
        },
        userId: {
            type: 'string',
            label: 'User',
            index: true
        },
        key: {
            type: 'string',
            label: 'Key'
        },
        value: {
            type: 'string',
            label: 'Value'
        }
    },
    publish(app, data, context) {
        if (data.userId) {
            return app
                .channel('authenticated')
                .filter(connection => _.isObject(connection.user) && connection.user._id === data.userId);
        }
    }
};
