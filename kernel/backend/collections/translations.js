export default {
    name: 'translations',
    title: 'Translations',
    timestamps: false,
    uid: false,
    softDelete: false,
    cache: true,
    schema: {
        program: 'string',
        locale: 'string',
        key: 'string',
        message: {
            type: 'string',
            required: false
        },
        translated: {
            type: 'string',
            required: false
        }
    },
    publish(app, data, context) {
        return app.channel('authenticated');
    }
};
