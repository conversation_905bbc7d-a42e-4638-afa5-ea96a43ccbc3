export default {
    name: 'periods',
    title: 'Accounting Periods',
    cache: true,
    schema: {
        code: {
            type: 'string',
            label: 'Code',
            unique: true
        },
        name: {
            type: 'string',
            label: 'Name',
            unique: true
        },
        periodCount: {
            type: 'integer',
            label: 'Period count',
            min: 1,
            max: 12
        },
        fiscalYear: {
            type: 'integer',
            label: 'Fiscal year',
            unique: true
        },
        fiscalYearStart: {
            type: 'date',
            label: 'Fiscal year start date'
        },
        periods: [
            {
                code: {
                    type: 'string',
                    label: 'Code',
                    unique: true
                },
                name: {
                    type: 'string',
                    label: 'Name',
                    unique: true
                },
                status: {
                    type: 'string',
                    label: 'Status',
                    allowed: ['unBlocked', 'blockedExceptForAdvisers', 'blocked']
                },
                recordDateStart: {
                    type: 'date',
                    label: 'Record Date Start'
                },
                recordDateEnd: {
                    type: 'date',
                    label: 'Record Date End'
                },
                issueDateStart: {
                    type: 'date',
                    label: 'Issue Date Start'
                },
                issueDateEnd: {
                    type: 'date',
                    label: 'Issue Date End'
                },
                dueDateStart: {
                    type: 'date',
                    label: 'Due Date Start'
                },
                dueDateEnd: {
                    type: 'date',
                    label: 'Due Date End'
                }
            }
        ]
    }
};
