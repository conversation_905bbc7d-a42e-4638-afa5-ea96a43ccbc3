export default {
    name: 'units',
    title: 'Units',
    order: true,
    localCache: true,
    cache: true,
    schema: {
        name: {
            type: 'string',
            label: 'Name',
            translate: true
        },
        symbol: {
            type: 'string',
            label: 'Symbol',
            required: false
        },
        category: {
            type: 'string',
            label: 'Category',
            allowed: [
                'unit',
                'weight',
                'length',
                'area',
                'volume',
                'temperature',
                'energy',
                'time',
                'working-time',
                'other'
            ]
        },
        type: {
            type: 'string',
            label: 'Type',
            allowed: ['bigger', 'reference', 'smaller', 'free'],
            default: 'reference'
        },
        ratio: {
            type: 'decimal',
            label: 'Ratio',
            default: 1
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true
        }
    }
};
