import _ from 'lodash';
import {toUpper} from 'framework/helpers';

export default {
    name: 'notifications',
    softDelete: false,
    noCache: true,
    schema: {
        type: {
            type: 'string',
            default: 'info',
            index: true
        },
        isMeri: {
            type: 'boolean',
            default: false,
            index: true
        },
        isRead: {
            type: 'boolean',
            default: false,
            index: true
        },
        title: {
            type: 'string',
            required: false,
            index: true
        },
        message: {
            type: 'string'
        },
        shortMessage: {
            type: 'string',
            required: false
        },
        link: {
            path: {
                type: 'string',
                required: false
            },
            params: {
                type: 'object',
                blackbox: true,
                required: false
            }
        },
        userId: {
            type: 'string',
            required: false,
            index: true
        }
    },
    events: ['un-read-count-changed'],
    hooks: {
        after: {
            create: [sendMobileNotifications, sendUnReadNotificationCount],
            update: [sendUnReadNotificationCount],
            patch: [sendUnReadNotificationCount],
            remove: [sendUnReadNotificationCount]
        }
    },
    publish(app, data) {
        if (data.userId) {
            return app
                .channel('authenticated')
                .filter(connection => _.isPlainObject(connection.user) && connection.user._id === data.userId);
        }

        return app.channel('authenticated');
    }
};

function sendMobileNotifications(context) {
    const app = context.app;

    (async () => {
        const result = (Array.isArray(context.result) ? context.result : [context.result]).filter(
            item => !!item.isMeri && !!item.shortMessage && !!item.userId
        );

        for (const item of result) {
            if (!!item.title) {
                item.title = toUpper(item.title);
            }

            await app.sendPushNotification(item.userId, item.title, item.shortMessage, {
                notificationId: item._id
            });
        }
    })();

    return context;
}

function sendUnReadNotificationCount(context) {
    const app = context.app;

    (async () => {
        const result = Array.isArray(context.result) ? context.result : [context.result];
        const userIds = [];

        for (const item of result) {
            if (!!item.userId && userIds.indexOf(item.userId) === -1) {
                userIds.push(item.userId);
            }
        }

        for (const userId of userIds) {
            const count = await app.collection('kernel.notifications').count({
                userId,
                isRead: false
            });

            context.service.emit('un-read-count-changed', {
                userId,
                count
            });
        }
    })();

    return context;
}
