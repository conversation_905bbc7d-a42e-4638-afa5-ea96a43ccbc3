export default {
    name: 'meeting-locations',
    title: 'Meeting Locations',
    order: true,
    cache: true,
    schema: {
        code: {
            type: 'string',
            label: 'Code',
            unique: true
        },
        name: {
            type: 'string',
            label: 'Name'
        },
        note: {
            type: 'string',
            label: 'Note',
            required: false
        },
        address: {
            type: 'object',
            blackbox: true,
            required: false
        },
        isPartnerAddress: {
            type: 'boolean',
            default: false
        },
        system: {
            type: 'boolean',
            default: false
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true
        }
    },
    attributes: {
        branch: {
            collection: 'kernel.branches',
            parentField: 'branchId',
            childField: '_id'
        }
    },
    hooks: {
        before: {
            create: [],
            update: [],
            patch: [],
            remove: []
        }
    }
};
