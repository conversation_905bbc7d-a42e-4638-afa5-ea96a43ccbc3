import _ from 'lodash';
import {ObjectId} from 'mongodb';

export default {
    name: 'user-groups',
    title: 'User Groups',
    order: true,
    tree: true,
    cache: true,
    schema: {
        code: {
            type: 'string',
            label: 'Code',
            unique: true
        },
        name: {
            type: 'string',
            label: 'Name'
        },
        description: {
            type: 'string',
            label: 'Description',
            required: false
        },
        isDefault: {
            type: 'boolean',
            label: 'Is default',
            default: false
        },
        isRoot: {
            type: 'boolean',
            default: false
        },
        system: {
            type: 'boolean',
            default: false
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true
        },
        permissions: {
            type: [
                {
                    name: 'string',
                    canDo: 'boolean'
                }
            ],
            default: []
        },
        recordPermissions: {
            type: [
                {
                    name: 'string',
                    read: 'string',
                    create: 'string',
                    update: 'string',
                    delete: 'string'
                }
            ],
            default: []
        },
        menus: {
            type: [
                {
                    name: 'string',
                    canAccess: 'boolean'
                }
            ],
            default: []
        },
        userIds: {
            type: ['string'],
            default: []
        }
    },
    hooks: {
        before: {
            remove: [checkIsDefault]
        },
        after: {
            create: [setIsDefault, syncUsers],
            update: [setIsDefault, syncUsers],
            patch: [setIsDefault, syncUsers],
            remove: []
        }
    }
};

async function checkIsDefault(context) {
    const query = context.params.query || {};

    if (context.id) {
        query._id = context.id;
    }

    const items = await context.app.collection('kernel.user-groups').find({
        ...query,
        $select: ['isDefault']
    });

    if (items.some(item => item.isDefault)) {
        throw new context.app.errors.Unprocessable(context.translate('Default user group cannot be removed!'));
    }

    return context;
}

async function setIsDefault(context) {
    if (_.isObject(context.result) && context.result.isDefault) {
        context.app.db
            .collection('kernel_user-groups')
            .updateMany(
                {_id: {$ne: new ObjectId(context.result._id)}},
                {$set: {isDefault: false}},
                {collation: {locale: context.app.config('app.locale')}}
            );
    }

    return context;
}

async function syncUsers(context) {
    if (!_.isObject(context.result)) return context;

    if ((await context.app.collection('kernel.users').count()) < 1) return;

    const currentGroup = context.result;
    let operations = [];

    const addedUsers = await context.app.collection('kernel.users').find({
        _id: {$in: currentGroup.userIds},
        $select: ['_id', 'groupIds']
    });
    const removedUsers = await context.app.collection('kernel.users').find({
        _id: {$nin: addedUsers.map(u => u._id)},
        groupIds: currentGroup._id,
        $select: ['_id', 'groupIds']
    });
    for (const user of addedUsers) {
        operations.push({
            updateOne: {
                filter: {_id: user._id},
                update: {$addToSet: {groupIds: currentGroup._id}}
            }
        });
    }
    for (const user of removedUsers) {
        operations.push({
            updateOne: {
                filter: {_id: user._id},
                update: {$pull: {groupIds: currentGroup._id}}
            }
        });
    }
    if (operations.length > 0) {
        await context.app.collection('kernel.users').bulkWrite(operations, {ordered: false});

        operations = [];
    }

    const userIds = _.uniq(addedUsers.map(u => u._id).concat(removedUsers.map(u => u._id)));
    if (userIds.length > 0) {
        const users = await context.app.collection('kernel.users').find({
            _id: {$in: userIds},
            $select: ['_id', 'groupIds']
        });

        for (const user of users) {
            const groupIds = user.groupIds;

            if (groupIds.length < 1) continue;

            const groups = await context.app.collection('kernel.user-groups').find({
                _id: {$in: groupIds},
                $select: ['_id', 'permissions', 'recordPermissions', 'menus', 'isRoot']
            });

            const isRoot = groups.some(group => group.isRoot);
            if (isRoot) {
                operations.push({
                    updateOne: {
                        filter: {_id: user._id},
                        update: {
                            $set: {
                                isRoot: true,
                                permissions: [],
                                recordPermissions: [],
                                menus: []
                            }
                        }
                    }
                });

                continue;
            }

            const permissions = [];
            const recordPermissions = [];
            const menus = [];
            for (const group of groups) {
                for (const permission of group.permissions ?? []) {
                    const existingIndex = _.findIndex(permissions, p => p.name === permission.name);

                    if (existingIndex !== -1) {
                        const existing = permissions[existingIndex];

                        if (!existing.canDo) existing.canDo = permission.canDo;
                    } else {
                        permissions.push(permission);
                    }
                }

                for (const recordPermission of group.recordPermissions) {
                    const existingIndex = _.findIndex(recordPermissions, p => p.name === recordPermission.name);

                    if (existingIndex !== -1) {
                        const existing = recordPermissions[existingIndex];

                        if (existing.read === 'no' || (existing.read === 'owned' && recordPermission.read === 'yes'))
                            existing.read = recordPermission.read;
                        if (existing.create === 'no') existing.create = recordPermission.create;
                        if (
                            existing.update === 'no' ||
                            (existing.update === 'owned' && recordPermission.update === 'yes')
                        )
                            existing.update = recordPermission.update;
                        if (
                            existing.delete === 'no' ||
                            (existing.delete === 'owned' && recordPermission.delete === 'yes')
                        )
                            existing.delete = recordPermission.delete;
                    } else {
                        recordPermissions.push(recordPermission);
                    }
                }

                for (const menu of group.menus) {
                    const existingIndex = _.findIndex(menus, m => m.name === menu.name);

                    if (existingIndex !== -1) {
                        const existing = menus[existingIndex];

                        if (!existing.canAccess) existing.canAccess = menu.canAccess;
                    } else {
                        menus.push(menu);
                    }
                }
            }

            operations.push({
                updateOne: {
                    filter: {_id: user._id},
                    update: {$set: {isRoot: false, permissions, recordPermissions, menus}}
                }
            });
        }
    }

    if (operations.length > 0) {
        await context.app.collection('kernel.users').bulkWrite(operations, {ordered: false});
    }

    return context;
}
