export default {
    name: 'templates',
    label: 'Templates',
    schema: {
        source: {
            type: 'string',
            label: 'Source',
            index: true
        },
        title: {
            type: 'string',
            label: 'Title',
            translate: true,
            index: true
        },
        contentType: {
            type: 'string',
            label: 'Output type',
            default: 'html'
        },
        outputType: {
            type: 'string',
            label: 'Output type',
            default: 'pdf'
        },
        outputOrientation: {
            type: 'string',
            label: 'Output orientation',
            default: 'portrait'
        },
        footerFormat: {
            type: 'string',
            label: 'Footer format',
            default: 'default'
        },
        footerTemplate: {
            type: 'string',
            label: 'Footer template',
            default: ''
        },
        outputFormat: {
            type: 'string',
            label: 'Output format',
            default: 'A4'
        },
        outputWidth: {
            type: 'decimal',
            label: 'Output width',
            default: 0
        },
        outputHeight: {
            type: 'decimal',
            label: 'Output height',
            default: 0
        },
        content: {
            type: 'string',
            label: 'content',
            default: ''
        }
    }
};
