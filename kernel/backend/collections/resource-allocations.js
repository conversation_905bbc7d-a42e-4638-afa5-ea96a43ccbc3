export default {
    name: 'resource-allocations',
    softDelete: false,
    timestamps: false,
    schema: {
        status: {
            type: 'string',
            label: 'Status',
            allowed: ['draft', 'planned', 'committed', 'consumed'],
            default: 'draft',
            index: true
        },
        resourceId: {
            type: 'string',
            label: 'Resource',
            index: true
        },
        resourceCode: {
            type: 'string',
            label: 'Resource code',
            index: true
        },
        resourceName: {
            type: 'string',
            label: 'Resource name',
            index: true
        },
        documentId: {
            type: 'string',
            label: 'Document',
            index: true
        },
        documentCollection: {
            type: 'string',
            label: 'Document collection',
            index: true
        },
        documentView: {
            type: 'string',
            label: 'Document view',
            required: false
        },
        documentCode: {
            type: 'string',
            label: 'Document code',
            index: true
        },
        subDocumentId: {
            type: 'string',
            label: 'Sub-document',
            index: true,
            required: false
        },
        subDocumentCollection: {
            type: 'string',
            label: 'Sub-document collection',
            index: true,
            required: false
        },
        subDocumentView: {
            type: 'string',
            label: 'Sub-document view',
            required: false
        },
        subDocumentCode: {
            type: 'string',
            label: 'Sub-document code',
            required: false,
            index: true
        },
        startDate: {
            type: 'datetime',
            label: 'Start date',
            index: true
        },
        endDate: {
            type: 'datetime',
            label: 'End date',
            index: true
        },
        // In minutes
        duration: {
            type: 'decimal',
            label: 'Duration',
            index: true
        }
    }
};
