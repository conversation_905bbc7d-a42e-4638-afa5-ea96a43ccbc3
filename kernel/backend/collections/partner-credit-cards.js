export default {
    name: 'partner-credit-cards',
    title: 'Partner Credit Cards',
    order: true,
    softDelete: false,
    schema: {
        cardHolder: {
            type: 'string',
            label: 'Card holder'
        },
        cardBrand: {
            type: 'string',
            label: 'Card brand'
        },
        cardNumber: {
            type: 'string',
            label: 'Card number',
            required: false
        },
        expireMonth: {
            type: 'integer',
            label: 'Expire month'
        },
        expireYear: {
            type: 'integer',
            label: 'Expire year'
        },
        cvv: {
            type: 'string',
            label: 'Cvv',
            required: false
        },
        isDefault: {
            type: 'boolean',
            label: 'Is default',
            default: false
        }
    }
};
