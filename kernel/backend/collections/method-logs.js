import _ from 'lodash';

export default {
    name: 'method-logs',
    softDelete: false,
    timestamps: false,
    sync: false,
    noCache: true,
    schema: {
        status: {
            type: 'string',
            default: 'started',
            allowed: ['started', 'completed', 'errored'],
            index: true
        },
        startDate: {
            type: 'datetime',
            index: true
        },
        endDate: {
            type: 'datetime',
            required: false,
            index: true
        },
        time: {
            type: 'decimal',
            required: false,
            index: true
        },
        name: {
            type: 'string',
            index: true
        },
        error: {
            type: 'string',
            required: false
        },
        userId: {
            type: 'string',
            required: false,
            index: true
        },
        user: {
            code: {
                type: 'string',
                default: '',
                index: true
            },
            name: {
                type: 'string',
                default: 'System',
                index: true
            }
        }
    },
    publish(app, data, context) {
        return false;
    }
};
