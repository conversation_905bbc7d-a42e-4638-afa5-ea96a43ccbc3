import _ from 'lodash';

export default {
    name: 'maintenances',
    softDelete: false,
    timestamps: false,
    sync: false,
    noCache: true,
    schema: {
        status: {
            type: 'string',
            label: 'Status',
            default: 'in-progress',
            index: true
        },
        maintenance: {
            type: 'string',
            label: 'Maintenance',
            index: true
        },
        startDate: {
            type: 'datetime',
            label: 'Start date',
            default: 'date:now',
            index: true
        },
        endDate: {
            type: 'datetime',
            label: 'End date',
            required: false,
            index: true
        },
        message: {
            type: 'string',
            label: 'Message',
            required: false,
            index: true
        },
        userCode: {
            type: 'string',
            label: 'User code',
            required: false,
            index: true
        },
        userName: {
            type: 'string',
            label: 'User name',
            required: false,
            index: true
        },
        progress: {
            type: 'string',
            label: 'Progress',
            required: false
        }
    },
    publish(app, data, context) {
        return app
            .channel('authenticated')
            .filter(connection => _.isPlainObject(connection.user) && !!connection.user.isRoot);
    }
};
