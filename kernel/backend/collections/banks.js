export default {
    name: 'banks',
    title: 'Banks',
    order: true,
    cache: true,
    schema: {
        code: {
            type: 'string',
            label: 'Code',
            unique: true
        },
        name: {
            type: 'string',
            label: 'Name'
        },
        swift: {
            type: 'string',
            label: 'Swift',
            required: false
        },
        logo: {
            type: 'string',
            label: 'Logo',
            required: false
        },
        integrationParams: {
            type: 'string',
            label: 'Integration params',
            required: false
        },
        bankBranchIds: {
            type: ['string'],
            default: []
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true
        }
    }
};
