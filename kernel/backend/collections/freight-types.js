import _ from 'lodash';

export default {
    name: 'freight-types',
    title: 'Freight Types',
    schema: {
        type: {
            type: 'string',
            label: 'Type',
            allowed: ['expense', 'income'],
            default: 'expense'
        },
        code: {
            type: 'string',
            label: 'Code'
        },
        name: {
            type: 'string',
            label: 'Name'
        },
        partnerId: {
            type: 'string',
            label: 'Partner',
            required: false
        },
        serviceProductId: {
            type: 'string',
            label: 'Service product'
        },
        expenseCategoryId: {
            type: 'string',
            label: 'Expense category',
            required: false
        },
        expenseTagId: {
            type: 'string',
            label: 'Expense tag',
            required: false
        },
        expenseAccountId: {
            type: 'string',
            label: 'Expense account',
            required: false
        },
        incomeCategoryId: {
            type: 'string',
            label: 'Income category',
            required: false
        },
        incomeTagId: {
            type: 'string',
            label: 'Income tag',
            required: false
        },
        incomeAccountId: {
            type: 'string',
            label: 'Income account',
            required: false
        },
        currencyId: {
            type: 'string',
            label: 'Currency'
        },
        taxIds: {
            type: ['string'],
            label: 'Taxes',
            default: []
        },
        amount: {
            type: 'decimal',
            label: 'Amount',
            default: 0
        },
        grossAmount: {
            type: 'decimal',
            label: 'Gross amount',
            default: 0
        },
        distributionMethod: {
            type: 'string',
            label: 'Distribution method',
            default: 'equal'
        },
        fixtureId: {
            type: 'string',
            label: 'Fixture',
            required: false
        },
        financialProjectId: {
            type: 'string',
            label: 'Financial project',
            required: false
        },
        validFrom: {
            type: 'date',
            label: 'Valid from',
            required: false
        },
        validTo: {
            type: 'date',
            label: 'Valid to',
            required: false
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true
        }
    }
};
