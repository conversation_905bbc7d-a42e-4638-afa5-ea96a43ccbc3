import _ from 'lodash';

export default function (app) {
    app.extensionsMap = {};
    app.extend = params => {
        const key = `${params.type}.${params.name}`;

        if (!Array.isArray(app.extensionsMap[key])) {
            app.extensionsMap[key] = [];
        }

        app.extensionsMap[key].push(params.payload);
    };

    for (const program of global.__programs) {
        if (_.isFunction(program.extend)) {
            program.extend(app);
        }
    }
}
