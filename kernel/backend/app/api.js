import * as handlers from './api/handlers';
import * as v1Handlers from './api/handlers/v1';
import * as reference from './api/reference';

export {authenticate} from './api/utils';

export default function (app) {
    app.post('/api/authenticate', (request, response) => handlers.authenticate(app, request, response));
    app.post('/api/records', (request, response) => handlers.records(app, request, response));
    app.post('/api/rpc', (request, response) => handlers.rpc(app, request, response));

    // app.get('/api/v1', (request, response) => scalar(app, request, response));
    // app.get('/api/v1/openapi.json', (request, response) => openapi(app, request, response));
    // app.post('/api/v1/records', (request, response) => handlers.records(app, request, response));
    // app.post('/api/v1/rpc', (request, response) => handlers.rpc(app, request, response));

    app.get('/api/v1/reference/info', (request, response) => reference.info(app, request, response));
    app.get('/api/v1/reference/openapi/:module', (request, response) => reference.openapi(app, request, response));
    app.get('/api/v1', (request, response) => reference.frontend(app, request, response));

    app.post('/api/v1/authenticate', (request, response) => v1Handlers.authenticate(app, request, response));

    app.get('/api/v1/:module/:collection/:id', (request, response) => v1Handlers.getRecord(app, request, response));
    app.get('/api/v1/:module/:collection', (request, response) => v1Handlers.listRecords(app, request, response));
    app.post('/api/v1/:module/:collection', (request, response) => v1Handlers.createRecord(app, request, response));
    app.patch('/api/v1/:module/:collection/:id', (request, response) => v1Handlers.patchRecord(app, request, response));
    app.delete('/api/v1/:module/:collection/:id', (request, response) =>
        v1Handlers.deleteRecord(app, request, response)
    );
    app.post('/api/v1/:module/:collection/aggregate', (request, response) =>
        v1Handlers.aggregateRecords(app, request, response)
    );

    app.all('/api/*', (request, response) => {
        return response.status(404).json({
            status: 'error',
            code: 404,
            message: 'Not found'
        });
    });
}
