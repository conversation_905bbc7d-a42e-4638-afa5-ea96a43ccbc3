import _ from 'lodash';
import methodService from 'framework/services/method';

export default function (app) {
    app.method = (name, definition) => {
        if (name.indexOf('.') !== -1) {
            name = name.split('.').join('/');
        }

        const methodName = `method-${name}`;

        if (!_.isObject(definition) && !_.isFunction(definition)) {
            return app.service(methodName);
        } else {
            const service = methodService({multi: true});
            service.app = app;
            service.isMethod = true;
            service.sync = false;
            service.action = _.isFunction(definition) ? definition : definition.action;
            service.authenticate = definition.authenticate !== false;

            if (_.isObject(definition.schema)) {
                service.schema = definition.schema;
            }

            app.use(methodName, service);
        }

        return app;
    };

    app.rpc = (name, payload = {}, params = {}) => {
        if (name.indexOf('.') !== -1) {
            name = name.split('.').join('/');
        }

        const service = app.service(`method-${name}`);

        // Fix feathers errors "First parameter for 'create' must be an object".
        if (!_.isPlainObject(payload)) {
            payload = {
                __value: payload
            };
        }

        return service.create(payload, params);
    };
}
