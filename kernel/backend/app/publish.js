import _ from 'lodash';

export default function (app) {
    // On a new real-time connection, add it to the
    // anonymous channel
    app.on('connection', connection => {
        app.channel('anonymous').join(connection);
    });

    app.on('login', (payload, {connection}) => {
        // Connection can be undefined if there is no
        // real-time connection, e.g. when logging in via REST.
        if (!!connection) {
            app.channel('anonymous').leave(connection);
            app.channel('authenticated').join(connection);
        }
    });

    // Publish all events to all authenticated users.
    app.publish((data, context) => {
        // Don't publish method events!
        if (context.service.isMethod) {
            return false;
        }

        if (!!app.get('disablePublications')) {
            return false;
        }

        // Check context params.
        if (_.isObject(context.params)) {
            const app = context.app;
            const user = context.params.user;

            if (context.service.isCollection) {
                // Get collection name.
                const collectionName =
                    context.path.indexOf('/') !== -1 ? context.path.split('/').join('.') : context.path;
                const collection = app.collection(collectionName);

                // Check permission.
                if (
                    !collection.isLocalCached &&
                    _.isPlainObject(user) &&
                    !user.isRoot &&
                    !context.params.disablePermissionCheck
                ) {
                    // Get permissions.
                    const mappedRecordPermissions = app.get('mappedRecordPermissions') || {};
                    let permission = null;
                    if (
                        _.isObject(mappedRecordPermissions[user._id]) &&
                        !_.isEmpty(mappedRecordPermissions[user._id])
                    ) {
                        permission = mappedRecordPermissions[user._id][collectionName];

                        if (!_.isObject(permission)) {
                            permission = (user.recordPermissions || []).find(p => p.name === collectionName);

                            mappedRecordPermissions[user._id][collectionName] = permission;

                            app.set('mappedRecordPermissions', mappedRecordPermissions);
                        }
                    } else {
                        permission = (user.recordPermissions || []).find(p => p.name === collectionName);

                        mappedRecordPermissions[user._id] = {};

                        mappedRecordPermissions[user._id][collectionName] = permission;

                        app.set('mappedRecordPermissions', mappedRecordPermissions);
                    }

                    // Get user record permission.
                    // const permission = (user.recordPermissions || []).find(p => p.name === collectionName);

                    // Check if the user has any permission for this collection.
                    if (!_.isObject(permission)) {
                        return false;
                    }

                    // Check read permission.
                    if (permission.read === 'no') {
                        return false;
                    } else if (
                        permission.read === 'owned' &&
                        _.isObject(data) &&
                        data.createdBy !== user._id &&
                        data.assignedBy !== user._id &&
                        data.assignedTo !== user._id
                    ) {
                        return false;
                    }
                }

                // Filter data.
                if (!context.params.disablePublicationFilter) {
                    const publicData = {};

                    if (_.isString(data._id)) {
                        publicData._id = data._id;
                        publicData.collectionName = collectionName;
                        publicData.type = context.method;
                        publicData.isLocalCached = collection.isLocalCached;
                    }

                    return app.channel('authenticated').send(publicData);
                }
            }
        }

        if (data.isBulkWrite) {
            return app.channel('authenticated').send({
                collectionName: data.collectionName,
                _id: null,
                type: 'bulkWrite'
            });
        }

        return app.channel('authenticated');
    });
}

/*
import _ from 'lodash';

export default function (app) {
    // On a new real-time connection, add it to the
    // anonymous channel
    app.on('connection', connection => {
        app.channel('anonymous').join(connection);
    });

    app.on('login', (payload, {connection}) => {
        // Connection can be undefined if there is no
        // real-time connection, e.g. when logging in via REST.
        if (!!connection) {
            app.channel('anonymous').leave(connection);
            app.channel('authenticated').join(connection);

            if (!!connection.user && connection.user._id) {
                app.channel(connection.user._id).join(connection);
            }
        }
    });

    // Publish all events to all authenticated users.
    app.publish((data, context) => {
        // Don't publish method events!
        if (context.service.isMethod) {
            return false;
        }

        if (!!app.get('disablePublications')) {
            return false;
        }

        // Check context params.
        if (_.isObject(context.params)) {
            const app = context.app;
            const user = context.params.user;

            if (context.service.isCollection) {
                // Get collection name.
                const collectionName =
                    context.path.indexOf('/') !== -1 ? context.path.split('/').join('.') : context.path;
                const collection = app.collection(collectionName);

                // Check permission.
                if (
                    !collection.isLocalCached &&
                    _.isPlainObject(user) &&
                    !user.isRoot &&
                    !context.params.disablePermissionCheck
                ) {
                    // Get permissions.
                    const mappedRecordPermissions = app.get('mappedRecordPermissions') || {};
                    let permission = null;
                    if (
                        _.isObject(mappedRecordPermissions[user._id]) &&
                        !_.isEmpty(mappedRecordPermissions[user._id])
                    ) {
                        permission = mappedRecordPermissions[user._id][collectionName];

                        if (!_.isObject(permission)) {
                            permission = (user.recordPermissions || []).find(p => p.name === collectionName);

                            mappedRecordPermissions[user._id][collectionName] = permission;

                            app.set('mappedRecordPermissions', mappedRecordPermissions);
                        }
                    } else {
                        permission = (user.recordPermissions || []).find(p => p.name === collectionName);

                        mappedRecordPermissions[user._id] = {};

                        mappedRecordPermissions[user._id][collectionName] = permission;

                        app.set('mappedRecordPermissions', mappedRecordPermissions);
                    }

                    // Get user record permission.
                    // const permission = (user.recordPermissions || []).find(p => p.name === collectionName);

                    // Check if the user has any permission for this collection.
                    if (!_.isObject(permission)) {
                        return false;
                    }

                    // Check read permission.
                    if (permission.read === 'no') {
                        return false;
                    } else if (
                        permission.read === 'owned' &&
                        _.isObject(data) &&
                        data.createdBy !== user._id &&
                        data.assignedBy !== user._id &&
                        data.assignedTo !== user._id
                    ) {
                        return false;
                    }
                }

                // Filter data.
                if (!context.params.disablePublicationFilter) {
                    const publicData = {};

                    if (_.isString(data._id)) {
                        publicData._id = data._id;
                        publicData.collectionName = collectionName;
                        publicData.type = context.method;
                        publicData.isLocalCached = collection.isLocalCached;
                    }

                    if (!!user) {
                        return app.channel(user._id).send(publicData);
                    }

                    return app.channel('authenticated').send(publicData);
                }
            }
        }

        if (data.isBulkWrite) {
            if (_.isObject(context.params) && !!context.params.user && !!context.params.user._id) {
                return app.channel(context.params.user._id).send({
                    collectionName: data.collectionName,
                    _id: null,
                    type: 'bulkWrite'
                });
            }

            return app.channel('authenticated').send({
                collectionName: data.collectionName,
                _id: null,
                type: 'bulkWrite'
            });
        }

        if (_.isObject(context.params) && !!context.params.user && !!context.params.user._id) {
            return app.channel(context.params.user._id);
        }

        return app.channel('authenticated');
    });
}
*/
