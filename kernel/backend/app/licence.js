import axios from 'axios';
import getMAC from 'getmac';
import {encrypt, decrypt} from 'framework/crypto';
import EJSON from 'framework/ejson';

export default async function (app) {
    if (app.absoluteUrl() === 'https://main.entererp.com' || app.absoluteUrl() === 'https://template.entererp.com') {
        return;
    }

    const serverAddress = app.absoluteUrl();
    const macAddress = getMAC();
    let license = {};
    let hasResponse = false;
    let tryCount = 0;

    do {
        console.log('Requesting license.');
        try {
            const response = await axios({
                method: 'POST',
                url: 'https://main.entererp.com/elm/common/check-license',
                headers: {
                    'Content-Type': 'application/json'
                },
                data: JSON.stringify(encrypt(EJSON.stringify({macAddress, serverAddress})))
            });

            license = EJSON.parse(decrypt(response.data));
            hasResponse = true;
        } catch (error) {
            console.error(error);

            license.status = 'invalid';
            license.reason = error.message || 'Invalid license!';
        }

        tryCount++;
    } while (!hasResponse && tryCount < 5);

    app.set('license', license);
}
