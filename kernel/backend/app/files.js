import path from 'path';
import fs from 'fs-extra';
import _ from 'lodash';
import multer from 'multer';
import mime from 'mime';
import sharp from 'sharp';
import {RateLimiterRedis} from 'rate-limiter-flexible';
import EJSON from 'framework/ejson';
import Random from 'framework/random';
import authMiddleware from 'framework/middlewares/auth';
import {encodeRFC5987, slugify} from 'framework/helpers';

export default function (app) {
    const collection = app.collection('kernel.files');

    // Files.
    app.files = {
        async read(id) {
            const fileInfo = await collection.get(id);

            return fs.readFile(fileInfo.path);
        },
        async write(filePath, data, extra = {}) {
            await fs.ensureDir(path.dirname(filePath));
            await fs.writeFile(filePath, data);

            const stats = await fs.stat(filePath);
            const mimeType = mime.getType(filePath);
            const now = app.datetime.local();
            const defaultCategory = await app.collection('kernel.file-categories').findOne({system: true});
            const file = {};
            file.name = path.basename(filePath);
            file.extension = mime.getExtension(mimeType);
            file.contentType = mimeType;
            file.size = stats.size;
            file.path = filePath;
            file.uploadedAt = now.toJSDate();
            if (file.contentType === 'application/x-rar') {
                file.extension = 'rar';
            }
            if (!!defaultCategory) {
                file.categoryId = defaultCategory._id;
            }

            return await collection.create({...file, ...extra});
        },
        async create(filePath, extra = {}) {
            await fs.ensureDir(path.dirname(filePath));

            const stats = await fs.stat(filePath);
            const mimeType = mime.getType(filePath);
            const now = app.datetime.local();
            const defaultCategory = await app.collection('kernel.file-categories').findOne({system: true});
            const file = {};
            file.name = path.basename(filePath);
            file.extension = mime.getExtension(mimeType);
            file.contentType = mimeType;
            file.size = stats.size;
            file.path = filePath;
            file.uploadedAt = now.toJSDate();
            if (file.contentType === 'application/x-rar') {
                file.extension = 'rar';
            }
            if (!!defaultCategory) {
                file.categoryId = defaultCategory._id;
            }

            return await collection.create({...file, ...extra});
        },
        async delete(id) {
            const fileInfo = await this.findOne({_id: id});

            if (!!fileInfo) {
                if (await fs.exists(fileInfo.path)) {
                    await fs.unlink(fileInfo.path);
                }

                await this.remove({_id: id});
            }
        },
        get(id, params = {}) {
            return collection.get(id);
        },
        find(query, params = {}) {
            return collection.find(query, params);
        },
        findOne(query, params = {}) {
            return collection.findOne(query, params);
        },
        patch(query, data, params = {}) {
            return collection.patch(query, data, params);
        },
        remove(query, params = {}) {
            return collection.remove(query, params);
        },
        async readStream(id) {
            const fileInfo = await collection.get(id);

            return fs.createReadStream(fileInfo.path);
        },
        async writeStream(filePath, extra = {}, callback = _.noop) {
            const mimeType = mime.getType(filePath);
            const now = app.datetime.local();
            const defaultCategory = await app.collection('kernel.file-categories').findOne({system: true});

            const file = {};
            file.name = path.basename(filePath);
            file.extension = mime.getExtension(mimeType);
            file.contentType = mimeType;
            file.size = 0;
            file.path = filePath;
            file.uploadedAt = now.toJSDate();
            if (file.contentType === 'application/x-rar') {
                file.extension = 'rar';
            }
            if (!!defaultCategory) {
                file.categoryId = defaultCategory._id;
            }
            const fileInfo = await collection.create({...file, ...extra});

            await fs.ensureDir(path.dirname(filePath));

            const stream = fs.createWriteStream(filePath);
            stream.on('finish', async () => {
                const stats = await fs.stat(filePath);

                await collection.patch({_id: fileInfo._id}, {size: stats.size});
            });

            callback(fileInfo);

            return stream;
        }
    };

    // Initialize rate limiter.
    const uploadLimiter = new RateLimiterRedis({
        storeClient: app.cache.client,
        points: 30,
        duration: 1,
        execEvenly: false,
        blockDuration: 10,
        keyPrefix: 'uploadRateLimiter'
    });
    const rateLimiterMiddleware = async function (request, response, next) {
        try {
            await uploadLimiter.consume(request.ip);

            next();
        } catch (error) {
            if (error instanceof Error) {
                next(new app.errors.Unprocessable());
            } else {
                const seconds = Math.round(error.msBeforeNext / 1000) || 1;

                next(
                    new app.errors.TooManyRequests('Too many requests', {
                        reTryAfter: seconds
                    })
                );
            }
        }
    };

    // Prepare disk storage.
    const storage = multer.diskStorage({
        destination(request, file, callback) {
            const now = app.datetime.local();
            let directoryPath = app.get('isPackaged')
                ? app.setting('system.uploadDirectoryPath')
                : app.config('paths.files');

            if (directoryPath[0] !== '/') {
                directoryPath = path.join(process.cwd(), directoryPath);
            }

            directoryPath = path.join(directoryPath, now.year.toString(), now.month.toString(), now.day.toString());

            fs.ensureDir(directoryPath, error => {
                if (!!error) {
                    callback(error);
                } else {
                    callback(null, directoryPath);
                }
            });
        },
        filename(request, file, callback) {
            callback(null, `${Random.id(8)}_${slugify(file.originalname)}`);
        }
    });

    // Initialize upload middleware.
    const uploadMiddleware = multer({
        fileFilter(request, file, callback) {
            const defaultUploadMaxFileSize = app.setting('system.defaultUploadMaxFileSize');
            const uploadAcceptedExtensions = (app.setting('system.uploadAcceptedExtensions') || '').split(',');
            const uploadMaxFileSizeForExtensions = app.setting('system.uploadMaxFileSizeForExtensions');
            let fileType = mime.getExtension(file.mimetype);

            // Fix rar extension.
            if (
                file.mimetype === 'application/x-rar' ||
                (file.mimetype === 'application/x-compressed' && file.originalname.toLowerCase().endsWith('.rar'))
            ) {
                fileType = 'rar';
            }
            if (file.mimetype === 'application/x-zip-compressed') {
                fileType = 'zip';
            }
            if (file.mimetype === 'application/octet-stream' && file.originalname.toLowerCase().endsWith('.dwg')) {
                fileType = 'dwg';
            }
            if (file.mimetype === 'application/octet-stream' && file.originalname.toLowerCase().endsWith('.step')) {
                fileType = 'step';
            }
            if (file.originalname.toLowerCase().endsWith('.btw')) {
                fileType = 'btw';
            }
            if (file.originalname.toLowerCase().endsWith('.bin')) {
                fileType = 'bin';
            }
            if (file.originalname.toLowerCase().endsWith('.fzip')) {
                fileType = 'fzip';
            }

            // Check auth.
            if (!_.isPlainObject(request.user)) {
                return callback(new Error('Not authenticated!'), false);
            }

            // Check file type.
            if (uploadAcceptedExtensions.indexOf(fileType) === -1) {
                return callback(new Error(`Not Acceptable file type ${fileType} of ${file.originalname}!`), false);
            }

            // Check file size.
            if (file.size === 0) {
                return callback(new Error('Invalid file!'), false);
            }
            let allowedFileSize = defaultUploadMaxFileSize;
            const allowedFileSizeForExtension = uploadMaxFileSizeForExtensions.find(s => s.extension === fileType);
            if (_.isObject(allowedFileSizeForExtension)) {
                allowedFileSize = allowedFileSizeForExtension.size;
            }
            if (file.size > allowedFileSize * Math.pow(1024, 2)) {
                return callback(new Error(`Max uploading file size must be under ${allowedFileSize}MB!`), false);
            }

            callback(null, true);
        },
        storage
    }).array('files');

    // Upload files
    app.post(
        '/files/upload',
        rateLimiterMiddleware,
        authMiddleware('jwt'),
        uploadMiddleware,
        async (request, response, next) => {
            const now = app.datetime.local();
            const defaultCategory = await app.collection('kernel.file-categories').findOne({system: true});
            const files = [];
            let params = {};

            // Check if request files are provided.
            if (!Array.isArray(request.files) || request.files.length < 1) {
                return next(new app.errors.NotAcceptable('No files found to upload!'));
            }

            if (_.isPlainObject(request.body) && _.isString(request.body.params)) {
                params = EJSON.parse(request.body.params);
            }

            for (const uploaded of request.files || []) {
                const file = {};

                // Compress images
                // if (uploaded.mimetype === 'image/jpeg' || uploaded.mimetype === 'image/png') {
                //     // Transform image.
                //     const image = await fs.readFile(uploaded.path);
                //     const imagePath = path.join(
                //         path.dirname(uploaded.path),
                //         `${path.basename(uploaded.path, path.extname(uploaded.path))}.webp`
                //     );
                //     const transformed = sharp(image).webp({progressive: true});
                //     await fs.writeFile(imagePath, transformed);
                //     await fs.remove(uploaded.path);
                //
                //     // Prepare file meta.
                //     file.name = uploaded.originalname;
                //     file.extension = 'webp';
                //     file.contentType = 'image/webp';
                //     file.size = uploaded.size;
                //     file.path = imagePath;
                //     file.uploadedAt = now.toJSDate();
                //     if (!!defaultCategory) {
                //         file.categoryId = defaultCategory._id;
                //     }
                //     file.params = params;
                // } else {
                //     // Prepare file meta.
                //     file.name = uploaded.originalname;
                //     file.extension = mime.getExtension(uploaded.mimetype);
                //     file.contentType = uploaded.mimetype;
                //     file.size = uploaded.size;
                //     file.path = uploaded.path;
                //     file.uploadedAt = now.toJSDate();
                //     if (file.contentType === 'application/x-rar') {
                //         file.extension = 'rar';
                //     }
                //     if (!!defaultCategory) {
                //         file.categoryId = defaultCategory._id;
                //     }
                //     file.params = params;
                // }

                // Prepare file meta.
                file.name = uploaded.originalname;
                file.extension = mime.getExtension(uploaded.mimetype);
                file.contentType = uploaded.mimetype;
                file.size = uploaded.size;
                file.path = uploaded.path;
                file.uploadedAt = now.toJSDate();
                if (file.contentType === 'application/x-rar') {
                    file.extension = 'rar';
                }
                if (file.contentType === 'application/octet-stream' && uploaded.originalname.endsWith('.dwg')) {
                    file.extension = 'dwg';
                    file.contentType = 'application/dwg';
                }
                if (!!defaultCategory) {
                    file.categoryId = defaultCategory._id;
                }
                file.params = params;

                files.push(file);
            }

            // Create files meta documents.
            let result = null;
            try {
                result = await collection.create(files);
            } catch (error) {
                for (const file of files) {
                    await fs.remove(file.path);
                }

                return next(new app.errors.Unprocessable(error.message));
            }

            response.send(result);
        }
    );

    // Determine content types that we can serve.
    const contentTypesToServe = [
        'text/plain',
        'text/html',
        'image/bmp',
        'image/gif',
        'image/jpeg',
        'image/png',
        'image/webp',
        'image/svg+xml',
        'application/xml',
        'application/json',
        'application/pdf'
    ];

    // Serve files.
    app.get('/files/:id?', async (request, response, next) => {
        // Get file id.
        let id = (request.params.id || '').trim();
        if (!id && !request.query.name) {
            return next(new app.errors.NotAcceptable());
        }

        // Get file info.
        const fileQuery = {};
        if (request.query.name) {
            fileQuery.name = request.query.name;
        } else {
            fileQuery._id = id;
        }
        const fileInfo = await collection.findOne(fileQuery);
        if (_.isNull(fileInfo)) {
            return next(new app.errors.NotFound('Not found!'));
        }
        id = fileInfo._id;

        // Set content type.
        response.removeHeader('X-Frame-Options');
        response.removeHeader('Content-Security-Policy');
        response.removeHeader('Content-Security-Policy-Report-Only');
        response.removeHeader('Cross-Origin-Embedder-Policy');
        response.removeHeader('Cross-Origin-Resource-Policy');
        response.removeHeader('X-Permitted-Cross-Domain-Policies');
        response.removeHeader('Referrer-Policy');
        response.setHeader('X-Robots-Tag', 'noindex');
        if (!request.query.download && contentTypesToServe.indexOf(fileInfo.contentType) !== -1) {
            response.set(
                'Content-Disposition',
                `inline; filename*=UTF-8''${encodeRFC5987(path.basename(fileInfo.name, fileInfo.extension))}.${
                    fileInfo.extension
                }`
            );
            response.set('Content-Type', `${fileInfo.contentType}; charset=utf-8`);
            response.setHeader('Cache-Control', 'public, max-age=21600');
        } else {
            response.attachment(fileInfo.name);
        }

        try {
            // Create read stream.
            const readStream = await createReadStream(app, id, {
                fileInfo,
                query: request.query
            });

            // Pipe to response.
            readStream.pipe(response);

            // Handle streaming error.
            readStream.on('error', () => {
                next(new app.errors.NotFound('Not found!'));
            });
        } catch (error) {
            return next(new app.errors.NotFound('Not found!'));
        }
    });

    app.get('/temp-files/:file', async (request, response, next) => {
        // Get filename.
        let filename = (request.query.filename || '').trim();
        let filePath = (request.params.file || '').trim();

        if (!filePath || !filename) {
            return next(new app.errors.NotAcceptable());
        }
        filePath = path.join(app.config('paths.temp'), filePath);

        // Check if file exist.
        if (!(await fs.exists(filePath))) {
            return next(new app.errors.NotFound('Not found!'));
        }

        // Set headers.
        response.removeHeader('X-Frame-Options');
        response.removeHeader('Content-Security-Policy');
        response.removeHeader('Content-Security-Policy-Report-Only');
        response.removeHeader('Cross-Origin-Embedder-Policy');
        response.removeHeader('Cross-Origin-Resource-Policy');
        response.removeHeader('X-Permitted-Cross-Domain-Policies');
        response.removeHeader('Referrer-Policy');
        response.removeHeader('Referrer Policy');
        response.setHeader('X-Robots-Tag', 'noindex');

        response.download(filePath, filename, async error => {
            if (error) {
                console.error(error);
            }

            await fs.remove(filePath);
        });
    });
}

async function createReadStream(app, id, params) {
    const fileInfo = params.fileInfo;
    const query = params.query;
    const format = fileInfo.contentType.replace('image/', '');
    const width = parseInt(query.width, 10) || null;
    const height = parseInt(query.height, 10) || null;
    let readStream = null;
    let tempFile = null;

    if (
        ((_.isNumber(width) && !isNaN(width)) || (_.isNumber(height) && !isNaN(height))) &&
        sharp.format.hasOwnProperty(format)
    ) {
        const width = parseInt(query.width, 10) || null;
        const height = parseInt(query.height, 10) || null;
        const quality = parseInt(query.quality, 10) || null;
        const filename = fileInfo.name;
        let imageName = `${path.basename(filename, path.extname(filename))}_${id}`;
        if (_.isNumber(width)) imageName += `_w${width}`;
        if (_.isNumber(height)) imageName += `_h${height}`;
        if (_.isNumber(quality)) imageName += `_q${quality}`;
        imageName += path.extname(filename);
        const imagePath = path.join(app.config('paths.temp'), imageName);

        if (!(await fs.exists(imagePath))) {
            const image = await fs.readFile(fileInfo.path);
            const transformer = sharp(image);

            transformer.resize({
                width,
                height,
                fit: 'cover'
                // withoutEnlargement: true
            });

            const transformed = await transformer[format]({
                quality: quality ?? 90,
                progressive: true
            }).toBuffer();

            await fs.writeFile(imagePath, transformed);

            if (!!fileInfo.isTemporary && fileInfo.isTemporary === true) {
                tempFile = imagePath;
            }
        }

        readStream = fs.createReadStream(imagePath);
    } else {
        readStream = fs.createReadStream(fileInfo.path);
    }

    // Clean temporary file.
    if (readStream !== null && !!fileInfo.isTemporary && fileInfo.isTemporary === true) {
        readStream.on('end', async () => {
            app.collection('kernel.files').remove(fileInfo._id);

            if (!_.isNull(tempFile)) {
                fs.unlink(tempFile);
            }
        });
    }

    return readStream;
}
