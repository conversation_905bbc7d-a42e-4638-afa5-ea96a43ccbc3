import redis from 'redis';
import Queue from 'bee-queue';

export default function (app) {
    const sharedConfig = {
        getEvents: false,
        sendEvents: false,
        isWorker: true,
        storeJobs: false,
        removeOnSuccess: true,
        removeOnFailure: true,
        activateDelayedJobs: true,
        redis: redis.createClient()
    };
    const queuesMap = {};

    app.registerQueuedJob = definition => {
        const queue = new Queue(definition.name, sharedConfig);

        queue.process(async job => {
            return await definition.action(app, job.data || {});
        });

        queuesMap[definition.name] = queue;
    };

    app.addQueuedJob = (name, data = {}, options = {}) => {
        const queue = queuesMap[name];
        if (!queue) {
            console.error(`Can not find queue with the name ${name}`);
            return;
        }

        const job = queue.createJob(data);

        if (typeof options.timeout === 'number') {
            job.timeout(options.timeout);
        }
        if (typeof options.retries === 'number') {
            job.retries(options.retries);
        }
        if (typeof options.delayUntil !== 'undefined') {
            job.delayUntil(options.delayUntil);
        }

        return job.save();
    };
}
