import path from 'path';
import fs from 'fs-extra';
import _ from 'lodash';
import mime from 'mime';
import sharp from 'sharp';
import Mailer from 'framework/mailer';
import {template} from 'framework/helpers';

export default async function (app) {
    const mailer = new Mailer();

    app.set('mail.host', app.config('mail.host'));
    app.set('mail.port', app.config('mail.port'));
    app.set('mail.secure', app.config('mail.secure'));
    app.set('mail.auth.user', app.config('mail.auth.user'));
    app.set('mail.auth.password', app.config('mail.auth.password'));

    app.mail = async params => {
        const company = await app.collection('kernel.company').findOne({});

        if (!params.from) {
            params.from = `${company.name} <${company.email}>`;
        }

        if (params.layout !== false) {
            const mjml = require('mjml');
            const payload = _.defaultsDeep(params.payload || {}, {
                title: params.subject || '',
                subTitle: '',
                topTitle: '',
                content: '',
                company: {
                    logo: company.logo,
                    name: company.name,
                    legalName: company.legalName,
                    phone: company.phone,
                    email: company.email,
                    website: company.website
                }
            });

            // Convert logo.
            let logo = null;
            let logoImageType = null;
            if (!!payload.company.logo) {
                if (payload.company.logo.indexOf('static') !== -1) {
                    const logoPath = path.join(process.cwd(), payload.company.logo);

                    logo = await fs.readFile(logoPath);
                    logoImageType = mime.getType(logoPath);
                } else {
                    let logoFileInfo = await app.files.findOne({
                        _id: payload.company.logo
                    });

                    logo = await app.files.read(payload.company.logo);
                    logoImageType = logoFileInfo.contentType;
                }
            } else {
                const logoPath = path.join(process.cwd(), '/static/images/default-logo.png');

                logo = await fs.readFile(logoPath);
                logoImageType = mime.getType(logoPath);
            }
            const logoAsBase64 = (
                await sharp(logo)
                    .resize({
                        height: 35,
                        fit: 'outside',
                        withoutEnlargement: true
                    })
                    .toBuffer()
            ).toString('base64');
            payload.company.logo = `data:${logoImageType};base64,${logoAsBase64}`;
            // let logoFileInfo = null;
            // let logo = null;
            // if (payload.company.logo) {
            //     logoFileInfo = await app.files.findOne({_id: payload.company.logo});
            //
            //     logo = await app.files.read(payload.company.logo);
            // } else {
            //     logoFileInfo = await app.files.findOne({filename: 'system/default-logo.png'});
            //
            //     logo = await app.files.read(logoFileInfo._id);
            // }
            // const logoImageType = logoFileInfo.contentType;
            // const logoAsBase64 = (await sharp(logo).resize({
            //     height: 35,
            //     fit: 'outside',
            //     withoutEnlargement: true
            // }).toBuffer()).toString('base64');
            // payload.company.logo = `data:${logoImageType};base64,${logoAsBase64}`;

            if (params.html) {
                payload.content = `<mj-column><mj-raw><div style="font-size: 14px;">${params.html}</div></mj-raw></mj-column>`;

                delete params.html;
            } else if (params.text) {
                payload.content = `<mj-column><mj-text align="left">${params.text}</mj-text></mj-column>`;

                delete params.text;
            } else if (params.template) {
                payload.content = params.template;

                delete params.template;
            }

            const result = mjml(
                template(
                    `
<mjml>
    <mj-head>
        <mj-attributes>
            <mj-text padding="7.5px 0px"
                     font-family="Helvetica Neue"
                     font-size="14px"
                     font-weight="400"
                     line-height="19px"
                     color="#333"/>
            <mj-section padding="0px" background-color="#fff"/>
            <mj-column padding="0px"/>
            <mj-divider padding="0px"/>
            <mj-button font-family="Helvetica Neue"
                       font-size="13px"
                       padding="10px 15px 10px 15px"
                       border-radius="4px"
                       align="left"
                       background-color="#2196F3"
                       color="#fff"/>
        </mj-attributes>

        <mj-style>
            body {
            width: 100%;
            height: 100%;
            padding: 30px;
            margin: 0;
            background-color: #f4f4f4;
            font-family: "Helvetica Neue", Arial, sans-serif !important;
            font-weight: 400;
            font-size: 14px;
            color: #333;
            }

            .company-logo img {
            height: 35px !important;
            width: auto !important;
            float: right;
            }
        </mj-style>
    </mj-head>
    <mj-body>
        <!-- Header -->
        <mj-section padding="20px 30px 20px 30px">
            <mj-group>
                <mj-column width="80%">
                    {# if (topTitle) { }}
                    <mj-text padding="0px 0px 5px 0px" font-size="12px" color="#999" align="left">{{topTitle}}</mj-text>
                    {# } }}

                    <mj-text padding="0px" font-size="18px" font-weight=600 align="left">{{title}}</mj-text>

                    {# if (subTitle) { }}
                    <mj-text padding="7.5px 0px 0px 0px" font-size="12px" color="#999" align="left">{{subTitle}}
                    </mj-text>
                    {# } }}
                </mj-column>

                <mj-column width="20%">
                    {# if (company.logo) { }}
                    <mj-image src="{{company.logo}}"
                              css-class="company-logo"
                              padding="0"
                              align="right"
                    />
                    {# } }}
                </mj-column>
            </mj-group>
        </mj-section>

        <mj-section>
            <mj-column padding="0px 30px 0px 30px">
                <mj-divider border-width="1px" border-color="#dcdfe6"/>
            </mj-column>
        </mj-section>

        <!-- Content -->
        {# if (content) { }}
        <mj-section padding="30px">{_content}}}</mj-section>
        {# } }}

        <mj-section>
            <mj-column padding="0px 30px 0px 30px">
                <mj-divider border-width="1px" border-color="#dcdfe6"/>
            </mj-column>
        </mj-section>

        <!-- Footer -->
        <mj-section padding="15px 30px 15px 30px">
            <mj-column>
                <mj-text padding="0px" font-size="13px" color="#666" align="left">
                    {{company.legalName ? company.legalName : company.name}}
                </mj-text>
                <mj-text padding="2.5px 0px 0px 0px" font-size="12px" color="#999" align="left">
                    {# if (company.email) { }}
                    <a style="text-decoration: none; color: #999;" href="mailto:{{company.email}}">{{company.email}}</a>
                    {# } }}
                    {# if (company.website) { }}
                    | <a style="text-decoration: none; color: #999;" href="{{company.website}}">{{company.website}}</a>
                    {# } }}
                    {# if (company.phone) { }}
                    | <a style="text-decoration: none; color: #999;" href="tel:{{company.phone}}">{{company.phone}}</a>
                    {# } }}
                </mj-text>
            </mj-column>
        </mj-section>

        <!-- Copyright -->
        <mj-section padding="10px 30px 10px 30px" background-color="#f4f4f4">
            <mj-column>
                <mj-text padding="0px" font-size="11px" color="#666" align="center">
                    Powered by <a href="http://www.enteryazilim.com.tr/products/entererp" style="color: #2196F3;">EnterERP</a>
                </mj-text>
            </mj-column>
        </mj-section>
    </mj-body>
</mjml>
`.trim(),
                    payload
                ),
                {fonts: {}}
            );

            if (result.errors.length < 1) {
                params.html = result.html;
            } else {
                throw new Error('An error occurred while parsing mail template! Error: ' + result.errors[0].message);
            }
        }

        return mailer.send(params);
    };

    app.initMailer = async () => {
        let configUpdated = false;

        if (
            (app.setting('system.mailHost') && app.setting('system.mailHost') !== app.get('mail.host')) ||
            (app.setting('system.mailPort') && app.setting('system.mailPort') !== app.get('mail.port')) ||
            (_.isBoolean(app.setting('system.mailSecure')) &&
                app.setting('system.mailSecure') !== app.get('mail.secure')) ||
            (app.setting('system.mailAuthUser') && app.setting('system.mailAuthUser') !== app.get('mail.auth.user')) ||
            (app.setting('system.mailAuthPassword') &&
                app.setting('system.mailAuthPassword') !== app.get('mail.auth.password'))
        ) {
            app.set('mail.host', app.setting('system.mailHost'));
            app.set('mail.port', app.setting('system.mailPort'));
            app.set('mail.secure', app.setting('system.mailSecure'));
            app.set('mail.auth.user', app.setting('system.mailAuthUser'));
            app.set('mail.auth.password', app.setting('system.mailAuthPassword'));

            configUpdated = true;
        }

        if (
            app.get('mail.host') &&
            app.get('mail.port') &&
            app.get('mail.auth.user') &&
            app.get('mail.auth.password') &&
            (!mailer.isVerified || configUpdated)
        ) {
            const config = {
                host: app.get('mail.host'),
                port: app.get('mail.port'),
                secure: app.get('mail.secure'),
                user: app.get('mail.auth.user'),
                password: app.get('mail.auth.password')
            };

            if (!config.secure) {
                config.tls = {
                    requireTLS: false,
                    ignoreTLS: true,
                    rejectUnauthorized: false
                };
            }

            configUpdated = false;

            return mailer.connect(config);
        }
    };
}
