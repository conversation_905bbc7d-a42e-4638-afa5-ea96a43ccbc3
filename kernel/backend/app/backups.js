import path from 'path';
import os from 'os';
import fs from 'fs-extra';
import _ from 'lodash';
import axios from 'axios';

class BackupManager {
    constructor(app) {
        this.app = app;
        this.collection = app.collection('kernel.backups');
        this.backupsPath = path.join(process.cwd(), '../entererp-backups');
        this.socket = null;
    }

    async create(description = 'Manuel backup', userName = null, email = null) {
        this._inform('info', 'Starting the backup process.');

        // Get current date.
        const now = this.app.datetime.local();

        // Get backup name.
        const backupName = `entererp-backup-${now.toFormat('yyyyLLddHHmm')}`;

        // Get paths.
        const rootPath = process.cwd();
        const backupsPath = this.backupsPath;
        const backupPath = path.join(backupsPath, `${backupName}.zip`);
        const tempPath = await fs.realpath(os.tmpdir());
        const tempBackupPath = path.join(tempPath, backupName);

        try {
            // Create directories.
            this._inform('info', 'Checking backup directory structure.');
            await fs.ensureDir(path.join(backupsPath));
            await fs.ensureDir(path.join(tempBackupPath));

            // Backup database.
            this._inform('info', 'Backing up the database.');
            await this.app.exec(
                `mongodump --quiet --host ${this.app.config('database.mongodb.host')}:${this.app.config(
                    'database.mongodb.port'
                )} -d "${this.app.config('database.mongodb.db')}" --gzip --archive=${path.join(
                    tempBackupPath,
                    'entererp.db'
                )}`
            );

            // Backup files.
            // this._inform('info', 'Backing up files.');
            // await fs.copy(rootPath, tempBackupPath);

            // Remove node_modules folder.
            // await fs.remove(path.join(tempBackupPath, 'node_modules'));

            // Compress and store backup.
            this._inform('info', 'Compressing the backup files.');
            await this.app.archive(tempBackupPath, backupPath);

            // Remove temp files.
            this._inform('info', 'Cleaning the temporary backup files.');
            await fs.remove(tempBackupPath);

            // Persist meta.
            const meta = {};
            meta.date = now.toJSDate();
            meta.name = backupName;
            meta.userName = userName ? userName : this.app.translate('System');
            if (email) meta.email = email;
            meta.description = this.app.translate(description);
            meta.path = backupPath;
            await this.collection.create(meta);

            this._inform('success', 'Backup process is completed successfully.');
        } catch (error) {
            this._inform('error', 'Backup process is failed.');

            throw error;
        }
    }

    async restore(id) {
        this._inform('info', 'Starting the restore process.');

        try {
            // Get meta.
            this._inform('info', 'Fetching the backup record.');
            const meta = await this.collection.get(id);

            // Get paths.
            const rootPath = process.cwd();
            const backupsPath = this.backupsPath;
            const backupPath = path.join(backupsPath, `${meta.name}.zip`);
            const tempPath = await fs.realpath(os.tmpdir());
            const tempBackupPath = path.join(tempPath, meta.name);

            // Extract backup.
            this._inform('info', 'Extracting the backup files.');
            await this.app.extract(backupPath, tempBackupPath);

            // Drop database.
            this._inform('info', 'Restoring the database.');
            await this.app.db.dropDatabase();

            // Restore database.
            await this.app.exec(
                `mongorestore --quiet --host ${this.app.config('database.mongodb.host')} --port ${this.app.config(
                    'database.mongodb.port'
                )} -d ${this.app.config('database.mongodb.db')} --archive=${path.join(
                    tempBackupPath,
                    'entererp.db'
                )} --gzip --drop --maintainInsertionOrder --numParallelCollections=1 --stopOnError`
            );

            // Clean app folder.
            // this._inform('info', 'Cleaning the app folder.');
            // await fs.remove(rootPath);
            // await fs.ensureDir(rootPath);

            // Copy backup files.
            // this._inform('info', 'Copying the backup files to the app folder.');
            // await fs.copy(tempBackupPath, rootPath);

            // Install node modules.
            // await this.app.exec('yarn install --production', {
            //     cwd: '/opt/entererp'
            // });

            // Delete db backup.
            this._inform('info', 'Cleaning the temporary backup files.');
            await fs.remove(path.join(rootPath, 'entererp.db'));

            // Remove temp files.
            await fs.remove(tempBackupPath);

            this._inform('success', 'Restore process is completed successfully.');

            if (!_.isNull(this.socket)) {
                this.socket.emit('kernel:backup.restore-completed');
            }

            // Reload server.
            await axios.get('http://localhost:2999/maintenance');
        } catch (error) {
            this._inform('error', 'Restore process is failed.');
        }
    }

    async remove(id) {
        // Get meta.
        const meta = await this.collection.get(id);

        // Remove file.
        this._inform('info', 'Removing backup files.');
        try {
            await fs.remove(meta.path);
        } catch (error) {
            console.log('Backup remove error:', error.message);
        }

        // Remove meta.
        this._inform('info', 'Removing backup records.');
        await this.collection.remove(id);

        this._inform('success', 'Backup removed successfully.');
    }

    async removeAll() {
        // Remove files.
        this._inform('info', 'Removing backups.');
        try {
            await fs.remove(this.backupsPath);
        } catch (error) {}

        // Remove meta.
        this._inform('info', 'Removing backup records.');
        await this.collection.remove({});

        this._inform('success', 'Backup removed successfully.');
    }

    _inform(status, message) {
        if (!_.isNull(this.socket)) {
            this.socket.emit('kernel:backup.info', {status, message});
        }
    }
}

export default function (app) {
    app.backups = new BackupManager(app);

    app.once('socketio-initialized', () => {
        app.io.on('connection', socket => {
            const manager = new BackupManager(app);
            const error = new app.errors.Forbidden(
                app.translate('You do not have sufficient permissions to perform this operation!')
            );

            socket.on('kernel:backup', async payload => {
                const user = (socket.feathers || {}).user;

                manager.socket = socket;

                if (!_.isObject(user) || _.isEmpty(user)) {
                    throw error;
                }

                // Disallow un-authenticated request.
                if (!_.isObject(user) || _.isEmpty(user)) {
                    throw error;
                }

                // Get user record permission.
                const permission = (user.recordPermissions || []).find(p => p.name === 'kernel.backups');

                // Check if the user has any permission for this collection.
                if (!user.isRoot && !_.isObject(permission)) {
                    throw error;
                }

                if (payload.method === 'create') {
                    if (!user.isRoot && permission.create === 'no') {
                        throw error;
                    }

                    await manager.create('Manuel backup', user.name, user.email);

                    app.log({
                        level: 'success',
                        message: app.translate('Backup created successfully.'),
                        user: {
                            name: user.name,
                            email: user.email
                        }
                    });
                } else if (payload.method === 'restore') {
                    if (!user.isRoot && permission.create === 'no') {
                        throw error;
                    }

                    await manager.restore(payload.id);

                    app.log({
                        level: 'success',
                        message: app.translate('Backup restored successfully.'),
                        user: {
                            name: user.name,
                            email: user.email
                        }
                    });
                } else if (payload.method === 'remove') {
                    if (!user.isRoot && permission.remove === 'no') {
                        throw error;
                    }

                    await manager.remove(payload.id);

                    app.log({
                        level: 'success',
                        message: app.translate('Backup removed successfully.'),
                        user: {
                            name: user.name,
                            email: user.email
                        }
                    });
                } else if (payload.method === 'removeAll') {
                    if (!user.isRoot && permission.remove === 'no') {
                        throw error;
                    }

                    await manager.removeAll();

                    app.log({
                        level: 'success',
                        message: app.translate('All the backups removed successfully.'),
                        user: {
                            name: user.name,
                            email: user.email
                        }
                    });
                }
            });
        });
    });
}
