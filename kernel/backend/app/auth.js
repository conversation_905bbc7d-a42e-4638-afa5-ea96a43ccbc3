import _ from 'lodash';
import {AuthenticationService, JWTStrategy} from '@feathersjs/authentication';
import {LocalStrategy} from '@feathersjs/authentication-local';
import UAParser from 'ua-parser-js';
import lookupIp from 'framework/helpers/lookup-ip';

export default function (app) {
    app.set('authentication', {
        secret: 'oRfzaDHsy8lCN0OW2iqzWrMYgVeP0JUT',
        entity: 'user',
        service: 'kernel/users',
        authStrategies: ['jwt', 'local'],
        jwtOptions: {
            header: {
                typ: 'access'
            },
            audience: app.absoluteUrl(),
            issuer: 'entererp',
            algorithm: 'HS256',
            expiresIn: '168h'
        },
        local: {
            entity: 'user',
            usernameField: 'email',
            passwordField: 'password'
        }
    });

    const authentication = new AuthenticationService(app);
    authentication.register('jwt', new JWTStrategy());
    authentication.register('local', new LocalStrategy());

    app.use('/authentication', authentication);

    // The `authentication` service is used to create a JWT.
    // The before `create` hook registers strategies that can be used
    // to create a new valid JWT (e.g. local or oauth2)
    app.service('authentication').hooks({
        before: {
            create: [checkAuthAttempts, validateCredentials]
        },
        after: {
            create: [checkAuthAttempts]
        }
    });

    const publisher = app.cache.client;

    app.on('login', async (result, params) => {
        const {user} = result;
        const {connection} = params;
        const ua = new UAParser(connection.headers['user-agent']).getResult();

        // Session.
        const session = {};
        session.userId = user._id;
        session.user = _.pick(user, ['code', 'name', 'email']);
        session.startDate = app.datetime.local().toJSDate();
        session.lastActiveDate = app.datetime.local().toJSDate();
        session.browserName = ua.browser.name;
        session.browserVersion = ua.browser.version;
        session.deviceType = ua.device.type || 'desktop';
        session.deviceVendor = ua.device.vendor || 'Unknown';
        session.deviceModel = ua.device.model || 'Unknown';
        session.osName = ua.os.name;
        session.osVersion = ua.os.version;
        session.ip = params.ip || connection.ip;
        session.socketId = connection.socketId;
        session.isOnline = true;
        if (session.deviceType !== 'desktop' && session.deviceType !== 'mobile' && session.deviceType !== 'tablet') {
            session.deviceType = 'other';
        }
        if (!!session.ip) {
            let ipLookup = null;
            try {
                ipLookup = await lookupIp(session.ip);
            } catch (error) {}

            if (!!ipLookup) {
                session.isp = ipLookup.isp;
                session.countryCode = ipLookup.countryCode;
                session.city = ipLookup.city;
            }
        }
        const existingSession = await app.collection('kernel.sessions').findOne({
            userId: session.userId,
            browserName: session.browserName,
            browserVersion: session.browserVersion,
            deviceType: session.deviceType,
            ip: session.ip,
            isOnline: true,
            $select: ['_id']
        });
        if (_.isPlainObject(existingSession)) {
            app.collection('kernel.sessions').patch({_id: existingSession._id}, session, {disableValidation: true});
        } else {
            app.collection('kernel.sessions').create(session, {
                disableValidation: true
            });
        }

        // Log login.
        const log = {};
        log.level = 'info';
        log.message = params.fromMobile
            ? app.translate('{{userName}} has successfully logged in from mobile.', {userName: user.name})
            : app.translate('{{userName}} has successfully logged in.', {
                  userName: user.name
              });
        log.userId = user._id;
        log.user = _.pick(user, ['name', 'email']);
        app.log(log);
    });
    app.on('logout', (result, params) => {
        const {user} = result;

        publisher.publish('user-logout', result.authentication.accessToken);

        // Session.
        (async () => {
            const ua = new UAParser(params.connection.headers['user-agent']).getResult();
            let deviceType = ua.device.type || 'desktop';
            if (deviceType !== 'desktop' && deviceType !== 'mobile' && deviceType !== 'tablet') {
                deviceType = 'other';
            }

            const session = await app.collection('kernel.sessions').findOne({
                userId: user._id,
                deviceType,
                browserName: ua.browser.name,
                browserVersion: ua.browser.version,
                isOnline: true,
                $select: ['_id']
            });

            if (_.isPlainObject(session)) {
                await app.collection('kernel.sessions').patch(
                    {_id: session._id},
                    {
                        endDate: app.datetime.local().toJSDate(),
                        isOnline: false
                    },
                    {disableValidation: true}
                );
            }
        })();

        // Log logout.
        const log = {};
        log.level = 'info';
        log.message = app.translate('{{userName}} has successfully logged out.', {userName: user.name});
        log.userId = user._id;
        log.user = _.pick(user, ['name', 'email']);
        log.platform = params.fromMobile ? 'mobile' : 'web';
        app.log(log);

        app.rpc('kernel.common.cancel-document-editing', {userId: user._id});
    });

    app.on('connection', connection => {
        // Session.
        if (!!connection.user) {
            (async () => {
                const ua = new UAParser(connection.headers['user-agent']).getResult();
                let deviceType = ua.device.type || 'desktop';
                if (deviceType !== 'desktop' && deviceType !== 'mobile' && deviceType !== 'tablet') {
                    deviceType = 'other';
                }

                const session = await app.collection('kernel.sessions').findOne({
                    userId: connection.user._id,
                    deviceType,
                    browserName: ua.browser.name,
                    browserVersion: ua.browser.version,
                    isOnline: false,
                    $sort: {lastActiveDate: -1},
                    $select: ['_id']
                });

                if (_.isPlainObject(session)) {
                    const payload = {
                        lastActiveDate: app.datetime.local().toJSDate(),
                        isOnline: true
                    };

                    if (!!connection.ip) {
                        let ipLookup = null;
                        try {
                            ipLookup = await lookupIp(connection.ip);
                        } catch (error) {}

                        if (!!ipLookup) {
                            payload.isp = ipLookup.isp;
                            payload.countryCode = ipLookup.countryCode;
                            payload.city = ipLookup.city;
                        }
                    }

                    await app
                        .collection('kernel.sessions')
                        .patch({_id: session._id}, payload, {disableValidation: true});
                } else {
                    const session = {};

                    session.userId = connection.user._id;
                    session.user = _.pick(connection.user, ['code', 'name', 'email']);
                    session.startDate = app.datetime.local().toJSDate();
                    session.lastActiveDate = app.datetime.local().toJSDate();
                    session.browserName = ua.browser.name;
                    session.browserVersion = ua.browser.version;
                    session.deviceType = ua.device.type || 'desktop';
                    session.deviceVendor = ua.device.vendor || 'Unknown';
                    session.deviceModel = ua.device.model || 'Unknown';
                    session.osName = ua.os.name;
                    session.osVersion = ua.os.version;
                    session.ip = connection.ip;
                    session.socketId = connection.socketId;
                    session.isOnline = true;
                    if (
                        session.deviceType !== 'desktop' &&
                        session.deviceType !== 'mobile' &&
                        session.deviceType !== 'tablet'
                    ) {
                        session.deviceType = 'other';
                    }

                    if (!!connection.ip) {
                        let ipLookup = null;
                        try {
                            ipLookup = await lookupIp(connection.ip);
                        } catch (error) {}

                        if (!!ipLookup) {
                            session.isp = ipLookup.isp;
                            session.countryCode = ipLookup.countryCode;
                            session.city = ipLookup.city;
                        }
                    }

                    app.collection('kernel.sessions').create(session, {
                        disableValidation: true
                    });
                }
            })();
        }
    });

    app.on('disconnect', connection => {
        // Session.
        if (!!connection.user) {
            (async () => {
                const ua = new UAParser(connection.headers['user-agent']).getResult();
                let deviceType = ua.device.type || 'desktop';
                if (deviceType !== 'desktop' && deviceType !== 'mobile' && deviceType !== 'tablet') {
                    deviceType = 'other';
                }

                const session = await app.collection('kernel.sessions').findOne({
                    userId: connection.user._id,
                    deviceType,
                    browserName: ua.browser.name,
                    browserVersion: ua.browser.version,
                    isOnline: true,
                    $select: ['_id']
                });

                if (_.isPlainObject(session)) {
                    await app.collection('kernel.sessions').patch(
                        {_id: session._id},
                        {
                            endDate: app.datetime.local().toJSDate(),
                            isOnline: false
                        },
                        {disableValidation: true}
                    );
                }
            })();

            app.rpc('kernel.common.cancel-document-editing', {userId: connection.user._id});
        }
    });
}

async function checkAuthAttempts(context) {
    if (!!context.params.skipAuthAttemptsCheck) {
        delete context.params.skipAuthAttemptsCheck;

        return context;
    }

    const app = context.app;
    const data = context.data;
    const identifier = data.accessToken ? data.accessToken : data.email;
    const attemptKey = `authAttempt-${identifier}`;

    if (context.type === 'before') {
        const now = app.datetime.local();
        let attempt = null;

        attempt = await app.cache.get(attemptKey);

        if (attempt !== null && _.isPlainObject(attempt)) {
            if (attempt.count > 4) {
                const attemptDate = app.datetime.fromJSDate(attempt.date);
                const diff = now.diff(attemptDate).as('minutes');
                const remaining = 15 - diff;

                if (remaining <= 0) {
                    await app.cache.set(attemptKey, {
                        count: 1,
                        date: now.toJSDate()
                    });
                } else {
                    if (remaining < 1) {
                        const remainingSeconds = remaining * 60;

                        throw new app.errors.Unprocessable(
                            app.translate('Your access is blocked for {{seconds}} second(s)!', {
                                seconds: Math.ceil(remainingSeconds)
                            })
                        );
                    } else {
                        throw new app.errors.Unprocessable(
                            app.translate('Your access is blocked for {{minutes}} minute(s)!', {
                                minutes: Math.ceil(remaining)
                            })
                        );
                    }
                }
            } else {
                await app.cache.set(attemptKey, {
                    count: attempt.count + 1,
                    date: attempt.date
                });
            }
        } else {
            await app.cache.set(attemptKey, {
                count: 1,
                date: now.toJSDate()
            });
        }

        // Check license.
        const license = app.get('license');
        if (!!license) {
            if (license.status !== 'active') {
                throw new app.errors.Unprocessable(app.translate(license.reason));
            }

            const fromApi = !!context.params.fromApi;

            let headers = null;
            if (!!context.params) {
                if (!!context.params.connection && !!context.params.connection.headers) {
                    headers = context.params.connection.headers;
                } else if (!!context.params.headers) {
                    headers = context.params.headers;
                }
            }
            if (headers !== null) {
                const onlineUserCount = license.onlineUserCount;
                const onlineMobileUserCount = license.onlineMobileUserCount;
                const deviceType = new UAParser(headers['user-agent']).getResult().device.type || 'desktop';

                // TODO -> Also check online mobile user count. There are mobile disconnection problems.
                if (deviceType === 'desktop') {
                    const sessions = await app.collection('kernel.sessions').find({
                        isOnline: true,
                        deviceType: 'desktop',
                        $select: ['_id', 'user', 'deviceType']
                    });

                    if (!fromApi && sessions.filter(s => s.user.email === data.email).length > 0) {
                        throw new app.errors.Unprocessable(
                            app.translate('A user cannot be logged in with multiple devices of the same type!')
                        );
                    }

                    if (onlineUserCount < sessions.length + 1) {
                        throw new app.errors.Unprocessable(
                            app.translate('Login failed due to reaching maximum number of allowed online users!')
                        );
                    }

                    // if (onlineMobileUserCount < sessions.filter(s => s.deviceType !== 'desktop').length + 1) {
                    //     throw new app.errors.Unprocessable(app.translate('Login failed due to reaching maximum number of allowed online mobile users!'));
                    // }
                }
            }
        }
    } else if (context.type === 'after') {
        if (!!context.result.user) {
            const user = context.result.user;
            const validityPeriod = user.validityPeriod;
            const createdAt = user.createdAt;

            if (_.isNumber(validityPeriod) && _.isDate(createdAt)) {
                const start = app.datetime.fromJSDate(createdAt);
                const now = app.datetime.local();

                if (now.diff(start).as('days') > validityPeriod) {
                    throw new Error(
                        context.app.translate("Unable to login because the user's usage period has expired!")
                    );
                }
            }
        }

        await app.cache.delete(attemptKey);
    }

    return context;
}

// const authAttempts = {};
//
// function checkAuthAttempts(context) {
//     const app = context.app;
//     const data = context.data;
//     const identifier = data.accessToken ? data.accessToken : data.email;
//
//     if (context.type === 'before') {
//         const now = app.datetime.local();
//
//         if (_.isObject(authAttempts[identifier])) {
//             const attempt = authAttempts[identifier];
//
//             if (attempt.count > 4) {
//                 const attemptDate = app.datetime.fromJSDate(attempt.date);
//                 const diff = now.diff(attemptDate).as('minutes');
//                 const remaining = 15 - diff;
//
//                 if (remaining <= 0) {
//                     authAttempts[identifier] = {
//                         count: 1,
//                         date: now.toJSDate()
//                     };
//                 } else {
//                     if (remaining < 1) {
//                         const remainingSeconds = remaining * 60;
//
//                         throw new app.errors.Unprocessable(app.translate('Your access is blocked for {{seconds}} second(s)!', {seconds: Math.ceil(remainingSeconds)}));
//                     } else {
//                         throw new app.errors.Unprocessable(app.translate('Your access is blocked for {{minutes}} minute(s)!', {minutes: Math.ceil(remaining)}));
//                     }
//                 }
//             } else {
//                 authAttempts[identifier].count++;
//             }
//         } else {
//             authAttempts[identifier] = {
//                 count: 1,
//                 date: now.toJSDate()
//             };
//         }
//     } else if (context.type === 'after') {
//         delete authAttempts[identifier];
//     }
//
//     return context;
// }

async function validateCredentials(context) {
    const app = context.app;

    if (!context.data.accessToken) {
        const schema = {
            strategy: {
                type: 'string'
            },
            email: {
                type: 'string',
                regexp: 'EmailWithTLD'
            },
            password: {
                type: 'string',
                min: 4
            }
        };

        const validation = await app.schema.validate(schema, context.data);
        if (!validation.isValid) {
            throw new app.errors.Unprocessable(app.translate('Invalid email or password!'));
        }
    }

    return context;
}
