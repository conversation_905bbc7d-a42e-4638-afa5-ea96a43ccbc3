import fs from 'fs-extra';
import path from 'path';

export default async function (app, request, response) {
    const html = await fs.readFile(path.join(app.config('paths.static'), 'templates/scalar/scalar.html'), {
        encoding: 'utf8'
    });

    if (app.get('isProduction')) {
        response.setHeader('Cache-Control', 'public, max-age=2592000');
    }

    response.setHeader('X-Robots-Tag', 'noindex');

    return response.send(html);
}
