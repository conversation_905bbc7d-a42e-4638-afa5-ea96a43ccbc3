export default async function (app) {
    const common = {
        openapi: '3.1.0',
        info: {
            title: 'EnterERP API',
            description: 'EnterERP rest api endpoints',
            version: '1.0.0'
        },
        security: [{BearerJwt: []}],
        paths: {
            '/api/v1/authenticate': {
                post: {
                    operationId: 'authenticate',
                    tags: ['Authentication'],
                    summary: 'Authenticate',
                    description: 'Authenticate to get the api token.',
                    security: [],
                    requestBody: {
                        required: true,
                        description: '',
                        content: {
                            'application/json': {
                                schema: {
                                    type: 'object',
                                    properties: {
                                        email: {type: 'string'},
                                        password: {type: 'string'}
                                    },
                                    required: ['email', 'password']
                                }
                            }
                        }
                    },
                    responses: {
                        200: {
                            description: 'The request was successful.',
                            content: {
                                'application/json': {
                                    schema: {
                                        type: 'object',
                                        properties: {
                                            token: {type: 'string'},
                                            tokenExpireAt: {type: 'string'},
                                            userId: {type: 'string'}
                                        },
                                        required: ['token', 'tokenExpireAt', 'userId']
                                    }
                                }
                            }
                        },
                        401: {
                            $ref: '#/components/responses/Unauthorized'
                        }
                    }
                }
            }
        },
        components: {
            parameters: {
                id: {
                    name: 'id',
                    in: 'path',
                    required: true,
                    schema: {
                        type: 'string'
                    }
                }
            },
            responses: {
                BadRequest: {
                    description:
                        'The server could not understand the request due to invalid syntax. The client should modify the request and try again.',
                    content: {
                        'application/json': {
                            schema: {
                                type: 'object',
                                properties: {
                                    status: {type: 'string'},
                                    code: {type: 'string'},
                                    message: {type: 'string'}
                                },
                                required: ['status', 'message']
                            }
                        }
                    }
                },
                Unauthorized: {
                    description:
                        'Authentication is required to access the requested resource. The client must include the appropriate credentials.',
                    content: {
                        'application/json': {
                            schema: {
                                type: 'object',
                                properties: {
                                    status: {type: 'string'},
                                    code: {type: 'string'},
                                    message: {type: 'string'}
                                },
                                required: ['status', 'message']
                            }
                        }
                    }
                },
                NotFound: {
                    description:
                        'The server cannot find the requested resource. The endpoint may be invalid or the resource may no longer exist.',
                    content: {
                        'application/json': {
                            schema: {
                                type: 'object',
                                properties: {
                                    status: {type: 'string'},
                                    code: {type: 'string'},
                                    message: {type: 'string'}
                                },
                                required: ['status', 'message']
                            }
                        }
                    }
                },
                InternalServerError: {
                    description:
                        'The server encountered an unexpected condition that prevented it from fulfilling the request. Report the issue to the support team if it persists.',
                    content: {
                        'application/json': {
                            schema: {
                                type: 'object',
                                properties: {
                                    status: {type: 'string'},
                                    code: {type: 'string'},
                                    message: {type: 'string'}
                                },
                                required: ['status', 'message']
                            }
                        }
                    }
                }
            },
            securitySchemes: {
                BearerJwt: {
                    type: 'http',
                    scheme: 'Bearer',
                    bearerFormat: 'Json Web Token (JWT)',
                    description:
                        'Jwt Auth: Authenticated requests contain a valid Json Web Token (JWT) as part of the `Authorization: Bearer <JWT>` header.'
                }
            }
        },
        tags: [{name: 'Authentication'}]
    };

    return common;
}
