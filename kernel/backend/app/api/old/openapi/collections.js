import _ from 'lodash';
import pluralize from 'pluralize';
import {firstUpper} from 'framework/helpers';

const excludedPrograms = ['local-tr'];

export default async function (app) {
    const publicParams = app.get('publicParams');
    const api = {
        paths: {},
        components: {
            schemas: {}
        },
        tags: [],
        'x-tagGroups': []
    };

    for (const program of publicParams.programs ?? []) {
        if ((program.collections ?? []).length < 1 || excludedPrograms.includes(program.name)) {
            continue;
        }

        let moduleTitle = program.title;
        let moduleName = program.name;
        if (program.name === 'kernel') {
            moduleTitle = 'System';
            moduleName = 'system';
        }

        const tagName = moduleTitle;
        const tagDisplayName = moduleTitle;
        const subTags = [];

        for (const collection of program.collections ?? []) {
            const subTagName = _.camelCase(collection.name);
            const subTagDisplayName = _.snakeCase(collection.name.replace(`${program.name}.`, ''))
                .split('_')
                .map(p => firstUpper(p, 'en'))
                .join(' ');
            let schemaName = '';
            if (program.name === 'kernel') {
                schemaName = pluralize.singular(
                    _.snakeCase(`system.${collection.name.replace(`${program.name}.`, '')}`)
                        .split('_')
                        .map(p => firstUpper(p, 'en'))
                        .join('')
                );
            } else {
                schemaName = pluralize.singular(
                    _.snakeCase(collection.name)
                        .split('_')
                        .map(p => firstUpper(p, 'en'))
                        .join('')
                );
            }
            const rawCollectionName = _.snakeCase(collection.name.replace(`${program.name}.`, ''))
                .split('_')
                .join(' ');
            const resourceTitleSingular = pluralize.singular(rawCollectionName);
            const resourceTitlePlural = pluralize.plural(rawCollectionName);
            const resourceNameSingular = _.snakeCase(resourceTitleSingular).split('_').join('-');
            const resourceNamePlural = _.snakeCase(resourceTitlePlural).split('_').join('-');

            api.components.schemas[schemaName] = prepareSchema(collection);

            const getEndpoint = prepareGetEndpoint({
                moduleTitle,
                moduleName,
                tagName: subTagName,
                schemaName,
                resourceTitleSingular,
                resourceTitlePlural,
                resourceNameSingular,
                resourceNamePlural
            });
            api.paths[getEndpoint.path] = getEndpoint.endpoint;

            subTags.push({subTagName, subTagDisplayName});
        }

        for (const subTag of subTags) {
            if (api.tags.findIndex(tag => tag.name === subTag.subTagName) === -1) {
                api.tags.push({name: subTag.subTagName, 'x-displayName': subTag.subTagDisplayName});
            }
        }

        const existingIndex = api['x-tagGroups'].findIndex(tag => tag.name === tagName);
        if (existingIndex === -1) {
            api['x-tagGroups'].push({
                name: tagName,
                'x-displayName': tagDisplayName,
                tags: subTags.map(st => st.subTagName)
            });
        } else {
            api['x-tagGroups'][existingIndex].tags.push(...subTags.map(st => st.subTagName));

            api['x-tagGroups'][existingIndex].tags = _.uniq(api['x-tagGroups'][existingIndex].tags);
        }
    }

    return api;
}

function prepareGetEndpoint(payload) {
    const {
        moduleTitle,
        moduleName,
        tagName,
        schemaName,
        resourceTitleSingular,
        resourceTitlePlural,
        resourceNameSingular,
        resourceNamePlural
    } = payload;

    const path = `/api/v1/${moduleName}/get-${resourceNameSingular}/:id`;
    const endpoint = {
        get: {
            operationId: _.camelCase(`${moduleName}-get-${resourceNameSingular}`),
            tags: [tagName],
            summary: `Get ${resourceTitleSingular}`,
            parameters: [
                {
                    $ref: '#/components/parameters/id'
                }
            ],
            responses: {
                200: {
                    description:
                        'The request was successful, and the server has returned the requested resource in the response body.',
                    content: {
                        'application/json': {
                            schema: {
                                $ref: `#/components/schemas/${schemaName}`
                            }
                        }
                    }
                },
                400: {
                    $ref: '#/components/responses/BadRequest'
                },
                401: {
                    $ref: '#/components/responses/Unauthorized'
                },
                404: {
                    $ref: '#/components/responses/NotFound'
                },
                500: {
                    $ref: '#/components/responses/InternalServerError'
                }
            }
        }
    };

    return {path, endpoint};
}

function prepareSchema(collection) {
    const schema = {
        type: 'object',
        properties: {},
        required: ['_id']
    };

    schema.properties._id = {
        type: 'string',
        example: '65b8bfa76a915dc244ecd550'
    };

    for (const fieldName of Object.keys(collection.schema ?? {})) {
        const field = collection.schema[fieldName];
        const prop = prepareProp(fieldName, field);

        if (!prop) {
            continue;
        }

        if (prop.required) {
            schema.required.push(fieldName);
        }

        schema.properties[fieldName] = _.omit(prop, 'required');
    }

    return schema;
}

function prepareProp(fieldName, field) {
    const prop = {type: null};

    if (field.type === 'string') {
        prop.type = 'string';

        if (
            fieldName.endsWith('Id') ||
            fieldName === 'createdBy' ||
            fieldName === 'updatedBy' ||
            fieldName === 'deletedBy' ||
            fieldName === 'assignedBy' ||
            fieldName === 'assignedTo'
        ) {
            prop.example = '65b8bfa76a915dc244ecd550';
        }
    } else if (field.type === 'decimal') {
        prop.type = 'number';
        prop.example = 1.25;
    } else if (field.type === 'integer') {
        prop.type = 'integer';
        prop.example = 1;
    } else if (field.type === 'boolean') {
        prop.type = 'boolean';
    } else if (field.type === 'date' || field.type === 'datetime' || field.type === 'time') {
        prop.type = 'string';

        if (field.type === 'date') {
            prop.format = 'date';
            prop.example = '2025-04-15';
        } else if (field.type === 'datetime') {
            prop.format = 'date-time';
            prop.example = '2025-07-21T17:32:28Z';
        }
    } else if (field.type === 'object' && field.blackbox) {
        prop.type = 'object';
        prop.properties = {};
        prop.required = [];
    } else if (Array.isArray(field.type) && field.type.length > 0) {
        if (typeof field.type[0] === 'string') {
            prop.type = 'array';
            prop.items = {
                type: 'string'
            };

            if (fieldName.endsWith('Ids')) {
                prop.example = ['65b8bfa76a915dc244ecd550'];
            }
        } else if (typeof field.type[0] === 'object' && field.type[0] !== null) {
            prop.type = 'array';
            prop.items = {
                type: 'object',
                properties: {},
                required: []
            };

            for (const fName of Object.keys(field.type[0])) {
                const f = field.type[0][fName];
                const p = prepareProp(fName, f);

                if (!p) {
                    continue;
                }

                if (p.required) {
                    prop.items.required.push(fName);
                }

                prop.items.properties[fName] = _.omit(p, 'required');
            }
        }
    }

    if (!prop.type) {
        return null;
    }

    prop.required = field.required !== false && typeof field.default === 'undefined';

    return prop;
}
