import {traverse} from 'framework/helpers';
import {authenticate} from '../utils';

export default async function (app, request, response) {
    try {
        const user = await authenticate(app, request);
        let {name, data = {}, params = {}} = request.body;

        if (!name || name.length < 1) {
            // noinspection ExceptionCaughtLocallyJS
            throw new app.errors.Unprocessable(`Method with the name ${name} is not supported!"`);
        }

        // Convert date strings.
        data = traverse(data).map(function (x) {
            if (!!x && typeof x === 'string' && x.length >= 10 && !isNaN(new Date(x).getDate())) {
                const milliseconds = Date.parse(x);

                if (!isNaN(milliseconds)) {
                    this.update(new Date(milliseconds));
                }
            }
        });

        return response.json(
            await app.rpc(name, data, {
                ...params,
                fromApi: true,
                user
            })
        );
    } catch (error) {
        return response.status(400).json({
            status: 'error',
            code: error.code || 400,
            message: error.message || 'Bad request'
        });
    }
}
