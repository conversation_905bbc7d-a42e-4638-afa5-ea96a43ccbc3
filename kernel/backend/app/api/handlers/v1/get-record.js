import _ from 'lodash';
import {authenticate} from '../../utils';

export default async function (app, request, response) {
    const moduleName = request.params.module;
    if (!moduleName) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'Bad request'
        });
    }

    const collectionName = request.params.collection;
    if (!collectionName) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'Bad request'
        });
    }

    let collection = app.collection(`${moduleName}.${collectionName}`);
    if (typeof collection.service === 'undefined') {
        if (moduleName === 'system') {
            collection = app.collection(`kernel.${collectionName}`);
        }

        if (typeof collection.service === 'undefined') {
            return response.status(400).json({
                status: 'error',
                code: 400,
                message: 'Bad request'
            });
        }
    }

    const id = (request.params.id ?? '').trim();
    if (!id) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'Bad request'
        });
    }

    try {
        const user = await authenticate(app, request);
        if (!user.apiUser && user.email !== '<EMAIL>') {
            throw new Error('You can only authenticate with API users!');
        }

        let result = await collection.get(id, {
            checkPermission: true,
            fromApi: true,
            user
        });

        if (moduleName === 'system' && collectionName === 'users') {
            if (_.isPlainObject(result)) {
                delete result.password;
            }
        }

        return response.json(result);
    } catch (error) {
        let code = 400;

        if (_.isNumber(error.code) && error.code >= 400 && error.code < 600) {
            code = error.code;
        }

        return response.status(code).json({
            status: 'error',
            code,
            message: error.message || 'Bad request'
        });
    }
}
