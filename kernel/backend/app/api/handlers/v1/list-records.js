import _ from 'lodash';
import {traverse} from 'framework/helpers';
import {authenticate} from '../../utils';

export default async function (app, request, response) {
    const moduleName = request.params.module;
    if (!moduleName) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'Bad request'
        });
    }

    const collectionName = request.params.collection;
    if (!collectionName) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'Bad request'
        });
    }

    let collection = app.collection(`${moduleName}.${collectionName}`);
    if (typeof collection.service === 'undefined') {
        if (moduleName === 'system') {
            collection = app.collection(`kernel.${collectionName}`);
        }

        if (typeof collection.service === 'undefined') {
            return response.status(400).json({
                status: 'error',
                code: 400,
                message: 'Bad request'
            });
        }
    }

    try {
        const user = await authenticate(app, request);
        if (!user.apiUser && user.email !== '<EMAIL>') {
            throw new Error('You can only authenticate with API users!');
        }

        let query = {};

        if (typeof request.query.filter === 'string' && request.query.filter.trim().length > 0) {
            // Convert date strings.
            const filters = traverse(JSON.parse(request.query.filter.trim())).map(function (x) {
                if (
                    !!x &&
                    typeof x === 'string' &&
                    x.length >= 10 &&
                    x.includes('-') &&
                    x.includes(':') &&
                    !isNaN(new Date(x).getDate())
                ) {
                    const milliseconds = Date.parse(x);

                    if (!isNaN(milliseconds)) {
                        this.update(new Date(milliseconds));
                    }
                }
            });

            query = {
                ...filters
            };
        }

        if (typeof request.query.search === 'string' && request.query.search.trim().length > 0) {
            query.$search = request.query.search.trim();
        }

        if (typeof request.query.skip === 'string' && request.query.skip.trim().length > 0) {
            const skip = parseInt(request.query.skip.trim(), 10);

            if (!_.isFinite(skip)) {
                throw new Error('Skip parameter should be an integer!');
            }

            query.$skip = skip;
        }

        if (typeof request.query.limit === 'string' && request.query.limit.trim().length > 0) {
            const limit = parseInt(request.query.limit.trim(), 10);

            if (!_.isFinite(limit)) {
                throw new Error('Limit parameter should be an integer!');
            }

            query.$limit = Math.min(limit, 10000);
        } else {
            query.$limit = 50;
        }

        if (typeof request.query.sort === 'string' && request.query.sort.trim().length > 0) {
            const sort = JSON.parse(request.query.sort.trim());

            if (!_.isPlainObject(sort)) {
                throw new Error('Sort parameter should be an object!');
            }

            query.$sort = {};
            for (const field of Object.keys(sort)) {
                query.$sort[field] = sort[field] === 'desc' ? -1 : 1;
            }
        }

        if (typeof request.query.select === 'string' && request.query.select.trim().length > 0) {
            const select = JSON.parse(request.query.select.trim());

            if (!Array.isArray(select)) {
                throw new Error('Select parameter should be an array of strings!');
            }

            query.$select = [];
            for (const field of select) {
                query.$select.push(field);
            }
        }

        let paginate = null;
        if (typeof request.query.paginate === 'string' && request.query.paginate.trim().length > 0) {
            paginate = Math.min(parseInt(request.query.paginate.trim(), 10), 1000);

            if (!_.isFinite(paginate)) {
                throw new Error('Paginate parameter should be an integer!');
            }
        }

        if (user.email !== '<EMAIL>') {
            if (moduleName === 'system' && (collectionName === 'users' || collectionName === 'partners')) {
                if (!Array.isArray(query.$and)) {
                    query.$and = [];
                }

                query.$and.push({email: {$ne: '<EMAIL>'}});
            }

            if (moduleName === 'system' && (collectionName === 'sessions' || collectionName === 'logs')) {
                if (!Array.isArray(query.$and)) {
                    query.$and = [];
                }

                query.$and.push({'user.email': {$ne: '<EMAIL>'}});
            }
        }

        let result = await collection.find(query, {
            ...(_.isFinite(paginate) ? {paginate: {default: paginate}} : {}),
            checkPermission: true,
            fromApi: true,
            user
        });

        if (moduleName === 'system' && collectionName === 'users') {
            if (Array.isArray(result.data)) {
                result.data = result.data
                    .filter(item => item.email !== '<EMAIL>')
                    .map(item => {
                        delete item.password;

                        return item;
                    });
            } else if (Array.isArray(result)) {
                result = result
                    .filter(item => item.email !== '<EMAIL>')
                    .map(item => {
                        delete item.password;

                        return item;
                    });
            }
        }

        return response.json(result);
    } catch (error) {
        let code = 400;

        if (_.isNumber(error.code) && error.code >= 400 && error.code < 600) {
            code = error.code;
        }

        return response.status(code).json({
            status: 'error',
            code,
            message: error.message || 'Bad request'
        });
    }
}
