import _ from 'lodash';

export default async function (app, request, response) {
    try {
        const {email, password} = request.body;

        const payload = await app.service('authentication').create(
            {
                strategy: 'local',
                email,
                password
            },
            {headers: request.headers, fromApi: true}
        );
        const result = {};

        // Get token.
        result.token = payload.accessToken;

        // Get token expiration time.
        const tokenPayload = await app.service('authentication').verifyAccessToken(payload.accessToken);
        result.tokenExpireAt = new Date(tokenPayload.exp * 1000);

        if (!payload.user.apiUser && payload.user.email !== '<EMAIL>') {
            throw new Error('You can only authenticate with API users!');
        }

        // Get user id.
        result.userId = payload.user._id;

        response.json(result);
    } catch (error) {
        let code = 400;

        if (_.isNumber(error.code) && error.code >= 400 && error.code < 600) {
            code = error.code;
        }

        return response.status(code).json({
            status: 'error',
            code: code,
            message: error.message || 'Bad request'
        });
    }
}
