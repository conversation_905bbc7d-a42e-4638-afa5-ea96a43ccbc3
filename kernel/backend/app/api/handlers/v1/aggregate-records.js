import _ from 'lodash';
import {traverse} from 'framework/helpers';
import {authenticate} from '../../utils';

export default async function (app, request, response) {
    const moduleName = request.params.module;
    if (!moduleName) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'Bad request'
        });
    }

    const collectionName = request.params.collection;
    if (!collectionName) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'Bad request'
        });
    }

    let collection = app.collection(`${moduleName}.${collectionName}`);
    if (typeof collection.service === 'undefined') {
        if (moduleName === 'system') {
            collection = app.collection(`kernel.${collectionName}`);
        }

        if (typeof collection.service === 'undefined') {
            return response.status(400).json({
                status: 'error',
                code: 400,
                message: 'Bad request'
            });
        }
    }

    let pipeline = request.body;
    if (!Array.isArray(pipeline) || pipeline.length < 1) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'Invalid aggregation pipeline'
        });
    }
    if (pipeline.some(p => !_.isPlainObject(p) || _.isEmpty(p))) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'Invalid aggregation pipeline'
        });
    }

    if (pipeline.some(stage => typeof stage['$lookup'] !== 'undefined')) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: '$lookup pipeline stage is not allowed!'
        });
    }
    const limitStageIndex = pipeline.findIndex(stage => typeof stage['$limit'] !== 'undefined');
    if (limitStageIndex === -1) {
        pipeline.push({
            $limit: 50
        });
    } else {
        if (typeof pipeline[limitStageIndex].$limit === 'number') {
            pipeline[limitStageIndex].$limit = Math.min(pipeline[limitStageIndex].$limit, 10000);
        } else {
            pipeline[limitStageIndex].$limit = 50;
        }
    }

    try {
        if (moduleName === 'system' && collectionName === 'users') {
            throw new Error('You can not apply aggregation to users collection!');
        }

        const user = await authenticate(app, request);
        if (!user.apiUser && user.email !== '<EMAIL>') {
            throw new Error('You can only authenticate with API users!');
        }

        await app.checkPermission({
            type: 'record',
            collection: `${moduleName}.${collectionName}`,
            method: 'find',
            user
        });

        // Convert date strings.
        pipeline = traverse(pipeline).map(function (x) {
            if (
                !!x &&
                typeof x === 'string' &&
                x.length >= 10 &&
                x.includes('-') &&
                x.includes(':') &&
                !isNaN(new Date(x).getDate())
            ) {
                const milliseconds = Date.parse(x);

                if (!isNaN(milliseconds)) {
                    this.update(new Date(milliseconds));
                }
            }
        });

        return response.json(
            await collection.aggregate(pipeline, {
                checkPermission: true,
                fromApi: true,
                user
            })
        );
    } catch (error) {
        let code = 400;

        if (_.isNumber(error.code) && error.code >= 400 && error.code < 600) {
            code = error.code;
        }

        return response.status(code).json({
            status: 'error',
            code,
            message: error.message || 'Bad request'
        });
    }
}
