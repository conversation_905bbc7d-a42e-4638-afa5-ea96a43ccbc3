import _ from 'lodash';

export default async function (app, request, response) {
    try {
        const {
            email,
            password,
            getUser = false,
            getCompany = false,
            getFormatOptions = false,
            getSettings = false
        } = request.body;

        const payload = await app.service('authentication').create(
            {
                strategy: 'local',
                email,
                password
            },
            {headers: request.headers, fromApi: true}
        );
        const result = {};

        // Get token.
        result.token = payload.accessToken;

        // Get token expiration time.
        const tokenPayload = await app.service('authentication').verifyAccessToken(payload.accessToken);
        result.tokenExpireAt = new Date(tokenPayload.exp * 1000);

        // Get user id.
        result.userId = payload.user._id;

        // Get user.
        if (!!getUser) {
            const user = {};

            user.code = payload.user.code;
            user.name = payload.user.name;
            user.email = payload.user.email;
            user.isRoot = payload.user.isRoot;
            user.groupIds = payload.user.groupIds;
            user.branchIds = payload.user.branchIds;
            user.partnerId = payload.user.partnerId;
            user.languageId = payload.user.languageId;
            user.timezone = payload.user.timezone;
            user.inactiveSessionTimeout = payload.user.inactiveSessionTimeout;
            user.avatar = payload.user.avatar;
            user.createdAt = payload.user.createdAt;
            user.updatedAt = payload.user.updatedAt;
            user.language = _.pick(payload.user.language, [
                'name',
                'localeCode',
                'isoCode',
                'decimalSeparator',
                'thousandsSeparator',
                'dateFormat',
                'timeFormat'
            ]);
            // if (user.branchIds.length > 0) {
            //     user.branches = (await app.collection('kernel.branches').find({
            //         _id: {$in: user.branchIds},
            //         $select: ['code', 'name', 'email', 'website', 'phone', 'phoneCountryCode', 'phoneNumbers', 'address', 'isActive'],
            //         $disableActiveCheck: true,
            //         $disableSoftDelete: true
            //     })).map(branch => _.omit(branch, ['_id', 'order']));
            // }
            if (user.groupIds.length > 0) {
                user.groups = (
                    await app.collection('kernel.user-groups').find({
                        _id: {$in: user.groupIds},
                        $select: ['code', 'name', 'isRoot', 'isActive'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    })
                ).map(branch => _.omit(branch, ['_id', 'order']));
            }

            result.user = user;
        }

        // Get company.
        let company = null;
        if (getCompany || getFormatOptions) {
            company = await app.collection('kernel.company').findOne({
                $select: [
                    '_id',
                    'name',
                    'tagline',
                    'email',
                    'website',
                    'phone',
                    'phoneCountryCode',
                    'phoneNumbers',
                    'address',
                    'legalName',
                    'tin',
                    'taxDepartment',
                    'logo'
                ]
            });
        }
        if (getCompany) {
            result.company = _.omit(company, ['language', 'currency']);
            result.company.language = _.pick(company.language, [
                'name',
                'localeCode',
                'isoCode',
                'decimalSeparator',
                'thousandsSeparator',
                'dateFormat',
                'timeFormat'
            ]);
            result.company.currency = _.pick(company.currency, ['name', 'unit', 'subUnit', 'symbol', 'symbolPosition']);
        }

        if (getFormatOptions) {
            const formatOptions = {};

            formatOptions.currency = {};
            formatOptions.currency.code = company.currency.name;
            formatOptions.currency.symbol = company.currency.symbol;
            formatOptions.currency.format = company.currency.symbolPosition === 'before' ? '%s %v' : '%v %s';
            formatOptions.currency.symbolPosition = company.currency.symbolPosition;
            formatOptions.currency.decimal = payload.user.language.decimalSeparator;
            formatOptions.currency.thousand = payload.user.language.thousandsSeparator;
            formatOptions.currency.precision = app.setting('system.currencyPrecision');
            formatOptions.number = {};
            formatOptions.number.decimal = payload.user.language.decimalSeparator;
            formatOptions.number.thousand = payload.user.language.thousandsSeparator;
            formatOptions.number.precision = app.setting('system.amountPrecision');
            formatOptions.date = payload.user.language.dateFormat;
            formatOptions.datetime = `${payload.user.language.dateFormat} ${payload.user.language.timeFormat}`;
            formatOptions.time = payload.user.language.timeFormat;

            result.formatOptions = formatOptions;
        }

        if (getSettings) {
            result.settings = {};

            for (const key of Object.keys(app.settingsStore)) {
                if (
                    key.includes('system.') ||
                    key.includes('sale.') ||
                    key.includes('purchase.') ||
                    key.includes('inventory.')
                ) {
                    const record = app.settingsStore[key];

                    result.settings[key] = !_.isUndefined(record.value) ? record.value : record.default;
                }
            }
        }

        response.json(result);
    } catch (error) {
        let code = 400;

        if (_.isNumber(error.code) && error.code >= 400 && error.code < 600) {
            code = error.code;
        }

        return response.status(code).json({
            status: 'error',
            code: code,
            message: error.message || 'Bad request'
        });
    }
}
