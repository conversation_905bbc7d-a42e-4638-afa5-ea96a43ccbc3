import {authenticate, parsePayload} from '../utils';

export default async function (app, request, response) {
    try {
        const user = await authenticate(app, request);
        const {collection, method, id, query, data, params} = parsePayload(app, user, request.body);

        if (method === 'find' || method === 'findOne') {
            return response.json(await app.collection(collection)[method](query, params));
        } else if (method === 'get') {
            return response.json(await app.collection(collection).get(id, params));
        } else if (method === 'create') {
            return response.json(await app.collection(collection).create(data, params));
        } else if (method === 'update') {
            return response.json(await app.collection(collection).update(id, data, params));
        } else if (method === 'patch') {
            return response.json(await app.collection(collection).patch(query, data, params));
        } else if (method === 'remove') {
            return response.json(await app.collection(collection).remove(id || query, params));
        }
    } catch (error) {
        return response.status(400).json({
            status: 'error',
            code: error.code || 400,
            message: error.message || 'Bad request'
        });
    }
}
