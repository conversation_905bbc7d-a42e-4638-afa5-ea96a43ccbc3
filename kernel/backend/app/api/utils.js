import _ from 'lodash';
import {traverse} from 'framework/helpers';

export function parsePayload(app, user, payload) {
    const allowedMethods = ['find', 'findOne', 'get', 'create', 'update', 'patch', 'remove'];
    const {collection, method, id = null, query = {}, data = {}, params = {}} = payload;
    const result = {};

    if (!collection || collection.length < 1) {
        throw new app.errors.Unprocessable('Collection name must be supplied!');
    }

    if (!method || method.length < 1) {
        throw new app.errors.Unprocessable('Method name must be supplied!');
    }

    if (allowedMethods.indexOf(method) === -1) {
        throw new app.errors.Unprocessable(`Method with the name ${method} is not supported!"`);
    }

    result.collection = collection;
    result.method = method;
    result.id = id;
    result.query = query || {};
    result.data = data || {};
    result.params = {
        ...(params || {}),
        checkPermission: true,
        fromApi: true,
        user
    };

    if (method === 'find' || method === 'findOne') {
        // Get pagination and limit.
        if (_.isPlainObject(query.$paginate)) {
            result.params.paginate = {
                ...query.$paginate,
                max: 5000
            };
            delete query.$paginate;
        }
        if (_.isNumber(query.$paginate)) {
            result.params.paginate = {default: query.$paginate, max: 5000};
            delete query.$paginate;
        } else {
            if (!_.isNumber(query.$limit)) {
                query.$limit = 5000;
            } else {
                query.$limit = Math.min(query.$limit, 5000);
            }
        }

        // Check branch.
        params.checkBranch = true;
    }

    // Convert date strings.
    result.data = traverse(result.data).map(function (x) {
        if (
            !!x &&
            typeof x === 'string' &&
            x.length >= 10 &&
            x.includes('-') &&
            x.includes(':') &&
            !isNaN(new Date(x).getDate())
        ) {
            const milliseconds = Date.parse(x);

            if (!isNaN(milliseconds)) {
                this.update(new Date(milliseconds));
            }
        }
    });
    result.query = traverse(result.query).map(function (x) {
        if (
            !!x &&
            typeof x === 'string' &&
            x.length >= 10 &&
            x.includes('-') &&
            x.includes(':') &&
            !isNaN(new Date(x).getDate())
        ) {
            const milliseconds = Date.parse(x);

            if (!isNaN(milliseconds)) {
                this.update(new Date(milliseconds));
            }
        }
    });

    return result;
}

export async function authenticate(app, request) {
    const headerValue = request.headers && request.headers['authorization'];

    if (!headerValue || typeof headerValue !== 'string') {
        throw new app.errors.NotAuthenticated(
            'Authentication is required to access the requested record. The client must include the appropriate credentials!'
        );
    }

    const [, scheme, schemeValue] = headerValue.match(/(\S+)\s+(\S+)/) || [];
    const hasScheme = scheme && ['Bearer', 'JWT'].some(current => new RegExp(current, 'i').test(scheme));

    if (scheme && !hasScheme) {
        throw new app.errors.NotAuthenticated(
            'Authentication is required to access the requested record. The client must include the appropriate credentials!'
        );
    }

    const accessToken = hasScheme ? schemeValue : headerValue;

    if (!accessToken) {
        throw new app.errors.NotAuthenticated('No access token');
    }

    const payload = await app.service('authentication').verifyAccessToken(accessToken);
    const userId = payload.sub;

    return await app.collection('kernel.users').get(userId);
}
