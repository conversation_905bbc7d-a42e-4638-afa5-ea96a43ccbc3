export default async function (app, request, response) {
    const result = {};

    result.title = app.config('app.title');
    result.version = app.config('app.version');

    result.modules = [];
    const publicParams = app.get('publicParams');
    const excludedModules = ['local-tr', 'enterapp', 'portal', 'kernel', 'mobile'];
    for (const program of publicParams.programs ?? []) {
        if (!app.hasModule(program.name) || excludedModules.includes(program.name)) {
            continue;
        }

        result.modules.push({name: program.name, title: program.title});
    }

    return response.json(result);
}
