import _ from 'lodash';
import {firstUpper} from 'framework/helpers';
import pluralize from 'pluralize';
import fastCopy from 'fast-copy';

import recordsListDescription from './records-list-description.md';
import recordsAggregateDescription from './records-aggregate-description.md';

export default async function (app, request, response) {
    const moduleName = (request.params ?? {}).module;
    if (!moduleName) {
        return response.status(404).json({
            status: 'error',
            code: 404,
            message: 'Not found'
        });
    }

    const publicParams = app.get('publicParams');
    const module = (publicParams.programs ?? []).find(program => program.name === moduleName);
    if (!module) {
        return response.status(404).json({
            status: 'error',
            code: 404,
            message: 'Not found'
        });
    }

    const api = prepareApiCommon(app, moduleName, module.title);
    const tags = [];

    if ((module.collections ?? []).length > 0) {
        const moduleTitle = module.title;
        const moduleName = module.name;
        const collections = module.collections ?? [];

        if (moduleName === 'system') {
            const kernelModule = fastCopy((publicParams.programs ?? []).find(program => program.name === 'kernel'));

            collections.unshift(
                ...(kernelModule.collections ?? []).map(collection => {
                    collection.name = collection.name.replace('kernel.', 'system.');
                    return collection;
                })
            );
        }

        const tagGroup = {
            name: 'records',
            'x-displayName': 'Records',
            tags: []
        };

        for (const collection of collections) {
            if (collection.name === 'kernel.method-logs' || collection.name === 'system.identity-validation-logs') {
                continue;
            }

            const tagName = pluralize.singular(_.camelCase(collection.name));
            const tagDisplayName = pluralize.singular(
                _.snakeCase(collection.name.replace(`${module.name}.`, ''))
                    .split('_')
                    .map(p => firstUpper(p, 'en'))
                    .join(' ')
            );
            let schemaName = '';
            if (module.name === 'kernel') {
                schemaName = pluralize.singular(
                    _.snakeCase(`system.${collection.name.replace(`${module.name}.`, '')}`)
                        .split('_')
                        .map(p => firstUpper(p, 'en'))
                        .join('')
                );
            } else {
                schemaName = pluralize.singular(
                    _.snakeCase(collection.name)
                        .split('_')
                        .map(p => firstUpper(p, 'en'))
                        .join('')
                );
            }
            const rawCollectionName = _.snakeCase(collection.name.replace(`${module.name}.`, ''))
                .split('_')
                .join(' ');
            const recordTitleSingular = pluralize.singular(rawCollectionName);
            const recordTitlePlural = pluralize.plural(rawCollectionName);
            const recordNameSingular = _.snakeCase(recordTitleSingular).split('_').join('-');
            const recordNamePlural = _.snakeCase(recordTitlePlural).split('_').join('-');

            api.components.schemas[schemaName] = prepareSchema(collection, {forCreate: false, forPatch: false});
            api.components.schemas[`Create${schemaName}Request`] = prepareSchema(collection, {
                forCreate: true,
                forPatch: false
            });
            api.components.schemas[`Patch${schemaName}Request`] = prepareSchema(collection, {
                forCreate: false,
                forPatch: true
            });

            const path = `/api/v1/${moduleName}/${recordNamePlural}`;
            api.paths[path] = {
                get: prepareListEndpoint({
                    moduleTitle,
                    moduleName,
                    tagName,
                    schemaName,
                    recordTitleSingular,
                    recordTitlePlural,
                    recordNameSingular,
                    recordNamePlural,
                    path
                })
            };
            api.paths[path].post = prepareCreateEndpoint({
                moduleTitle,
                moduleName,
                tagName,
                schemaName,
                recordTitleSingular,
                recordTitlePlural,
                recordNameSingular,
                recordNamePlural,
                path
            });
            api.paths[`${path}/{id}`] = {
                get: prepareGetEndpoint({
                    moduleTitle,
                    moduleName,
                    tagName,
                    schemaName,
                    recordTitleSingular,
                    recordTitlePlural,
                    recordNameSingular,
                    recordNamePlural,
                    path
                })
            };
            api.paths[`${path}/{id}`].patch = preparePatchEndpoint({
                moduleTitle,
                moduleName,
                tagName,
                schemaName,
                recordTitleSingular,
                recordTitlePlural,
                recordNameSingular,
                recordNamePlural,
                path
            });
            api.paths[`${path}/{id}`].delete = prepareDeleteEndpoint({
                moduleTitle,
                moduleName,
                tagName,
                schemaName,
                recordTitleSingular,
                recordTitlePlural,
                recordNameSingular,
                recordNamePlural,
                path
            });
            api.paths[`${path}/aggregate`] = {
                post: prepareAggregateEndpoint({
                    moduleTitle,
                    moduleName,
                    tagName,
                    schemaName,
                    recordTitleSingular,
                    recordTitlePlural,
                    recordNameSingular,
                    recordNamePlural,
                    path
                })
            };

            tags.push({tagName, tagDisplayName});
            tagGroup.tags.push(tagName);
        }

        api['x-tagGroups'].push(tagGroup);
    }

    for (const tag of tags) {
        if (api.tags.findIndex(t => t.name === tag.tagName) === -1) {
            api.tags.push({name: tag.tagName, 'x-displayName': tag.tagDisplayName});
        }
    }

    return response.json(api);
}

function prepareAggregateEndpoint(payload) {
    const {
        moduleTitle,
        moduleName,
        tagName,
        schemaName,
        recordTitleSingular,
        recordTitlePlural,
        recordNameSingular,
        recordNamePlural,
        path
    } = payload;

    return {
        operationId: _.camelCase(`${moduleName}-aggregate-${recordNamePlural}`),
        tags: [tagName],
        summary: `Aggregate ${recordTitlePlural}`,
        description: recordsAggregateDescription.replaceAll('{{url}}', path),
        parameters: [],
        requestBody: {
            required: true,
            content: {
                'application/json': {
                    schema: {
                        type: 'array',
                        items: {
                            type: 'object',
                            properties: {},
                            required: []
                        }
                    }
                }
            }
        },
        responses: {
            200: {
                description:
                    'The request was successful, and the server has returned the requested records in the response body.',
                content: {
                    'application/json': {
                        schema: {
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {},
                                required: []
                            }
                        }
                    }
                }
            },
            400: {
                $ref: '#/components/responses/BadRequest'
            },
            401: {
                $ref: '#/components/responses/Unauthorized'
            },
            404: {
                $ref: '#/components/responses/NotFound'
            },
            500: {
                $ref: '#/components/responses/InternalServerError'
            }
        }
    };
}

function prepareDeleteEndpoint(payload) {
    const {
        moduleTitle,
        moduleName,
        tagName,
        schemaName,
        recordTitleSingular,
        recordTitlePlural,
        recordNameSingular,
        recordNamePlural
    } = payload;

    return {
        operationId: _.camelCase(`${moduleName}-delete-${recordNameSingular}`),
        tags: [tagName],
        summary: `Delete ${recordTitleSingular}`,
        description:
            'You can use this endpoint to delete a single record with a valid `id`. `id` parameter must be provided as a path parameter. ' +
            `Ex: DELETE /api/v1/${moduleName}/${recordNamePlural}/65b8bfa76a915dc244ecd550`,
        parameters: [
            {
                $ref: '#/components/parameters/id'
            }
        ],
        responses: {
            200: {
                description:
                    'The request was successful, and the server has returned the requested record in the response body.',
                content: {
                    'application/json': {
                        schema: {
                            $ref: `#/components/schemas/${schemaName}`
                        }
                    }
                }
            },
            400: {
                $ref: '#/components/responses/BadRequest'
            },
            401: {
                $ref: '#/components/responses/Unauthorized'
            },
            404: {
                $ref: '#/components/responses/NotFound'
            },
            500: {
                $ref: '#/components/responses/InternalServerError'
            }
        }
    };
}

function preparePatchEndpoint(payload) {
    const {
        moduleTitle,
        moduleName,
        tagName,
        schemaName,
        recordTitleSingular,
        recordTitlePlural,
        recordNameSingular,
        recordNamePlural
    } = payload;

    return {
        operationId: _.camelCase(`${moduleName}-patch-${recordNameSingular}`),
        tags: [tagName],
        summary: `Patch ${recordTitleSingular}`,
        description:
            'You can use this endpoint to apply partial update to a single record with a valid `id`. `id` parameter must be provided as a path parameter. Only the fields to be updated should be sent in the request body. Updated record will be returned as a successful response. ' +
            `Ex: PATCH /api/v1/${moduleName}/${recordNamePlural}/65b8bfa76a915dc244ecd550`,
        parameters: [
            {
                $ref: '#/components/parameters/id'
            }
        ],
        requestBody: {
            required: true,
            content: {
                'application/json': {
                    schema: {
                        $ref: `#/components/schemas/Patch${schemaName}Request`
                    }
                }
            }
        },
        responses: {
            200: {
                description:
                    'The request was successful, and the server has returned the requested record in the response body.',
                content: {
                    'application/json': {
                        schema: {
                            $ref: `#/components/schemas/${schemaName}`
                        }
                    }
                }
            },
            400: {
                $ref: '#/components/responses/BadRequest'
            },
            401: {
                $ref: '#/components/responses/Unauthorized'
            },
            404: {
                $ref: '#/components/responses/NotFound'
            },
            500: {
                $ref: '#/components/responses/InternalServerError'
            }
        }
    };
}

function prepareCreateEndpoint(payload) {
    const {
        moduleTitle,
        moduleName,
        tagName,
        schemaName,
        recordTitleSingular,
        recordTitlePlural,
        recordNameSingular,
        recordNamePlural
    } = payload;

    return {
        operationId: _.camelCase(`${moduleName}-create-${recordNameSingular}`),
        tags: [tagName],
        summary: `Create ${recordTitleSingular}`,
        description:
            'You can use this endpoint to create a single record. Created record will be returned as a successful response.',
        parameters: [],
        requestBody: {
            required: true,
            content: {
                'application/json': {
                    schema: {
                        $ref: `#/components/schemas/Create${schemaName}Request`
                    }
                }
            }
        },
        responses: {
            201: {
                description:
                    'The request was successful, and the server has returned the requested record in the response body.',
                content: {
                    'application/json': {
                        schema: {
                            $ref: `#/components/schemas/${schemaName}`
                        }
                    }
                }
            },
            400: {
                $ref: '#/components/responses/BadRequest'
            },
            401: {
                $ref: '#/components/responses/Unauthorized'
            },
            404: {
                $ref: '#/components/responses/NotFound'
            },
            500: {
                $ref: '#/components/responses/InternalServerError'
            }
        }
    };
}

function prepareListEndpoint(payload) {
    const {
        moduleTitle,
        moduleName,
        tagName,
        schemaName,
        recordTitleSingular,
        recordTitlePlural,
        recordNameSingular,
        recordNamePlural,
        path
    } = payload;

    const parameters = [
        {
            in: 'query',
            name: 'filter',
            description: `Filter records with the record fields. Some basic mongo queries can be used.`,
            example: `{"code": {"$ne": "000"}, "test": "demo", "age": {"$gte": 20}}`,
            required: false,
            schema: {
                type: 'string'
            },
            allowReserved: false
        },
        {
            in: 'query',
            name: 'search',
            description: 'Search records.',
            example: 'test',
            required: false,
            style: 'form',
            explode: true,
            schema: {
                type: 'string'
            },
            allowReserved: false
        },
        {
            in: 'query',
            name: 'select',
            description: `Return only selected record field.`,
            example: `["code", "items.productCode"]`,
            required: false,
            style: 'form',
            explode: true,
            schema: {
                type: 'string'
            },
            allowReserved: false
        },
        {
            in: 'query',
            name: 'skip',
            description: 'Skip records.',
            example: 50,
            required: false,
            style: 'form',
            explode: true,
            schema: {
                type: 'integer',
                minimum: 0
            },
            allowReserved: false
        },
        {
            in: 'query',
            name: 'limit',
            description: 'Limit records.',
            example: 50,
            required: false,
            style: 'form',
            explode: true,
            schema: {
                type: 'integer',
                minimum: 1,
                maximum: 10000
            },
            allowReserved: false
        },
        {
            in: 'query',
            name: 'sort',
            description: 'Order records.',
            example: '{"code": "asc", "test": "desc"}',
            required: false,
            schema: {
                type: 'string'
            },
            allowReserved: false
        },
        {
            in: 'query',
            name: 'paginate',
            description: 'Paginate records.',
            example: 10,
            required: false,
            style: 'form',
            explode: true,
            schema: {
                type: 'integer',
                minimum: 1
            },
            allowReserved: false
        }
    ];

    return {
        operationId: _.camelCase(`${moduleName}-list-${recordNamePlural}`),
        tags: [tagName],
        summary: `List ${recordTitlePlural}`,
        description: recordsListDescription.replaceAll('{{url}}', path),
        parameters,
        responses: {
            200: {
                description:
                    'The request was successful, and the server has returned the requested records in the response body.',
                content: {
                    'application/json': {
                        schema: {
                            oneOf: [
                                {
                                    type: 'array',
                                    items: {
                                        $ref: `#/components/schemas/${schemaName}`
                                    }
                                },
                                {
                                    type: 'object',
                                    properties: {
                                        data: {
                                            type: 'array',
                                            items: {
                                                $ref: `#/components/schemas/${schemaName}`
                                            }
                                        },
                                        total: {
                                            type: 'number'
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            400: {
                $ref: '#/components/responses/BadRequest'
            },
            401: {
                $ref: '#/components/responses/Unauthorized'
            },
            404: {
                $ref: '#/components/responses/NotFound'
            },
            500: {
                $ref: '#/components/responses/InternalServerError'
            }
        }
    };
}

function prepareGetEndpoint(payload) {
    const {
        moduleTitle,
        moduleName,
        tagName,
        schemaName,
        recordTitleSingular,
        recordTitlePlural,
        recordNameSingular,
        recordNamePlural
    } = payload;

    return {
        operationId: _.camelCase(`${moduleName}-get-${recordNameSingular}`),
        tags: [tagName],
        summary: `Get ${recordTitleSingular}`,
        description:
            'You can use this endpoint to get a single record with a valid `id`. `id` parameter must be provided as a path parameter. ' +
            `Ex: GET /api/v1/${moduleName}/${recordNamePlural}/65b8bfa76a915dc244ecd550`,
        parameters: [
            {
                $ref: '#/components/parameters/id'
            }
        ],
        responses: {
            200: {
                description:
                    'The request was successful, and the server has returned the requested record in the response body.',
                content: {
                    'application/json': {
                        schema: {
                            $ref: `#/components/schemas/${schemaName}`
                        }
                    }
                }
            },
            400: {
                $ref: '#/components/responses/BadRequest'
            },
            401: {
                $ref: '#/components/responses/Unauthorized'
            },
            404: {
                $ref: '#/components/responses/NotFound'
            },
            500: {
                $ref: '#/components/responses/InternalServerError'
            }
        }
    };
}

function prepareSchema(collection, settings) {
    const {forCreate = false, forPatch = false} = settings;
    const excludedFields = ['system', 'deleted', 'deletedAt', 'deletedBy', 'searchTerms', 'searchText'];
    const schema = {
        type: 'object',
        properties: {},
        required: ['_id']
    };

    if (!forCreate && !forPatch) {
        schema.properties._id = {
            type: 'string',
            example: '65b8bfa76a915dc244ecd550'
        };
    }

    if (forCreate || forPatch) {
        excludedFields.push(...['createdAt', 'createdBy', 'updatedAt', 'updatedBy']);
    }

    for (const fieldName of Object.keys(collection.schema ?? {})) {
        if (excludedFields.includes(fieldName)) {
            continue;
        }

        const field = collection.schema[fieldName];
        const prop = prepareProp(fieldName, field, settings);

        if (!prop) {
            continue;
        }

        if (prop.required && !forPatch) {
            schema.required.push(fieldName);
        }

        schema.properties[fieldName] = _.omit(prop, 'required');
    }

    return schema;
}

function prepareProp(fieldName, field, settings) {
    const {forCreate = false, forPatch = false} = settings;
    const prop = {type: null};

    if (field.type === 'string') {
        prop.type = 'string';

        if (
            fieldName.endsWith('Id') ||
            fieldName === 'createdBy' ||
            fieldName === 'updatedBy' ||
            fieldName === 'deletedBy' ||
            fieldName === 'assignedBy' ||
            fieldName === 'assignedTo'
        ) {
            prop.example = '65b8bfa76a915dc244ecd550';
        }
    } else if (field.type === 'decimal') {
        prop.type = 'number';
        prop.example = 1.25;
    } else if (field.type === 'integer') {
        prop.type = 'integer';
        prop.example = 1;
    } else if (field.type === 'boolean') {
        prop.type = 'boolean';
    } else if (field.type === 'date' || field.type === 'datetime' || field.type === 'time') {
        prop.type = 'string';

        if (field.type === 'date') {
            prop.format = 'date';
            prop.example = '2025-04-15';
        } else if (field.type === 'datetime') {
            prop.format = 'date-time';
            prop.example = '2025-07-21T17:32:28Z';
        }
    } else if (field.type === 'object' && field.blackbox) {
        prop.type = 'object';
        prop.properties = {};
        prop.required = [];
    } else if (Array.isArray(field.type) && field.type.length > 0) {
        if (typeof field.type[0] === 'string') {
            prop.type = 'array';
            prop.items = {
                type: 'string'
            };

            if (fieldName.endsWith('Ids')) {
                prop.example = ['65b8bfa76a915dc244ecd550'];
            }
        } else if (typeof field.type[0] === 'object' && field.type[0] !== null) {
            prop.type = 'array';
            prop.items = {
                type: 'object',
                properties: {},
                required: []
            };

            for (const fName of Object.keys(field.type[0])) {
                const f = field.type[0][fName];
                const p = prepareProp(fName, f, settings);

                if (!p) {
                    continue;
                }

                if (p.required && !forPatch) {
                    prop.items.required.push(fName);
                }

                prop.items.properties[fName] = _.omit(p, 'required');
            }
        }
    }

    if (!prop.type) {
        return null;
    }

    prop.required = field.required !== false && typeof field.default === 'undefined';

    return prop;
}

function prepareApiCommon(app, moduleName, moduleTitle) {
    return {
        openapi: '3.1.0',
        info: {
            title: `EnterERP ${moduleTitle} API`,
            description: `EnterERP ${moduleTitle.toLowerCase()} rest api endpoints.`,
            version: '1.0.0'
        },
        security: [{BearerJwt: []}],
        paths: {
            '/api/v1/authenticate': {
                post: {
                    operationId: 'authenticate',
                    tags: ['authentication'],
                    summary: 'Authenticate',
                    description: 'Authenticate to get the api token.',
                    security: [],
                    requestBody: {
                        required: true,
                        description: '',
                        content: {
                            'application/json': {
                                schema: {
                                    type: 'object',
                                    properties: {
                                        email: {type: 'string', example: '<EMAIL>'},
                                        password: {type: 'string'}
                                    },
                                    required: ['email', 'password']
                                }
                            }
                        }
                    },
                    responses: {
                        200: {
                            description: 'The request was successful.',
                            content: {
                                'application/json': {
                                    schema: {
                                        type: 'object',
                                        properties: {
                                            token: {type: 'string'},
                                            tokenExpireAt: {
                                                type: 'string',
                                                format: 'date-time',
                                                example: '2025-07-21T17:32:28Z'
                                            },
                                            userId: {type: 'string', example: '65b8bfa76a915dc244ecd550'}
                                        },
                                        required: ['token', 'tokenExpireAt', 'userId']
                                    }
                                }
                            }
                        },
                        401: {
                            $ref: '#/components/responses/Unauthorized'
                        }
                    }
                }
            }
        },
        components: {
            parameters: {
                id: {
                    name: 'id',
                    in: 'path',
                    required: true,
                    schema: {
                        type: 'string'
                    }
                }
            },
            responses: {
                BadRequest: {
                    description:
                        'The server could not understand the request due to invalid syntax. The client should modify the request and try again.',
                    content: {
                        'application/json': {
                            schema: {
                                type: 'object',
                                properties: {
                                    status: {type: 'string'},
                                    code: {type: 'string'},
                                    message: {type: 'string'}
                                },
                                required: ['status', 'message']
                            }
                        }
                    }
                },
                Unauthorized: {
                    description:
                        'Authentication is required to access the requested record. The client must include the appropriate credentials.',
                    content: {
                        'application/json': {
                            schema: {
                                type: 'object',
                                properties: {
                                    status: {type: 'string'},
                                    code: {type: 'string'},
                                    message: {type: 'string'}
                                },
                                required: ['status', 'message']
                            }
                        }
                    }
                },
                NotFound: {
                    description:
                        'The server cannot find the requested record. The endpoint may be invalid or the record may no longer exist.',
                    content: {
                        'application/json': {
                            schema: {
                                type: 'object',
                                properties: {
                                    status: {type: 'string'},
                                    code: {type: 'string'},
                                    message: {type: 'string'}
                                },
                                required: ['status', 'message']
                            }
                        }
                    }
                },
                InternalServerError: {
                    description:
                        'The server encountered an unexpected condition that prevented it from fulfilling the request. Report the issue to the support team if it persists.',
                    content: {
                        'application/json': {
                            schema: {
                                type: 'object',
                                properties: {
                                    status: {type: 'string'},
                                    code: {type: 'string'},
                                    message: {type: 'string'}
                                },
                                required: ['status', 'message']
                            }
                        }
                    }
                }
            },
            securitySchemes: {
                BearerJwt: {
                    type: 'http',
                    scheme: 'Bearer',
                    bearerFormat: 'Json Web Token (JWT)',
                    description:
                        'Jwt Auth: Authenticated requests contain a valid Json Web Token (JWT) as part of the `Authorization: Bearer <JWT>` header.'
                }
            },
            schemas: {}
        },
        tags: [{name: 'authentication', 'x-displayName': 'Authentication'}],
        'x-tagGroups': [
            {
                name: 'authentication',
                'x-displayName': 'Authentication',
                tags: ['authentication']
            }
        ]
    };
}
