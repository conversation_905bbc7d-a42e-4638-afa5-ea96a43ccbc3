Aggregation operations process multiple records and return computed results. You can use aggregation operations to:
- Group values from multiple records together.
- Perform operations on the grouped data to return a single result.
- Analyze data changes over time.

To perform aggregation operations, you can use: [aggregation pipelines](https://www.mongodb.com/docs/manual/aggregation/#std-label-aggregation-pipeline-intro "Aggregation pipelines"), which are the preferred method for performing aggregations.

$limit pipeline stage can be maximum 10,000. Up to 10,000 records can be listed.

### Example
```js
const pipeline = [
    {
        $match: {
            amount: {$gte: 1500}
        }
    },
    {
        $group: {
            _id: {code: '$code', name: '$name'},
            total: {$sum: '$amount'}
        }
    },
    {
        $project: {
            _id: 0,
            code: '$_id.code',
            name: '$_id.name',
            total: '$total'
        }
    },
    {
        $sort: {total: -1}
    },
    {
        $limit: 10
    }
];

const response = await fetch('{{url}}/aggregate', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer YOUR_SECRET_TOKEN'
    },
    body: JSON.stringify(pipeline)
});

if (response.ok) {
    const result = await response.json();

    console.log(result);
}
```
