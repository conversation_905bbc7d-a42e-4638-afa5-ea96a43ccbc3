You can use this endpoint to get list of records. You can filter, select, skip, limit, sort, search, and paginate the records. Limit query parameter can be maximum 10,000. Up to 10,000 records can be listed.

Filter and sort query parameters (if supplied) must be a valid json. You can use basic mongo queries in the filter object.

### Example
```js
const paramsObj = {
    filters: JSON.stringify({
        code: {$ne: 'A000'},
        salary: {$lt: 1000}
    }),
    search: 'test',
    skip: 0,
    limit: 50,
    select: JSON.stringify(['test', 'test.sub']),
    sort: JSON.stringify({code: 'asc'})
};
const searchParams = new URLSearchParams(paramsObj);
const url = `{{url}}?${searchParams.toString()}`;

const response = await fetch('{{url}}', {
    headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer YOUR_SECRET_TOKEN'
    }
});

if (response.ok) {
    const result = await response.json();
    
    console.log(result);
}
```
