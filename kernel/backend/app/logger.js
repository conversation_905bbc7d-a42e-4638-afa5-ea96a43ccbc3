import _ from 'lodash';

export default function (app) {
    app.log = payload => {
        let log = payload || {};

        if (_.isString(payload)) {
            log.message = payload;
        }

        if (!!payload.status) {
            payload.level = payload.status;
        }

        log = _.defaults(log, {
            level: 'info',
            message: '',
            date: app.datetime.local().toJSDate(),
            user: {
                name: 'System',
                email: ''
            },
            props: null,
            persist: true
        });

        // Persist logs.
        if (log.persist) {
            return app.collection('kernel.logs').create(_.omit(log, 'persist'), {
                disableValidation: true,
                skipEvents: true
            });
        }

        return log;
    };
}
