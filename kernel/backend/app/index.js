import path from 'path';
import os from 'os';
import cors from 'cors';
import compress from 'compression';
import helmet from 'helmet';
import winston from 'winston';
import historyApiFallback from 'connect-history-api-fallback';
import feathers from '@feathersjs/feathers';
import * as errors from '@feathersjs/errors';
import express from '@feathersjs/express';

// import appSchema from 'framework/schema/app-schema';
import appHooks from '../hooks';
import config from './config';
import schema from './schema';
import datetime from './datetime';
import general from './general';
import licence from './licence';
import initDB from './init-db';
import initReportDB from './init-report-db';
import collection from './collection';
import method from './method';
import initSocket from './init-socket';
import sync from './sync';
import auth from './auth';
import setting from './setting';
import translation from './translation';
import permission from './permission';
import rounding from './rounding';
import formatter from './formatter';
import jobs from './jobs';
import queues from './queues';
import mail from './mail';
import print from './print';
import converter from './converter';
import extensions from './extensions';
import programs from './programs';
import files from './files';
import api from './api';
import frontend from './frontend';
import publish from './publish';
import migrations from './migrations';
import boot from './boot';
import backups from './backups';
import logger from './logger';
import '@/programs.json';
import process from 'node:process';

export default async function () {
    // Create app instance.
    const app = express(feathers());
    app.disable('x-powered-by');

    // Common vars
    app.set('arch', 'backend');
    app.set('isDevelopment', process.env.NODE_ENV === 'development');
    app.set('isProduction', process.env.NODE_ENV !== 'development');
    app.set('mappedRecordPermissions', {});

    // Initialize app errors.
    app.errors = errors;

    // Initialize app config.
    app.configure(config);

    // Initialize app date time.
    app.configure(datetime);

    // Initialize general app methods.
    app.configure(general);

    // Initialize schema.
    app.configure(schema);

    // Register middleware.
    app.use(cors());
    if (!app.get('isDevelopment')) {
        app.use(
            helmet({
                contentSecurityPolicy: false
            })
        );
    }
    if (app.get('isDevelopment')) {
        app.use(compress());
    }
    app.use(
        express.json({
            limit: '256kb'
        })
    );
    app.use(express.urlencoded({extended: true}));
    app.set('etag', false);
    if (app.get('isDevelopment')) {
        app.use('/vendors/', express.static(app.config('paths.vendors')));
        app.use('/assets/', express.static(app.config('paths.assets')));
        app.use('/static/', express.static(app.config('paths.static')));
    } else {
        app.use('/vendors/', express.static(app.config('paths.vendors'), {maxAge: 1000 * 60 * 60 * 24 * 30}));
        app.use('/assets/', express.static(app.config('paths.assets'), {maxAge: 1000 * 60 * 60 * 24 * 30}));
        app.use('/static/', express.static(app.config('paths.static'), {maxAge: 1000 * 60 * 60 * 24 * 30}));
    }
    if (app.get('isDevelopment')) {
        app.use('/library/', express.static(path.join(app.config('paths.frontend'), 'library/')));
    }

    // Initialize licence.
    if (!!app.get('isPackaged')) {
        await licence(app);
    }

    // Initialize database connection.
    await initDB(app);

    // Initialize report database connection.
    await initReportDB(app);

    // Clear cache.
    if (app.get('isPackaged') && app.isMaster) {
        await app.exec('redis-cli FLUSHALL');
    }

    // Initialize collection system.
    app.configure(collection);

    // Initialize method system.
    app.configure(method);

    // Initialize socket.
    app.configure(initSocket);

    // Synchronize service events.
    if (app.get('isProduction') && os.cpus().length > 1) {
        app.configure(sync);
    }

    // Initialize auth.
    app.configure(auth);

    // Initialize persistent setting system.
    app.configure(setting);

    // Initialize translation system.
    app.configure(translation);

    // Initialize permission system.
    app.configure(permission);

    // Initialize rounding.
    app.configure(rounding);

    // Initialize formatter.
    app.configure(formatter);

    // Initialize jobs.
    app.configure(jobs);

    // Initialize queues.
    app.configure(queues);

    // Initialize mail.
    app.configure(mail);

    // Initialize print.
    app.configure(print);

    // Initialize converter.
    app.configure(converter);

    // Initialize extensions.
    app.configure(extensions);

    // Initialize programs.
    programs(app);

    // Initialize and serve files.
    app.configure(files);

    // Initialize backups.
    app.configure(backups);

    // Initialize logger.
    app.configure(logger);

    // Initialize application level hooks.
    app.hooks(appHooks);

    // Boot up.
    await boot(app);

    // Run migrations.
    if (app.get('isPackaged') && app.isMaster) {
        await migrations(app);
    }

    // Initialize api.
    app.configure(api);

    // Register frontend routes.
    app.configure(frontend);

    // Register history api fallback before error middleware
    // to use rest system.
    app.use(historyApiFallback());

    // Configure a middleware for 404s and the error handler.
    app.use(express.notFound());
    app.use(
        express.errorHandler({
            html: {
                401: path.resolve(app.config('paths.static'), '401.html'),
                404: path.resolve(app.config('paths.static'), '404.html'),
                default: path.resolve(app.config('paths.static'), 'default.html')
            },
            winston
        })
    );

    // TODO Create a middleware that matches /api/store routes to apply rate limit for ecommerce!

    // Configure real-time event publishing.
    app.configure(publish);

    // Initialize profiler.
    // if (app.get('isDevelopment')) {
    //     const {profiler} = require('feathers-profiler');
    //
    //     app.configure(profiler());
    // }

    // Start server.
    app.listen(3000, !!process.env.DEV_SERVER ? '0.0.0.0' : 'localhost', error => {
        if (error) {
            console.error(error);
        }
    });
}
