import path from 'path';
import fs from 'fs-extra';
import _ from 'lodash';
import mime from 'mime';
import sharp from 'sharp';
import Mailer from 'framework/mailer';
import compileHBS from 'framework/helpers/compile-hbs';

export default async function (app) {
    const mailer = new Mailer();

    app.set('mail.host', app.config('mail.host'));
    app.set('mail.port', app.config('mail.port'));
    app.set('mail.secure', app.config('mail.secure'));
    app.set('mail.auth.user', app.config('mail.auth.user'));
    app.set('mail.auth.password', app.config('mail.auth.password'));

    app.mail = async params => {
        const company = await app.collection('kernel.company').findOne({});

        if (!params.from) {
            params.from = `${company.name} <${company.email}>`;
        }

        if (params.layout !== false) {
            const mjml = require('mjml');
            const payload = _.defaultsDeep(params.payload || {}, {
                title: null,
                content: '',
                company: {
                    logo: company.logo,
                    name: company.name,
                    legalName: company.legalName,
                    phone: company.phone,
                    email: company.email,
                    website: company.website,
                    address: company.address.address
                }
            });

            // Get layout.
            const layout = await fs.readFile(path.join(app.config('paths.static'), 'templates/mail/layout.hbs'), {
                encoding: 'utf8'
            });

            // Convert logo.
            let logoUrl = app.absoluteUrl('static/images/default-logo.png');
            if (!!payload.company.logo && payload.company.logo.indexOf('static') === -1) {
                logoUrl = app.absoluteUrl(`files/${payload.company.logo}`);
            }

            payload.company.logo = logoUrl;
            // let logo = null;
            // let logoImageType = null;
            // if (!!payload.company.logo) {
            //     if (payload.company.logo.indexOf('static') !== -1) {
            //         const logoPath = path.join(process.cwd(), payload.company.logo);
            //
            //         logo = await fs.readFile(logoPath);
            //         logoImageType = mime.getType(logoPath);
            //     } else {
            //         let logoFileInfo = await app.files.findOne({
            //             _id: payload.company.logo
            //         });
            //
            //         logo = await app.files.read(payload.company.logo);
            //         logoImageType = logoFileInfo.contentType;
            //     }
            // } else {
            //     const logoPath = path.join(process.cwd(), '/static/images/default-logo.png');
            //
            //     logo = await fs.readFile(logoPath);
            //     logoImageType = mime.getType(logoPath);
            // }
            // const logoAsBase64 = (
            //     await sharp(logo)
            //         .resize({
            //             height: 40,
            //             fit: 'outside',
            //             withoutEnlargement: true
            //         })
            //         .toBuffer()
            // ).toString('base64');
            // payload.company.logo = `data:${logoImageType};base64,${logoAsBase64}`;

            if (params.html) {
                payload.content = `<mj-section><mj-column><mj-raw><div style="font-size: 16px;">${params.html}</div></mj-raw></mj-column></mj-section>`;

                delete params.html;
            } else if (params.text) {
                payload.content = `<mj-section><mj-column><mj-text align="left">${params.text}</mj-text></mj-column></mj-section>`;

                delete params.text;
            } else if (params.template && params.noContentWrapper) {
                payload.contentWithoutWrapper = params.template;

                delete params.template;
            } else if (params.template) {
                payload.content = params.template;

                delete params.template;
            }

            const result = mjml(await compileHBS(app, layout, payload), {fonts: {}});

            if (result.errors.length < 1) {
                params.html = result.html;
            } else {
                throw new Error('An error occurred while parsing mail template! Error: ' + result.errors[0].message);
            }
        }

        return mailer.send(params);
    };

    app.initMailer = async () => {
        let configUpdated = false;

        if (
            (app.setting('system.mailHost') && app.setting('system.mailHost') !== app.get('mail.host')) ||
            (app.setting('system.mailPort') && app.setting('system.mailPort') !== app.get('mail.port')) ||
            (_.isBoolean(app.setting('system.mailSecure')) &&
                app.setting('system.mailSecure') !== app.get('mail.secure')) ||
            (app.setting('system.mailAuthUser') && app.setting('system.mailAuthUser') !== app.get('mail.auth.user')) ||
            (app.setting('system.mailAuthPassword') &&
                app.setting('system.mailAuthPassword') !== app.get('mail.auth.password'))
        ) {
            app.set('mail.host', app.setting('system.mailHost'));
            app.set('mail.port', app.setting('system.mailPort'));
            app.set('mail.secure', app.setting('system.mailSecure'));
            app.set('mail.auth.user', app.setting('system.mailAuthUser'));
            app.set('mail.auth.password', app.setting('system.mailAuthPassword'));

            configUpdated = true;
        }

        if (
            app.get('mail.host') &&
            app.get('mail.port') &&
            app.get('mail.auth.user') &&
            app.get('mail.auth.password') &&
            (!mailer.isVerified || configUpdated)
        ) {
            const config = {
                host: app.get('mail.host'),
                port: app.get('mail.port'),
                secure: app.get('mail.secure'),
                user: app.get('mail.auth.user'),
                password: app.get('mail.auth.password')
            };

            if (!config.secure) {
                config.tls = {
                    requireTLS: false,
                    ignoreTLS: true,
                    rejectUnauthorized: false
                };
            }

            configUpdated = false;

            return mailer.connect(config);
        }
    };
}
