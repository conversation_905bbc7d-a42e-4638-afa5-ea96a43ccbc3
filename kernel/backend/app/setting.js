import _ from 'lodash';

export default async function (app) {
    app.settingsStore = {};

    app.setting = key => {
        const entry = app.settingsStore[key];

        if (_.isObject(entry)) {
            return !_.isUndefined(entry.value) ? entry.value : entry.default;
        }
    };

    app.setSetting = async (key, value) => {
        const entry = app.settingsStore[key];

        if (_.isObject(entry)) {
            app.settingsStore[key].value = entry.value = value;

            if ((await app.collection('kernel.settings').count({name: key})) < 1) {
                await app.collection('kernel.settings').create(entry);
            } else {
                await app.collection('kernel.settings').patch({name: key}, {value: value});
            }
        } else {
            let setting = {};

            setting.name = key;
            setting.value = value;

            app.settingsStore[key] = setting;

            await app.collection('kernel.settings').create(setting);
        }
    };
}
