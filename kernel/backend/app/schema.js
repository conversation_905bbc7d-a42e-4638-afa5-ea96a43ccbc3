import {clean, validate} from 'framework/schema';

export default function (app) {
    app.translateSchemaMessages = () => null;

    app.schema = {};
    app.schema.clean = async (schema, data, options = {}) => {
        return await clean(app, schema, data, options);
    };

    app.schema.validate = async (schema, data, options = {}) => {
        return await validate(app, schema, data, options);
    };
}
