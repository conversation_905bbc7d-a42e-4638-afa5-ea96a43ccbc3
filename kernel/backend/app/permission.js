import _ from 'lodash';

export default async function (app) {
    app.permissionsStore = {};

    app.checkPermission = async payload => {
        const {type = 'record', user, collection, method, id, query} = payload;
        const error = new app.errors.Forbidden(
            app.translate('You do not have sufficient permissions to perform this operation!')
        );

        // Disallow un-authenticated request.
        if (!_.isPlainObject(user) || _.isEmpty(user)) {
            throw error;
        }

        // Root user allowed all to do all operations.
        if (user.isRoot) {
            return true;
        }

        if (type === 'permission') {
            const permissionName = payload.name;

            // Get permissions.
            const mappedPermissions = app.get('mappedPermissions') || {};
            let permission = null;

            if (_.isPlainObject(mappedPermissions[user._id])) {
                permission = mappedPermissions[user._id][permissionName];

                if (!_.isPlainObject(permission)) {
                    permission = (user.permissions || []).find(p => p.name === permissionName);
                    mappedPermissions[user._id][permissionName] = permission;

                    app.set('mappedPermissions', mappedPermissions);
                }
            } else {
                permission = (user.permissions || []).find(p => p.name === permissionName);
                mappedPermissions[user._id] = {};
                mappedPermissions[user._id][permissionName] = permission;

                app.set('mappedPermissions', mappedPermissions);
            }

            if (!!permission) {
                return !!permission.canDo;
            }

            return false;
        } else if (type === 'record') {
            const collectionName = collection;
            const service = app.collection(collection);

            // Check permission only collection that has title.
            if (!service.title) {
                return true;
            }

            // Get permissions.
            const mappedRecordPermissions = app.get('mappedRecordPermissions') || {};
            let permission = null;

            if (_.isPlainObject(mappedRecordPermissions[user._id])) {
                permission = mappedRecordPermissions[user._id][collectionName];

                if (!_.isPlainObject(permission)) {
                    permission = (user.recordPermissions || []).find(p => p.name === collectionName);
                    mappedRecordPermissions[user._id][collectionName] = permission;

                    app.set('mappedRecordPermissions', mappedRecordPermissions);
                }
            } else {
                permission = (user.recordPermissions || []).find(p => p.name === collectionName);
                mappedRecordPermissions[user._id] = {};
                mappedRecordPermissions[user._id][collectionName] = permission;

                app.set('mappedRecordPermissions', mappedRecordPermissions);
            }

            // Check if the user has any permission for this collection.
            if (!_.isPlainObject(permission)) {
                throw error;
            }

            // Check read permission.
            if (method === 'find') {
                if (permission.read === 'no') {
                    throw error;
                }
            }

            // Check create permission.
            else if (method === 'create') {
                if (permission.create === 'no') {
                    throw error;
                }
            }

            // Check update permission.
            else if (method === 'update' || method === 'patch') {
                if (_.isString(id) && id) {
                    if (permission.update === 'no') {
                        throw error;
                    } else if (permission.update === 'owned') {
                        let countQuery = {
                            _id: id,
                            createdBy: user._id
                        };

                        if (service.assignable) {
                            countQuery = {
                                _id: id,
                                $or: [{createdBy: user._id}, {assignedBy: user._id}, {assignedTo: user._id}]
                            };
                        }

                        const userRecordCount = await app.collection(collectionName).count(countQuery);

                        if (userRecordCount < 1) {
                            throw error;
                        }
                    }
                } else if (_.isPlainObject(query)) {
                    if (permission.update === 'no') {
                        throw error;
                    } else if (permission.update === 'owned') {
                        let countQuery = {
                            _id: id || query._id,
                            createdBy: user._id
                        };

                        if (service.assignable) {
                            countQuery = {
                                _id: id || query._id,
                                $or: [{createdBy: user._id}, {assignedBy: user._id}, {assignedTo: user._id}]
                            };
                        }

                        const userRecordCount = await app.collection(collectionName).count(countQuery);

                        if (userRecordCount < 1) {
                            throw error;
                        }
                    }
                }
            }

            // Check delete permission.
            else if (method === 'remove') {
                if (_.isString(id) && id) {
                    if (permission.delete === 'no') {
                        throw error;
                    } else if (permission.delete === 'owned') {
                        let countQuery = {
                            _id: id,
                            createdBy: user._id
                        };

                        if (service.assignable) {
                            countQuery = {
                                _id: id,
                                $or: [{createdBy: user._id}, {assignedBy: user._id}, {assignedTo: user._id}]
                            };
                        }

                        const userRecordCount = await app.collection(collectionName).count(countQuery);

                        if (userRecordCount < 1) {
                            throw error;
                        }
                    }
                } else if (_.isPlainObject(query)) {
                    if (permission.delete === 'no') {
                        throw error;
                    } else if (permission.delete === 'owned') {
                        let countQuery = {
                            _id: id || query._id,
                            createdBy: user._id
                        };

                        if (service.assignable) {
                            countQuery = {
                                _id: id || query._id,
                                $or: [{createdBy: user._id}, {assignedBy: user._id}, {assignedTo: user._id}]
                            };
                        }

                        const userRecordCount = await app.collection(collectionName).count(countQuery);

                        if (userRecordCount < 1) {
                            throw error;
                        }
                    }
                }
            }
        }
    };
}
