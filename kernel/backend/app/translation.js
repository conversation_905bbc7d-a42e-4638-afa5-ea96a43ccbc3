import _ from 'lodash';
import {template} from 'framework/helpers';

export default function (app) {
    app.translationsStore = {};

    // const reportedKeys = [];
    // const reportUntranslated = async (locale, key) => {
    //     const collection = app.collection('kernel.translations');
    //
    //     if ((await collection.count({key})) < 1) {
    //         collection.create({
    //             locale,
    //             key,
    //             program: 'unknown'
    //         });
    //     }
    // };

    // const mappedStore = {};

    app.translate = (key, locale, params) => {
        let message = '';

        if (!!locale && typeof locale === 'object') {
            params = locale;
            locale = app.config('app.locale');
        } else if (typeof locale !== 'string') {
            locale = app.config('app.locale');
        }

        let store = app.translationsStore[locale];
        if (typeof store === 'object') {
            const translation = store[key];

            if (typeof translation === 'object') {
                message = !!translation.message ? translation.message : translation.key;
            } else {
                message = key;
            }
        } else {
            message = key;
        }

        if (typeof params !== 'object') {
            return message;
        }

        return template(message, params);
    };
}
