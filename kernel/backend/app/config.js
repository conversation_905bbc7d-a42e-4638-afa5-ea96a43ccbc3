import path from 'path';
import fs from 'fs';
import _ from 'lodash';
import config from 'framework/config';

export default function (app) {
    const appConfig = JSON.parse(fs.readFileSync(path.join(process.cwd(), 'config.json')));

    config.setStore(appConfig);

    const configMap = {};

    app.config = (key, value) => {
        if (_.isUndefined(value)) {
            if (!_.isUndefined(configMap[key])) return configMap[key];

            const hasKey = config.has(key);
            let result = null;

            if (!hasKey) result = null;
            else result = config.get(key);

            configMap[key] = result;

            return result;
        } else {
            config.set(key, value);

            delete configMap[key];

            return this;
        }
    };

    // Is packaged.
    app.set('isPackaged', app.config('app.packaged') || process.env.IS_PACKAGED === 'YES' || !!global.isPackaged);
    app.set('isBundled', app.isBundled);

    // Paths.
    if (app.get('isPackaged')) app.config('paths.backend', path.join(process.cwd(), 'backend/'));
    else app.config('paths.backend', path.join(process.cwd(), 'storage/build/backend/'));
    if (app.get('isPackaged')) app.config('paths.frontend', path.join(process.cwd(), 'frontend/'));
    else app.config('paths.frontend', path.join(process.cwd(), 'storage/build/frontend/'));
    app.config('paths.bin', path.join(process.cwd(), 'bin/'));
    app.config('paths.vendors', path.join(app.config('paths.frontend'), 'vendors/'));
    app.config('paths.assets', path.join(app.config('paths.frontend'), 'assets/'));
    app.config('paths.cache', path.join(process.cwd(), 'storage/cache/'));
    app.config('paths.temp', path.join(process.cwd(), 'storage/temp/'));
    app.config('paths.files', path.join(process.cwd(), 'storage/files/'));
    app.config('paths.static', path.join(process.cwd(), 'static'));

    // Public configurations.
    const publicParams = app.get('publicParams') || {};
    publicParams.config = {
        app: app.config('app'),
        server: app.config('server')
    };
    app.set('publicParams', publicParams);
}
