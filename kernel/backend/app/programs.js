import _ from 'lodash';
import fastCopy from 'fast-copy';

const programSchema = {
    name: 'string',
    title: 'string',
    icon: 'string',
    hasFlatIcon: {
        type: 'boolean',
        default: false
    },
    hasLineIcon: {
        type: 'boolean',
        default: false
    },
    navigation: {
        type: 'boolean',
        default: true
    },
    color: 'string',
    version: 'string',
    order: 'integer',
    window: {
        type: 'object',
        blackbox: true,
        required: false
    },
    collections: [
        {
            type: 'object',
            blackbox: true
        }
    ],
    migrations: [
        {
            type: 'object',
            blackbox: true
        }
    ],
    schemas: [
        {
            type: 'object',
            blackbox: true
        }
    ],
    methods: [
        {
            type: 'object',
            blackbox: true
        }
    ],
    jobs: [
        {
            type: 'object',
            blackbox: true
        }
    ],
    settings: [
        {
            type: 'object',
            blackbox: true
        }
    ],
    permissions: [
        {
            type: 'object',
            blackbox: true
        }
    ],
    translations: {
        type: 'object',
        blackbox: true,
        required: false
    },
    records: [
        {
            type: 'object',
            blackbox: true
        }
    ],
    templates: [
        {
            type: 'object',
            blackbox: true
        }
    ],
    widgets: [
        {
            type: 'object',
            blackbox: true,
            required: false
        }
    ],
    init: {
        type: 'object',
        blackbox: true,
        required: false
    }
};
const programDefaults = {
    hasFlatIcon: false,
    hasLineIcon: false,
    navigation: true,
    window: {
        active: false,
        toolbar: true,
        maximized: false,
        minimized: false,
        draggable: true,
        resizable: true,
        minimizable: true,
        maximizable: true,
        closable: true,
        width: 1280,
        height: 720,
        minWidth: 820,
        minHeight: 480,
        top: 15,
        left: 15,
        zIndex: 100
    },
    migrations: [],
    schemas: [],
    methods: [],
    jobs: [],
    collections: [],
    settings: [],
    permissions: [],
    translations: {},
    records: [],
    templates: [],
    widgets: []
};
const programMigrations = [];
const programSchemas = {};
const programTranslations = [];
const programWidgets = [];
const publicParams = [];
const kernelPublicParam = {
    name: 'kernel',
    collections: []
};
const initFns = [];
let programSeeds = [];

function normalizeCollectionSchemaForClient(schema) {
    _.each(schema, (prop, key) => {
        if (_.isPlainObject(prop) && _.isUndefined(prop.type)) {
            return normalizeCollectionSchemaForClient(prop);
        } else {
            if (_.isPlainObject(prop)) {
                if (_.isFunction(prop.default)) {
                    prop.required = false;
                    prop.optional = true;
                    delete prop.default;
                }

                if (_.isFunction(prop.auto)) {
                    prop.required = false;
                    prop.optional = true;
                    delete prop.auto;
                }
            }
        }
    });

    return schema;
}

export default function (app) {
    const templates = {};

    // Initialize addons.
    for (const addon of global.__addons || []) {
        if (!(global.__programs || []).some(p => p.name === addon.name)) {
            (global.__programs || []).push(addon);
        } else {
            throw new Error(`Addon name "${addon.name}" is used by one of the internal modules.`);
        }
    }

    // Register schemas first.
    (global.__programs || []).map(p => {
        const schemas = p.schemas || [];

        if (schemas.length > 0) {
            for (const schema of schemas) {
                if (schema.name.indexOf('.') === -1) {
                    schema.name = p.name + '.' + schema.name;
                }

                programSchemas[schema.name] = {
                    programName: p.name,
                    programTitle: p.title,
                    ...schema
                };
            }
        }
    });
    app.set('programSchemas', programSchemas);

    let programs = (global.__programs || []).map(p => {
        let publicParam = {};

        // // Validate definition.
        // Schema.check(p, programSchema);

        // Apply defaults.
        p = _.defaultsDeep(p, programDefaults);

        // Initialize public params.
        publicParam = _.omit(p, [
            'collections',
            'methods',
            'translations',
            'jobs',
            'migrations',
            'records',
            'settings',
            'permissions',
            'init'
        ]);

        // Register collections.
        publicParam.collections = [];
        p.collections.forEach(c => {
            const name = c.name.indexOf('.') === -1 ? p.name + '.' + c.name : c.name;

            const extensionsKey = `collection.${name}`;
            const extensions = app.extensionsMap[extensionsKey];
            if (Array.isArray(extensions)) {
                for (const extension of extensions) {
                    if (_.isPlainObject(extension)) {
                        c = _.merge(c, extension);
                    } else if (_.isFunction(extension)) {
                        c = _.merge(c, extension(app) || {});
                    }
                }
            }

            app.collection(name, c);

            const publicCollection = {
                name,
                title: c.title,
                order: !!c.order,
                tree: !!c.tree,
                localCache: !!c.localCache,
                branch: c.branch,
                assignable: !!c.assignable,
                revisions: c.revisions,
                view: c.view,
                masterView: c.masterView,
                detailView: c.detailView,
                labelParams: c.labelParams,
                locationFrom: c.locationFrom,
                timestamps: c.timestamps !== false,
                uid: c.uid !== false,
                canCopy: !!c.copy,
                hasSearchTerms: _.isFunction(c.searchTerms),
                schema: normalizeCollectionSchemaForClient(fastCopy(c.schema))
            };
            if (_.isObject(c.attributes)) {
                publicCollection.populations = Object.keys(c.attributes);

                publicCollection.attributes = [];
                _.each(c.attributes, (prop, field) => {
                    if (!_.isFunction(prop)) {
                        const attribute = prop;

                        attribute.field = field;
                        if (_.isFunction(attribute.computed)) {
                            attribute.isComputed = true;
                            delete attribute.computed;
                        }

                        publicCollection.attributes.push(attribute);
                    }
                });
            }
            publicParam.collections.push(publicCollection);
        });

        // Register migrations.
        if (p.migrations.length > 0) {
            for (const migration of p.migrations) {
                if (!migration.version || !_.isFunction(migration.action)) {
                    continue;
                }

                migration.name = p.name + '.' + migration.version;

                programMigrations.push({
                    programName: p.name,
                    programTitle: p.title,
                    ...migration
                });
            }
        }

        // Register methods.
        p.methods.forEach(m => {
            if (Array.isArray(m)) {
                m.forEach(sm => {
                    let name = sm.name.indexOf('.') === -1 ? p.name + '.' + sm.name : sm.name;

                    const extensionsKey = `method.${name}`;
                    const extensions = app.extensionsMap[extensionsKey];
                    if (Array.isArray(extensions)) {
                        for (const extension of extensions) {
                            if (_.isPlainObject(extension)) {
                                sm = _.assign(sm, extension);
                            } else if (_.isFunction(extension)) {
                                sm = _.assign(sm, extension(app) || {});
                            }
                        }
                    }

                    app.method(name, sm);
                });
            } else {
                let name = m.name.indexOf('.') === -1 ? p.name + '.' + m.name : m.name;

                const extensionsKey = `method.${name}`;
                const extensions = app.extensionsMap[extensionsKey];
                if (Array.isArray(extensions)) {
                    for (const extension of extensions) {
                        if (_.isPlainObject(extension)) {
                            m = _.assign(m, extension);
                        } else if (_.isFunction(extension)) {
                            m = _.assign(m, extension(app) || {});
                        }
                    }
                }

                app.method(name, m);
            }
        });

        // Register jobs.
        p.jobs.forEach(j => {
            if (app.hasModule(p.name)) {
                if (j.name.indexOf('.') === -1) {
                    j.name = p.name + '.' + j.name;
                }

                if (!!j.queued) {
                    app.registerQueuedJob(j);
                } else {
                    app.registerJob(j);
                }
            }
        });

        // Register settings.
        p.settings.forEach(s => {
            s.name = s.name.indexOf('.') === -1 ? p.name + '.' + s.name : s.name;

            app.settingsStore[s.name] = s;
        });

        // Register permissions.
        p.permissions.forEach(permission => {
            permission.name = permission.name.indexOf('.') === -1 ? p.name + '.' + permission.name : permission.name;

            app.permissionsStore[permission.name] = permission;
        });

        // Initialize translations.
        // if (app.get('isInitializing') === true || app.get('isDevelopment') === true) {
        //     _.each(p.translations, (translations, locale) => {
        //         _.each(translations, (message, key) => {
        //             if (!_.isObject(programTranslations.find(record => record.locale === locale && record.key === key))) {
        //                 programTranslations.push({
        //                     program: p.name,
        //                     locale,
        //                     key,
        //                     message
        //                 });
        //             }
        //         });
        //     });
        // } else {
        //     delete p.translations;
        // }
        _.each(p.translations, (translations, locale) => {
            _.each(translations, (message, key) => {
                if (!_.isObject(programTranslations.find(record => record.locale === locale && record.key === key))) {
                    programTranslations.push({
                        program: p.name,
                        locale,
                        key,
                        message
                    });
                }
            });
        });

        // Register templates.
        p.templates.forEach(t => {
            if (t.name.indexOf('.') === -1) {
                t.name = p.name + '.' + t.name;
            }

            t.programName = p.name;
            t.programTitle = p.title;
            t.contentType = t.contentType || 'html';
            t.outputType = t.outputType || 'pdf';
            t.outputOrientation = t.outputOrientation || 'portrait';
            t.outputFormat = t.outputFormat || 'A4';
            t.outputWidth = t.outputWidth || 210;
            t.outputHeight = t.outputHeight || 297;

            templates[t.name] = t;
        });

        // Register widgets.
        if (p.widgets.length > 0) {
            for (const widget of p.widgets.filter(w => _.isObject(w.definition))) {
                if (typeof widget.method === 'function') {
                    const name = p.name + '.widget-methods.' + widget.name;

                    app.method(name, widget.method);
                }

                programWidgets.push({
                    program: p.name,
                    name: widget.name,
                    ...widget.definition
                });
            }
        }

        // Init scripts.
        if (_.isFunction(p.init)) {
            initFns.push(p.init);
        }

        // Public params
        publicParams.push(publicParam);

        return p;
    });

    // Register kernel collections.
    const resolvedCollections = require.context('../collections/', false, /^\.\/([\w-_]+)\.js/);
    _.each(resolvedCollections.keys(), key => {
        let c = resolvedCollections(key).default,
            name = 'kernel.' + c.name;

        app.collection(name, c);

        const extensionsKey = `collection.${name}`;
        const extensions = app.extensionsMap[extensionsKey];
        if (Array.isArray(extensions)) {
            for (const extension of extensions) {
                if (_.isPlainObject(extension)) {
                    c = _.merge(c, extension);
                } else if (_.isFunction(extension)) {
                    c = _.merge(c, extension(app) || {});
                }
            }
        }

        const publicCollection = {
            name,
            title: c.title,
            order: !!c.order,
            tree: !!c.tree,
            localCache: !!c.localCache,
            branch: c.branch,
            assignable: !!c.assignable,
            revisions: c.revisions,
            view: c.view,
            masterView: c.masterView,
            detailView: c.detailView,
            labelParams: c.labelParams,
            locationFrom: c.locationFrom,
            timestamps: c.timestamps !== false,
            uid: c.uid !== false,
            canCopy: !!c.copy,
            hasSearchTerms: _.isFunction(c.searchTerms),
            schema: normalizeCollectionSchemaForClient(fastCopy(c.schema))
        };
        if (_.isObject(c.attributes)) {
            publicCollection.populations = Object.keys(c.attributes);

            publicCollection.attributes = [];
            _.each(c.attributes, (prop, field) => {
                if (!_.isFunction(prop)) {
                    const attribute = prop;

                    attribute.field = field;
                    if (_.isFunction(attribute.computed)) {
                        attribute.isComputed = true;
                        delete attribute.computed;
                    }

                    publicCollection.attributes.push(attribute);
                }
            });
        }
        kernelPublicParam.collections.push(publicCollection);
    });

    // Register kernel methods.
    const resolvedMethods = require.context('../methods/', true, /([\w-_]+)\.js$/);
    _.each(resolvedMethods.keys(), key => {
        let m = resolvedMethods(key).default;

        if (Array.isArray(m)) {
            m.forEach(sm => {
                let name = 'kernel' + '.' + sm.name;

                const extensionsKey = `method.${name}`;
                const extensions = app.extensionsMap[extensionsKey];
                if (Array.isArray(extensions)) {
                    for (const extension of extensions) {
                        if (_.isPlainObject(extension)) {
                            m = _.assign(m, extension);
                        } else if (_.isFunction(extension)) {
                            m = _.assign(m, extension(app) || {});
                        }
                    }
                }

                app.method(name, sm);
            });
        } else {
            let name = 'kernel' + '.' + m.name;

            const extensionsKey = `method.${name}`;
            const extensions = app.extensionsMap[extensionsKey];
            if (Array.isArray(extensions)) {
                for (const extension of extensions) {
                    if (_.isPlainObject(extension)) {
                        m = _.assign(m, extension);
                    } else if (_.isFunction(extension)) {
                        m = _.assign(m, extension(app) || {});
                    }
                }
            }

            app.method(name, m);
        }
    });

    // Register kernel jobs.
    const resolvedJobs = require.context('../jobs/', false, /([\w-_]+)\.js$/);
    _.each(resolvedJobs.keys(), key => {
        let j = resolvedJobs(key).default;

        if (j.name.indexOf('.') === -1) {
            j.name = 'kernel.' + j.name;
        }

        if (!!j.queued) {
            app.registerQueuedJob(j);
        } else {
            app.registerJob(j);
        }
    });

    // Register migrations.
    app.set('programMigrations', programMigrations);

    // Update translation collection.
    // if (app.get('isInitializing') === true && programTranslations.length > 0) {
    //     await app.collection('kernel.translations').remove();
    //     await app.collection('kernel.translations').create(programTranslations);
    //
    //     const translations = await app.collection('kernel.translations').find();
    //
    //     app.translationsStore = _.groupBy(translations.map(t => _.omit(t, '_id')), 'locale');
    // }
    // if (app.get('isDevelopment') === true && programTranslations.length > 0) {
    //     app.translationsStore = _.groupBy(programTranslations, 'locale');
    // }
    app.set('programTranslations', programTranslations);

    // Register templates.
    app.set('templates', templates);

    // Register widgets.
    app.set('programWidgets', programWidgets);

    // Set public params.
    publicParams.push(kernelPublicParam);
    app.set(
        'publicParams',
        _.assign(app.get('publicParams') || {}, {
            programs: publicParams
        })
    );

    // Register init functions.
    app.set('initFns', initFns);
}
