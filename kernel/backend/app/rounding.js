export default function (app) {
    app.round = (number, type = 'currency') => {
        switch (type) {
            case 'currency':
                return app.roundNumber(number, app.setting('system.currencyPrecision'));
            case 'unit-price':
                return app.roundNumber(number, app.setting('system.unitPricePrecision'));
            case 'unit':
                return app.roundNumber(number, app.setting('system.unitPrecision'));
            case 'amount':
                return app.roundNumber(number, app.setting('system.amountPrecision'));
            case 'percentage':
                return app.roundNumber(number, app.setting('system.percentagePrecision'));
            case 'total':
                return app.roundNumber(number, app.setting('system.totalPrecision'));
            case 'exchange-rate':
                return app.roundNumber(number, app.setting('system.exchangeRatePrecision'));
            default:
                return app.roundNumber(number, 2);
        }
    };
}
