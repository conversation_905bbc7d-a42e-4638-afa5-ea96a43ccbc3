import path from 'path';
import fs from 'fs-extra';
import mime from 'mime';
import _ from 'lodash';
import sharp from 'sharp';
import pdfMakePrinter from 'pdfmake';
import QRCode from 'qrcode';
import Random from 'framework/random';

export default function (app) {
    app.print = (payload = {}) => {
        return new Promise(async (resolve, reject) => {
            const company = await app.collection('kernel.company').findOne({});
            const saveOutput = !!payload.saveOutput;
            const title = payload.title || app.translate('Document');
            const definition = _.defaultsDeep(payload.definition, {
                defaultStyle: {
                    color: '#212121'
                },
                pageMargins: [30, 90, 30, 50],
                info: {
                    title: title,
                    author: `${app.config('app.title')} v${app.config('app.version')}`
                }
                // ...(!!payload.landscape ? {pageOrientation: 'landscape'} : {})
            });
            if (!!payload.landscape) {
                definition.pageOrientation = 'landscape';

                // delete payload.landscape;
            }

            // Fix definition.
            if (!Array.isArray(definition.content)) {
                definition.content = [];
            }

            // Build page header.
            if (payload.header !== false) {
                // Convert logo
                let logo = null;
                let logoImageType = null;
                if (!!company.logo) {
                    if (company.logo.indexOf('static') !== -1) {
                        const logoPath = path.join(process.cwd(), company.logo);

                        logo = await fs.readFile(logoPath);
                        logoImageType = mime.getType(logoPath);
                    } else {
                        let logoFileInfo = await app.files.findOne({
                            _id: company.logo
                        });

                        try {
                            logo = await app.files.read(company.logo);
                            logoImageType = logoFileInfo.contentType;
                        } catch (e) {
                            logo = null;
                        }

                        if (!logo) {
                            const logoPath = path.join(app.config('paths.static'), '/images/default-logo.png');

                            logo = await fs.readFile(logoPath);
                            logoImageType = mime.getType(logoPath);
                        }
                    }
                } else {
                    const logoPath = path.join(app.config('paths.static'), '/images/default-logo.png');

                    logo = await fs.readFile(logoPath);
                    logoImageType = mime.getType(logoPath);
                }
                const image = sharp(logo).resize({
                    height: 512,
                    fit: 'outside',
                    withoutEnlargement: true
                });
                const logoAsBase64 = (await image.toBuffer()).toString('base64');
                logo = `data:${logoImageType};base64,${logoAsBase64}`;
                const metadata = await image.metadata();
                const logoWidth = (30 * metadata.width) / metadata.height;
                let qr = null;

                if (_.isPlainObject(payload.qr)) {
                    qr = await QRCode.toDataURL(payload.qr.text, _.omit(payload.qr, 'text') || {});
                }

                // Add page header.
                definition.header = () => [
                    {
                        columns: [
                            {
                                image: logo,
                                fit: [logoWidth, 30]
                            },
                            {
                                text: title,
                                fontSize: 14,
                                bold: true,
                                alignment: 'right',
                                margin: [0, 7, 0, 0],
                                width: 'auto',
                                noWrap: true
                            }
                        ].concat(
                            !!qr
                                ? [
                                      {
                                          image: qr,
                                          alignment: 'right',
                                          fit: [30, 30],
                                          width: 45
                                      }
                                  ]
                                : []
                        ),
                        margin: [30, 30, 30, 0]
                    },
                    {
                        canvas: [
                            {
                                type: 'line',
                                x1: 30,
                                y1: 10,
                                x2: !!payload.landscape ? 842 - 30 : 595 - 30,
                                y2: 10,
                                lineWidth: 0.5,
                                lineColor: '#bdc3c7'
                            }
                        ]
                    }
                ];
            }

            if (payload.footer !== false) {
                definition.footer = (currentPage, pageCount) => {
                    return [
                        {
                            canvas: [
                                {
                                    type: 'line',
                                    x1: 30,
                                    y1: 10,
                                    x2: !!payload.landscape ? 842 - 30 : 595 - 30,
                                    y2: 10,
                                    lineWidth: 0.5,
                                    lineColor: '#bdc3c7'
                                }
                            ]
                        },
                        {
                            columns: [
                                {
                                    text: app.translate('Page {{currentPage}} / {{pageCount}}', {
                                        currentPage,
                                        pageCount
                                    }),
                                    fontSize: 9,
                                    margin: [30, 5, 0, 0]
                                },
                                {
                                    text: app.translate('Created by {{appName}}', {appName: app.config('app.title')}),
                                    alignment: 'right',
                                    fontSize: 9,
                                    margin: [0, 5, 30, 0]
                                }
                            ]
                        }
                    ];
                };
            }

            try {
                const fontDescriptors = {
                    Roboto: {
                        normal: 'static/fonts/Roboto-Regular.ttf',
                        bold: 'static/fonts/Roboto-Medium.ttf',
                        italics: 'static/fonts/Roboto-Italic.ttf',
                        bolditalics: 'static/fonts/Roboto-MediumItalic.ttf'
                    }
                };
                const printer = new pdfMakePrinter(fontDescriptors);
                const document = printer.createPdfKitDocument(definition);
                const now = app.datetime.local();

                let filePath = path.join(app.config('paths.temp'), `${Random.id(32)}.pdf`);
                if (saveOutput) {
                    let directoryPath = app.get('isPackaged')
                        ? app.setting('system.uploadDirectoryPath')
                        : app.config('paths.files');
                    if (directoryPath[0] !== '/') {
                        directoryPath = path.join(process.cwd(), directoryPath);
                    }
                    directoryPath = path.join(
                        directoryPath,
                        now.year.toString(),
                        now.month.toString(),
                        now.day.toString(),
                        'prints'
                    );

                    filePath = path.join(directoryPath, `${Random.id(32)}.pdf`);
                }

                let fileId = null;
                const writeStream = await app.files.writeStream(
                    filePath,
                    {isTemporary: !saveOutput, name: `${title}.pdf`},
                    fileInfo => (fileId = fileInfo._id)
                );

                document.pipe(writeStream);

                document.on('end', () => {
                    resolve({
                        title,
                        url: app.absoluteUrl(`files/${fileId}`),
                        ...(saveOutput ? {fileId} : {})
                    });
                });

                document.end();
            } catch (error) {
                reject(error);
            }
        });
    };
}
