import _ from 'lodash';
import notepack from 'notepack.io';
import {hooks} from '@feathersjs/commons';

export const SYNC = Symbol('event-sync/enabled');

export default function (app) {
    if (app[SYNC]) {
        return;
    }

    app[SYNC] = true;

    app.on('sync-in', buffer => {
        const {event, path, data, context} = notepack.decode(buffer);
        const service = app.service(path);
        const hook = !!context ? Object.assign({app, service}, context) : context;

        // Find locale.
        let locale = 'en';

        if (!!context && !!context.params && !!context.params.user && !!context.params.user.locale) {
            locale = context.params.user.locale;
        } else {
            locale = app.config('app.locale') || 'en';
        }

        // Translation context method.
        if (!!hook) {
            hook.translate = (key, params = {}) => {
                return app.translate(key, locale, params);
            };
        }

        if (!!service) {
            service._emit(event, data, hook);
        }
    });

    app.mixins.push((service, path) => {
        if (typeof service._emit !== 'function') {
            service._emit = service.emit;

            service.mixin({
                emit(event, data, ctx) {
                    const disabled = (ctx && ctx[SYNC] === false) || service.sync === false;

                    if (!service._serviceEvents.includes(event) || disabled) {
                        return this._emit(event, data, ctx);
                    }

                    return app.emit(
                        'sync-out',
                        notepack.encode({
                            event,
                            path,
                            data,
                            context: hooks.isHookObject(ctx)
                                ? _.omit(ctx, ['app', 'service', 'translate', 'self'])
                                : ctx
                        })
                    );
                }
            });
        }
    });

    const pub = app.cache.client;
    const sub = pub.duplicate();
    const key = 'event-sync';

    app.on('sync-out', data => {
        pub.publish(key, data);
    });

    sub.subscribe(key);
    sub.on('messageBuffer', function (e, data) {
        if (e.toString() === key) {
            app.emit('sync-in', data);
        }
    });
}

/*

import _ from 'lodash';
import notepack from 'notepack.io';
import {hooks} from '@feathersjs/commons';

export const SYNC = Symbol('event-sync/enabled');

export default function (app) {
    if (app[SYNC]) {
        return;
    }

    app[SYNC] = true;

    app.on('sync-in', buffer => {
        const {event, path, data, context} = notepack.decode(buffer);
        const service = app.service(path);
        const hook = !!context ? Object.assign({app, service}, context) : context;

        // Find locale.
        let locale = 'en';

        if (!!context && !!context.params && !!context.params.user && !!context.params.user.locale) {
            locale = context.params.user.locale;
        } else {
            locale = app.config('app.locale') || 'en';
        }

        // Translation context method.
        if (!!hook) {
            hook.translate = (key, params = {}) => {
                return app.translate(key, locale, params);
            };
        }

        if (!!service) {
            service._emit(event, data, hook);
        }
    });

    app.mixins.push((service, path) => {
        if (typeof service._emit !== 'function') {
            service._emit = service.emit;

            service.mixin({
                emit(event, data, ctx) {
                    const disabled = (ctx && ctx[SYNC] === false) || service.sync === false;

                    if (!service._serviceEvents.includes(event) || disabled) {
                        return this._emit(event, data, ctx);
                    }

                    if (typeof path === 'string' && path.includes('/') && path.split('/')[0] !== 'kernel') {
                        return this._emit(event, data, ctx);
                    }

                    return app.emit(
                        'sync-out',
                        notepack.encode({
                            event,
                            path,
                            data,
                            context: hooks.isHookObject(ctx)
                                ? _.omit(ctx, ['app', 'service', 'translate', 'self'])
                                : ctx
                        })
                    );
                }
            });
        }
    });

    const pub = app.cache.client;
    const sub = pub.duplicate();
    const key = 'event-sync';

    app.on('sync-out', data => {
        pub.publish(key, data);
    });

    sub.subscribe(key);
    sub.on('messageBuffer', function (e, data) {
        if (e.toString() === key) {
            app.emit('sync-in', data);
        }
    });
}


* */
