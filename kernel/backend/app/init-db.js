import _ from 'lodash';
import {MongoClient} from 'mongodb';

export default async function (app) {
    let auth = null;

    try {
        auth = app.config('database.mongodb.auth');

        if (!_.isPlainObject(auth)) {
            auth = null;
        }
    } catch (e) {
        auth = null;
    }

    const dbUrl = !!auth
        ? `mongodb://${auth.username}:${auth.password}@${app.config('database.mongodb.host')}:${app.config(
              'database.mongodb.port'
          )}/${app.config('database.mongodb.db')}?authMechanism=DEFAULT&authSource=${auth.authSource}`
        : `mongodb://${app.config('database.mongodb.host')}:${app.config('database.mongodb.port')}/${app.config(
              'database.mongodb.db'
          )}`;
    const mongoClient = await MongoClient.connect(dbUrl, {
        appName: 'entererp'
        // monitorCommands: true
        // poolSize: 10,
        // useUnifiedTopology: true1
    });

    app.mongo = mongoClient;
    app.db = mongoClient.db(app.config('database.mongodb.db'));

    // mongoClient.on('commandStarted', event => {
    //     console.log(`Mongo: ${JSON.stringify(event.command, null, 2)}`);
    // });
}
