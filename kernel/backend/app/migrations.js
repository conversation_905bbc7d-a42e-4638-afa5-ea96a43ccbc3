import _ from 'lodash';

export default async function (app) {
    if (!app.isMaster) {
        return;
    }

    const isMigrationsRunning = !!(await app.cache.get('isMigrationsRunning'));
    if (isMigrationsRunning) {
        return;
    } else {
        await app.cache.set('isMigrationsRunning', true);
    }

    const migrations = (await app.get('programMigrations')) || [];

    for (const migration of migrations) {
        if (!migration.version || !migration.name || !_.isFunction(migration.action)) {
            continue;
        }

        const existingCount = await app.collection('kernel.migration-history').count({
            name: migration.name,
            version: migration.version
        });

        if (existingCount > 0) {
            continue;
        }

        try {
            await migration.action(app);

            await app.db.collection('kernel_migration-history').insertOne({
                name: migration.name,
                programName: migration.programName,
                programTitle: migration.programTitle,
                version: migration.version,
                date: app.datetime.local().toJSDate()
            });

            console.log(`Migration: ${migration.name} is completed.`);
        } catch (error) {
            console.log('---------------');
            console.log(`Migration: ${migration.name}`);
            console.log(error.message);
            console.log('---------------');
        }

        console.log('All migrations are completed.');
    }

    await app.cache.set('isMigrationsRunning', false);
}
