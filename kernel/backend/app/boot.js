import _ from 'lodash';
import esbuild from 'esbuild';
import process from 'node:process';
import notepack from 'notepack.io';
import {serialPromise} from 'framework/helpers';

export default async function (app) {
    // Sync the cache.
    (() => {
        const sub = app.realTimeEventSubscription;
        const onMessage = (channel, buffer) => {
            if (channel.toString() !== 'real-time-events') {
                return;
            }

            const payload = notepack.decode(buffer);

            if (!!payload && !!payload.data) {
                const pid = process.pid;
                const event = payload.event;
                const data = payload.data;

                if (event === 'kernel:clear-memory-cache' && !!data.cacheKey && data.pid !== pid) {
                    try {
                        app.memoryCache.clear(data.cacheKey, false);
                    } catch (e) {
                        console.log(e);
                    }
                }
            }
        };
        sub.on('messageBuffer', onMessage);
    })();

    // Sync settings.
    (await app.collection('kernel.settings').find()).forEach(s => {
        app.settingsStore[s.name] = {..._.omit(s, '_id'), persisted: true};
    });
    app.collection('kernel.settings').on('all', async () => {
        (await app.collection('kernel.settings').find()).forEach(s => {
            app.settingsStore[s.name] = {
                ..._.omit(s, '_id'),
                persisted: true
            };
        });

        // Initialize mailer.
        // noinspection ES6MissingAwait
        (async () => {
            try {
                await app.initMailer();
            } catch (error) {
                console.error(error);
            }
        })();
    });

    // Initialize translations.
    const translationsStore = _.groupBy(app.get('programTranslations'), 'locale');
    app.set('publicParams', {
        ...(app.get('publicParams') || {}),
        translations: {...translationsStore}
    });
    const mappedTranslationStore = {};
    _.each(translationsStore, (translations, locale) => {
        for (const translation of translations) {
            if (!_.isPlainObject(mappedTranslationStore[locale])) mappedTranslationStore[locale] = {};

            mappedTranslationStore[locale][translation.key] = translation;
        }
    });
    app.translationsStore = mappedTranslationStore;
    app.set('programTranslations', null);
    // if (app.get('isDevelopment') !== true) {
    //     const translations = await app.collection('kernel.translations').find();
    //     app.translationsStore = _.groupBy(translations.map(t => _.omit(t, '_id')), 'locale');
    // }
    // if (app.get('isInitializing') !== true && app.get('isSeeding') !== true) {
    //     app.collection('kernel.translations').on('all', async () => {
    //         const translations = await app.collection('kernel.translations').find();
    //
    //         app.translationsStore = _.groupBy(translations.map(t => _.omit(t, '_id')), 'locale');
    //
    //         app.set('publicParams', _.assign(app.get('publicParams') || {}, {
    //             translations: app.translationsStore
    //         }));
    //
    //         // Set public params script.
    //         setPublicParamsScript();
    //     });
    //
    //     app.set('publicParams', _.assign(app.get('publicParams') || {}, {
    //         translations: app.translationsStore
    //     }));
    // }

    // Inform fronted that app has or has not report-db.
    app.set('publicParams', {
        ...(app.get('publicParams') || {}),
        hasReportDb: !!app.reportDb
    });

    // Fix dead sessions.
    if (app.get('isPackaged') && app.isMaster) {
        const deadSessions = await app.collection('kernel.sessions').find({
            isOnline: true,
            $select: ['_id', 'startDate', 'lastActiveDate']
        });
        const operations = [];

        for (const deadSession of deadSessions) {
            operations.push({
                updateOne: {
                    filter: {_id: deadSession._id},
                    update: {
                        $set: {
                            isOnline: false,
                            endDate: deadSession.lastActiveDate || deadSession.startDate
                        }
                    }
                }
            });
        }

        if (operations.length > 0) {
            await app.collection('kernel.sessions').bulkWrite(operations);
        }
        await app.db.collection('kernel_sessions').deleteMany(
            {
                startDate: {$lt: app.datetime.local().startOf('year').toJSDate()}
            },
            {collation: {locale: app.config('app.locale')}}
        );
        await app.rpc('kernel.common.cancel-document-editing', {});

        await app.collection('kernel.maintenances').patch(
            {status: 'in-progress'},
            {
                status: 'error',
                message: 'Stopped due to system backup.'
            }
        );
    }

    // Delete method logs.
    if (app.isMaster) {
        await app.db.collection('kernel_method-logs').deleteMany(
            {
                startDate: {$lt: app.datetime.local().startOf('day').toJSDate()}
            },
            {collation: {locale: app.config('app.locale')}}
        );
    }

    // Initialize format options.
    const language = await app.collection('kernel.languages').findOne({
        isoCode: app.config('app.locale')
    });
    const company = await app.collection('kernel.company').findOne({});
    if (_.isPlainObject(language) && _.isPlainObject(company)) {
        const formatterOptions = {};

        formatterOptions.currency = {};
        formatterOptions.currency.name = company.currency.name;
        formatterOptions.currency.symbol = company.currency.symbol;
        formatterOptions.currency.format = company.currency.symbolPosition === 'before' ? '%s %v' : '%v %s';
        formatterOptions.currency.symbolPosition = company.currency.symbolPosition;
        formatterOptions.currency.decimal = language.decimalSeparator;
        formatterOptions.currency.thousand = language.thousandsSeparator;
        formatterOptions.currency.precision = app.setting('system.currencyPrecision');
        formatterOptions.number = {};
        formatterOptions.number.decimal = language.decimalSeparator;
        formatterOptions.number.thousand = language.thousandsSeparator;
        formatterOptions.number.precision = app.setting('system.amountPrecision');
        formatterOptions.date = language.dateFormat;
        formatterOptions.datetime = `${language.dateFormat} ${language.timeFormat}`;
        formatterOptions.time = language.timeFormat;

        app.set('formatterOptions', formatterOptions);
    }

    // Initialize mailer.
    // noinspection ES6MissingAwait
    (async () => {
        try {
            await app.initMailer();
        } catch (error) {
            console.error(error);
        }
    })();

    // Execute init functions.
    for (const fn of app.get('initFns')) {
        await fn(app);
    }
    app.set('initFns', null);

    // Set public params script.
    await setPublicParamsScript();

    async function setPublicParamsScript() {
        const publicParamsScript = JSON.stringify({
            ...app.get('publicParams'),
            clientVersion: app.get('clientVersion')
            // settings: app.settingsStore,
            // translations: translationsStore
        });
        let entererpJs = null;

        if (app.get('isProduction')) {
            const result = await esbuild.transform(`window.entererp = ${publicParamsScript};`, {
                loader: 'js',
                minify: true
            });

            entererpJs = result.code;
        } else {
            entererpJs = `window.entererp = ${publicParamsScript};`;
        }

        app.set('entererpJs', entererpJs);
    }
}
