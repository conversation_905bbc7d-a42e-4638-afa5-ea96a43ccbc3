import _ from 'lodash';
import socketio from 'framework/socket.io/server';
import * as socketParser from 'framework/socket.io/parsers/notepack';
import * as socketEJSONParser from 'framework/socket.io/parsers/ejson';

const pongTimes = {};

export default function (app) {
    app.configure(
        socketio(
            {
                path: '/ws/',
                serveClient: false,
                transports: ['websocket'],
                upgrade: false,
                maxHttpBufferSize: 1e7,
                wsEngine: require('ws').Server,
                // parser: app.get('isProduction') ? socketParser : socketEJSONParser
                parser: socketParser
            },
            function (io) {
                app.emit('socketio-initialized', io);

                io.sockets.setMaxListeners(0);

                io.use(function (socket, next) {
                    if (!!socket.handshake.headers && !!socket.handshake.headers['x-real-ip']) {
                        socket.handshake.address = socket.handshake.headers['x-real-ip'];
                    }

                    socket.feathers.ip = socket.handshake.address;
                    socket.feathers.socketId = socket.id;

                    socket.conn.on('packet', function (packet) {
                        if (packet.type === 'pong') {
                            pongTimes[socket.id] = new Date();
                        }
                    });

                    next();
                });
            }
        )
    );

    setInterval(async () => {
        for (const socketId of Object.keys(pongTimes)) {
            const lastPongTime = pongTimes[socketId];

            if (!!lastPongTime) {
                const now = new Date();

                if (now.getTime() - lastPongTime.getTime() > 1000 * 60 * 3) {
                    const session = await app.collection('kernel.sessions').findOne({socketId});

                    delete pongTimes[socketId];

                    if (_.isPlainObject(session)) {
                        await app.collection('kernel.sessions').patch(
                            {_id: session._id},
                            {
                                endDate: app.datetime.local().toJSDate(),
                                isOnline: false
                            },
                            {disableValidation: true}
                        );

                        app.rpc('kernel.common.cancel-document-editing', {userId: session.userId});
                    }
                }
            }
        }
    }, 5000);

    // app.configure(primus({
    //     transformer: 'websockets',
    //     parser: 'EJSON',
    //     iknowclusterwillbreakconnections: true
    // }, primus => {
    //     app.emit('primus-initialized');
    //
    //     primus.use('extra-params', function(req, res){
    //         // Exposing a request property to services and hooks
    //         req.feathers.ip = req.forwarded.ip;
    //     });
    // }));
}
