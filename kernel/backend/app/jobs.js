import _ from 'lodash';
import {Agenda} from '@hokify/agenda';

export default function (app) {
    const agenda = new Agenda({
        mongo: app.db,
        db: {collection: 'kernel_jobs'},
        processEvery: '60 seconds',
        ensureIndex: app.get('isPackaged') === true,
        defaultLockLifetime: 4 * 60 * 60 * 1000
    });
    let registeredJobs = [];

    app.registerJob = definition => {
        if (_.isObject(registeredJobs.find(j => j.name === definition.name))) {
            throw new Error(`Job name:"${definition.name}" is in use!`);
        } else {
            registeredJobs.push(definition);
        }

        agenda.define(definition.name, async job => {
            const runningJobs = (await app.cache.get('kernel.runningJobs')) || [];

            if (runningJobs.indexOf(definition.name) === -1) {
                // Update running jobs pool.
                runningJobs.push(definition.name);
                await app.cache.set('kernel.runningJobs', runningJobs);

                // Run job.
                await definition.action(app, job.attrs.data || {});

                // Re-Update running jobs pool.
                setTimeout(async () => {
                    const runningJobs = (await app.cache.get('kernel.runningJobs')) || [];
                    runningJobs.splice(runningJobs.indexOf(definition.name), 1);
                    await app.cache.set('kernel.runningJobs', runningJobs);
                }, 2500);
            }

            // if (!(await app.cache.has(cacheKey))) {
            //     await app.cache.set(cacheKey, true);
            //     await definition.action(app, job.attrs.data || {});
            //
            //     setTimeout(async () => {
            //         if (await app.cache.has(cacheKey)) {
            //             app.cache.delete(cacheKey);
            //         }
            //     }, 5000);
            // }
        });
    };

    app.runJob = (name, data = {}) => {
        const job = agenda.create(name, data);

        return job.run();
    };

    app.repeatJob = (interval, name, data = {}, options = {}) => {
        return agenda.every(interval, name, data, options);
    };

    app.scheduleJob = (when, name, data = {}) => {
        return agenda.schedule(when, name, data);
    };

    agenda.on('ready', async () => {
        await app.cache.set('kernel.runningJobs', []);

        if (app.get('isPackaged') === true && app.isMaster) {
            // Initialize running jobs pool.
            await app.db.collection('kernel_jobs').deleteMany({}, {collation: {locale: app.config('app.locale')}});

            for (const job of registeredJobs) {
                if (_.isString(job.repeatEvery)) {
                    const skipImmediate = _.isBoolean(job.skipImmediate) ? job.skipImmediate : true;

                    await agenda
                        .create(job.name, job.data || {})
                        .repeatEvery(job.repeatEvery, {
                            timezone: app.config('app.timezone'),
                            skipImmediate
                        })
                        .unique({name: job.name}, {insertOnly: true})
                        .save();
                } else if (_.isString(job.repeatAt)) {
                    await agenda
                        .create(job.name, job.data || {})
                        .repeatAt(job.repeatAt)
                        .unique({name: job.name}, {insertOnly: true})
                        .save();
                } else if (_.isString(job.schedule)) {
                    await agenda
                        .create(job.name, job.data || {})
                        .schedule(job.schedule)
                        .unique({name: job.name}, {insertOnly: true})
                        .save();
                }
            }

            await agenda.start();
        } else {
            await agenda.start();
        }
    });
}
