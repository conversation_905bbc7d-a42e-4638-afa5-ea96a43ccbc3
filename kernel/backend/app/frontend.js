import path from 'path';
import fs from 'fs';
import _ from 'lodash';
import {template} from 'framework/helpers';
import indexHtml from '../../frontend/index.html';

export default function (app) {
    const entererpJsPath = app.get('isProduction')
        ? `/assets/js/entererp.${app.get('clientVersion')}.js`
        : '/assets/js/entererp.js';
    const vendorsManifest = JSON.parse(fs.readFileSync(path.join(app.config('paths.vendors'), 'manifest.json')));
    const manifest = JSON.parse(fs.readFileSync(path.join(app.config('paths.assets'), 'manifest.json')));
    const allManifests = Object.values(vendorsManifest).concat(Object.values(manifest));
    const assets = {
        styles: [],
        scripts: []
    };
    allManifests.forEach(asset => {
        if (_.isString(asset.css)) {
            assets.styles.push(asset.css);
        } else if (Array.isArray(asset.css)) {
            for (const css of asset.css) {
                if (_.isString(css)) {
                    assets.styles.push(css);
                }
            }
        }

        if (_.isString(asset.js)) {
            assets.scripts.push(asset.js);
        } else if (Array.isArray(asset.js)) {
            for (const js of asset.js) {
                if (_.isString(js)) {
                    assets.scripts.push(js);
                }
            }
        }
    });
    assets.scripts.unshift(entererpJsPath);

    // PWA.
    if (app.get('isProduction')) {
        app.get('/manifest.json', (request, response) => {
            response.setHeader('Cache-Control', 'public, max-age=2592000');
            response.setHeader('X-Robots-Tag', 'noindex');

            return response.json({
                name: app.config('app.title'),
                short_name: app.config('app.title'),
                start_url: '.',
                display: 'standalone',
                background_color: '#009bd2',
                theme_color: '#034e6f',
                description: 'New generation enterprise resource planning software.',
                icons: [
                    {
                        src: '/static/images/app-icons/192x192.png',
                        type: 'image/png',
                        sizes: '192x192'
                    },
                    {
                        src: '/static/images/app-icons/512x512.png',
                        type: 'image/png',
                        sizes: '512x512'
                    }
                ]
            });
        });
        const offlineHtml = fs.readFileSync(path.resolve(app.config('paths.static'), './offline.html'), {
            encoding: 'utf8'
        });
        app.get('/offline.html', (request, response) => {
            if (app.get('isProduction')) {
                response.setHeader('Cache-Control', 'public, max-age=2592000');
            }

            response.setHeader('X-Robots-Tag', 'noindex');

            return response.send(offlineHtml);
        });
        app.get('/sw.js', (request, response) => {
            response.setHeader('Content-Type', 'text/javascript');

            if (app.get('isProduction')) {
                response.setHeader('Cache-Control', 'public, max-age=2592000');
            }

            response.setHeader('X-Robots-Tag', 'noindex');

            response.send(
                `
const CACHE_NAME = 'version-1';
const urlsToCache = ${JSON.stringify(['/index.html', '/offline.html'].concat(assets.styles.concat(assets.scripts)))};

const self = this;

// Install SW
self.addEventListener('install', (event) => {
    event.waitUntil(
        caches.open(CACHE_NAME)
        .then((cache) => {
            return cache.addAll(urlsToCache);
        })
    );
});

// Listen for requests
self.addEventListener('fetch', (event) => {
    event.respondWith(
        caches.match(event.request)
        .then(() => {
            return fetch(event.request)
            .catch(() => caches.match('offline.html'))
        })
    )
});

// Activate the SW
self.addEventListener('activate', (event) => {
    const cacheWhitelist = [];
    cacheWhitelist.push(CACHE_NAME);

    event.waitUntil(
        caches.keys().then((cacheNames) => Promise.all(
            cacheNames.map((cacheName) => {
                if(!cacheWhitelist.includes(cacheName)) {
                    return caches.delete(cacheName);
                }
            })
        ))
    )
});
        `.trim()
            );
        });
    }

    // Common resources.
    const html = template(indexHtml, {
        title: app.config('app.title'),
        isPackaged: app.get('isPackaged'),
        isDevelopment: app.get('isDevelopment'),
        isProduction: !app.get('isDevelopment'),
        lang: app.config('app.locale'),
        assets
    });
    const invalidLicenseHtml = fs.readFileSync(path.resolve(app.config('paths.static'), './invalid-license.html'), {
        encoding: 'utf8'
    });
    app.get(entererpJsPath, (request, response) => {
        if (app.get('isProduction')) {
            response.setHeader('Cache-Control', 'public, max-age=2592000');
        } else {
            response.setHeader('Cache-Control', 'private, no-cache, no-store, must-revalidate');
            response.setHeader('Expires', '-1');
            response.setHeader('Pragma', 'no-cache');
        }
        response.setHeader('Content-Type', 'text/javascript');
        response.setHeader('X-Robots-Tag', 'noindex');

        response.send(app.get('entererpJs'));
    });
    app.get('/client-version', (request, response) => {
        response.type('text/plain');

        // if (app.get('isProduction')) {
        //     response.setHeader('Cache-Control', 'public, max-age=2592000');
        // }

        response.setHeader('X-Robots-Tag', 'noindex');

        return response.send(app.get('clientVersion'));
    });
    // app.get('/robots.txt', (request, response) => {
    //     response.type('text/plain');
    //
    //     if (app.get('isProduction')) {
    //         response.setHeader('Cache-Control', 'public, max-age=2592000');
    //     }
    //
    //     response.send('User-agent: *\nDisallow: /');
    // });
    app.use('/', (request, response) => {
        const license = app.get('license');

        if (!!license && license.status !== 'active') {
            return response.send(invalidLicenseHtml);
        }

        response.setHeader('X-Robots-Tag', 'noindex');

        return response.send(html);
    });
}
