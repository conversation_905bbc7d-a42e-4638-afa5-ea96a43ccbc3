import _ from 'lodash';
import knex from 'knex';
import {rawMongoQuery, simpleHash} from 'framework/helpers';

export default async function (app) {
    let reportDb = null;

    knex.QueryBuilder.extend('fromMongoQuery', function (query) {
        applyMongoToKnex(rawMongoQuery(query), this);

        if (Array.isArray(query.$select) && query.$select.length > 0) {
            this.select(query.$select.map(field => _.snakeCase(field)));
        }
        if (_.isPlainObject(query.$sort)) {
            for (const field of Object.keys(query.$sort)) {
                this.orderBy(_.snakeCase(field), query.$sort[field] === -1 ? 'DESC' : 'ASC');
            }
        }
        if (_.isNumber(query.$limit)) {
            this.limit(query.$limit);
        }
        if (_.isNumber(query.$skip)) {
            this.offset(query.$skip);
        }

        return this;
    });

    try {
        const db = knex({
            client: 'pg',
            connection: {
                host: app.config('database.postgresql.host'),
                port: app.config('database.postgresql.port'),
                user: app.config('database.postgresql.user'),
                password: app.config('database.postgresql.password'),
                database: app.config('database.postgresql.db')
            }
            // debug: app.get('isDevelopment')
        });

        if (!!app.get('isProduction')) {
            await db.raw('SELECT 1');
        }

        reportDb = db;

        reportDb.substituteQueryFields = (query, fieldMap = {}) => {
            const newQuery = {};

            for (const field of Object.keys(query)) {
                if (field === '$and' || field === '$or') {
                    newQuery[field] = [];

                    for (const q of query[field]) {
                        const qs = {};

                        for (const subField of Object.keys(q)) {
                            const mapped = fieldMap[subField];

                            if (!!mapped) {
                                qs[mapped] = q[subField];
                            } else {
                                qs[subField] = q[subField];
                            }
                        }

                        newQuery[field].push(qs);
                    }
                } else {
                    const mapped = fieldMap[field];

                    if (!!mapped) {
                        newQuery[mapped] = query[field];
                    } else {
                        newQuery[field] = query[field];
                    }
                }
            }

            return newQuery;
        };

        reportDb.getDateBasedCachedRecords = async (queryBuilder, dateField, processRecords) => {
            const key = `krdbdbcr-${simpleHash(queryBuilder.clone().toQuery())}`;

            let lastRecord = await app.cache.get(key);
            if (!lastRecord) {
                lastRecord = await app.db.collection('kernel_report-db-date-based-cached-records').findOne(
                    {
                        key
                    },
                    {locale: app.config('app.locale')}
                );
            }

            const oldRecords = !!lastRecord ? lastRecord.records || [] : [];
            const newRecords = !!lastRecord
                ? await queryBuilder.where(dateField, '>', lastRecord.updatedAt)
                : await queryBuilder;
            const records = processRecords(oldRecords, newRecords);
            if (newRecords.length > 0) {
                try {
                    const payload = {
                        updatedAt: app.datetime.local().toJSDate(),
                        records
                    };

                    await app.cache.set(key, payload);
                    await app.db.collection('kernel_report-db-date-based-cached-records').updateOne(
                        {key},
                        {
                            $set: payload
                        },
                        {
                            upsert: true,
                            collation: {locale: app.config('app.locale')}
                        }
                    );
                } catch (error) {
                    console.log('Error while persisting report-db cache!');
                }
            }

            return records;
        };
    } catch (error) {
        console.error(error);

        reportDb = null;
    }

    app.reportDb = reportDb;
}

const OPERATORS = {
    $eq: '=',
    $ne: '<>',
    $lt: '<',
    $lte: '<=',
    $gt: '>',
    $gte: '>=',
    $in: 'in',
    $nin: 'not in',
    $exists: '$exists'
};

const OPERATORS_KNEX_METHOD = {
    $and: 'where',
    $or: 'orWhere'
};

function applyMongoToKnex(query, knex, parentKey, parentKnexMethodName) {
    Object.keys(query).forEach(key => {
        const value = query[key];

        if (_.isPlainObject(value)) {
            return applyMongoToKnex(value, knex, key);
        }

        // UI Scope regex query.
        if (key === '$or' && Array.isArray(value) && value.length > 0) {
            const qs = Object.values(value[0]);

            if (qs.length > 0) {
                const q = qs[0];

                if (!!q.$regex) {
                    const column = _.snakeCase(Object.keys(value[0])[0]);
                    const r = q.$regex;
                    const search = r.replace('^((?!', '').replace(').)*$', '').replace('^', '').replace('$', '');

                    if (!!r) {
                        if (r.startsWith('^((?!') && r.endsWith(').)*$')) {
                            return knex[parentKnexMethodName === 'orWhere' ? 'orWhere' : 'where'](
                                column,
                                'not ilike',
                                `%${search}%`
                            );
                        } else if (r.startsWith('^')) {
                            return knex[parentKnexMethodName === 'orWhere' ? 'orWhereILike' : 'whereILike'](
                                column,
                                `${search}%`
                            );
                        } else if (r.endsWith('$')) {
                            return knex[parentKnexMethodName === 'orWhere' ? 'orWhereILike' : 'whereILike'](
                                column,
                                `%${search}`
                            );
                        } else {
                            return knex[parentKnexMethodName === 'orWhere' ? 'orWhereILike' : 'whereILike'](
                                column,
                                `%${search}%`
                            );
                        }
                    }
                }
            }
        } else if (key === '$regex') {
            const column = _.snakeCase(parentKey);

            return knex.whereRaw(`"${column}" ~ '${value}'`);
        }

        const knexMethodName = OPERATORS_KNEX_METHOD[key];
        if (knexMethodName) {
            return queryByMethod(knex, key, value, knexMethodName);
        }

        const column = _.snakeCase(parentKey || key);
        const operator = OPERATORS[key] || '=';
        const methodName = parentKnexMethodName || 'where';

        if (operator === '$exists') {
            return value === true ? knex.whereNotNull(column) : knex.whereNull(column);
        }
        if (value === null) {
            if (operator === '=') {
                return knex.whereNull(column);
            } else if (operator === '<>') {
                return knex.whereNotNull(column);
            }
        }

        return knex[methodName](column, operator, value);
    });
}

function queryByMethod(knex, key, value, knexMethodName) {
    if (!Array.isArray(value)) {
        throw new Error(`${key} expect an array value`);
    }

    knex.where(function () {
        value.forEach(item => applyMongoToKnex(item, this, null, knexMethodName));
    });
}
