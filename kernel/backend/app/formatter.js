import _ from 'lodash';
import fastCopy from 'fast-copy';
import Numeral from 'framework/numeral';
import Big from 'big.js';

export default function (app) {
    app.format = (value, format, extra = {}) => {
        const FORMATS = [
            'currency',
            'money',
            'amount',
            'percentage',
            'unit-price',
            'unit',
            'integer',
            'formatted-integer',
            'number',
            'decimal',
            'total',
            'shorten-number',
            'date',
            'datetime',
            'time',
            'file-size'
        ];

        const useOutputPrecision =
            _.isPlainObject(extra) && _.isBoolean(extra.useOutputPrecision) ? extra.useOutputPrecision : false;

        // Get options.
        let options = fastCopy(app.get('formatterOptions'));
        let precision = 2;
        if (format === 'currency') {
            precision = app.setting('system.currencyPrecision');
            if (useOutputPrecision) precision = app.setting('system.outputCurrencyPrecision');
        } else if (format === 'amount' || format === 'decimal') {
            precision = app.setting('system.amountPrecision');
            if (useOutputPrecision) precision = app.setting('system.outputAmountPrecision');
        } else if (format === 'percentage') {
            precision = app.setting('system.percentagePrecision');
            if (useOutputPrecision) precision = app.setting('system.outputPercentagePrecision');
        } else if (format === 'unit-price') {
            precision = app.setting('system.unitPricePrecision');
            if (useOutputPrecision) precision = app.setting('system.outputUnitPricePrecision');
        } else if (format === 'unit') {
            precision = app.setting('system.unitPrecision');
            if (useOutputPrecision) precision = app.setting('system.outputUnitPrecision');
        } else if (format === 'total') {
            precision = app.setting('system.totalPrecision');
            if (useOutputPrecision) precision = app.setting('system.outputTotalPrecision');
        }
        options.currency.precision = precision;
        options.number.precision = precision;
        _.merge(options, fastCopy(extra));

        const numeral = new Numeral(_.pick(options, ['currency', 'number']));
        let formatted = '';

        if (!_.isString(format)) {
            throw new Error('Please provide a format type!');
        }

        if (FORMATS.indexOf(format) === -1) {
            throw new Error(`Unknown format "${format}"!`);
        }

        if (value === '') {
            return formatted;
        }

        switch (format) {
            case 'currency':
            case 'money':
            case 'unit-price':
            case 'total':
                formatted = numeral.formatCurrency(value);
                break;
            case 'amount':
            case 'decimal':
            case 'percentage':
            case 'unit':
            case 'number':
                formatted = numeral.format(value);
                break;
            case 'integer':
                formatted = parseInt(value) + '';
                break;
            case 'formatted-integer':
                formatted = numeral.format(value, 0) + '';
                break;
            case 'shorten-number':
                const result = shortenNumber(Math.abs(value));

                if (result.prefix) {
                    formatted =
                        app.format(result.number, 'decimal', {
                            number: {precision: 1}
                        }) + result.prefix;
                } else {
                    formatted =
                        Number(result.number) === result.number && result.number % 1 !== 0
                            ? app.format(result.number, 'decimal', {
                                  number: {precision: 1}
                              })
                            : result.number;
                }
                break;
            case 'date':
                formatted = app.datetime.fromJSDate(value).toFormat(options.date);
                break;
            case 'datetime':
                formatted = app.datetime.fromJSDate(value).toFormat(options.datetime);
                break;
            case 'time':
                formatted = app.datetime.fromJSDate(value).toFormat(options.time);
                break;
            case 'file-size':
                const round = (n, p) => Number(Big(n).round(p));

                if (_.isNumber(value)) {
                    if (value < 1024) {
                        formatted = `${round(value, 1)} B`;
                    } else if (value >= Math.pow(1024, 1) && value < Math.pow(1024, 2)) {
                        formatted = `${round(value / 1024, 1)} KB`;
                    } else if (value >= Math.pow(1024, 2) && value < Math.pow(1024, 3)) {
                        formatted = `${round(value / Math.pow(1024, 2), 1)} MB`;
                    } else if (value >= Math.pow(1024, 3) && value < Math.pow(1024, 4)) {
                        formatted = `${round(value / Math.pow(1024, 3), 1)} GB`;
                    } else {
                        formatted = '';
                    }
                } else {
                    formatted = '';
                }
                break;
        }

        return formatted;
    };

    app.unFormat = (value, format = 'number') => {
        const options = app.get('formatterOptions');
        let precision = 2;
        if (format === 'currency') {
            precision = app.setting('system.currencyPrecision');
        } else if (format === 'amount' || format === 'decimal') {
            precision = app.setting('system.amountPrecision');
        } else if (format === 'percentage') {
            precision = app.setting('system.percentagePrecision');
        } else if (format === 'unit-price') {
            precision = app.setting('system.unitPricePrecision');
        } else if (format === 'unit') {
            precision = app.setting('system.unitPrecision');
        } else if (format === 'total') {
            precision = app.setting('system.totalPrecision');
        }
        options.currency.precision = precision;
        options.number.precision = precision;

        if (format === 'number' || format === 'decimal' || format === 'float' || format === 'integer') {
            const numeral = new Numeral(_.pick(options, ['currency', 'number']));

            let unFormatted = numeral.unFormat(value);

            if (format === 'integer') {
                unFormatted = parseInt(unFormatted);
            }

            return unFormatted;
        } else if (format === 'date') {
            return app.datetime.fromFormat(value, options.date).toJSDate();
        } else if (format === 'datetime') {
            return app.datetime.fromFormat(value, options.datetime).toJSDate();
        } else if (format === 'time') {
            return app.datetime.fromFormat(value, options.time).toJSDate();
        } else {
            throw new Error(`Unknown format "${format}"!`);
        }
    };
}

const SI_PREFIXES = ['', 'k', 'M', 'G', 'T', 'P', 'E'];

function shortenNumber(number) {
    // What tier? (determines SI prefix)
    let tier = (Math.log10(number) / 3) | 0;

    // If zero, we don't need a prefix
    if (tier === 0) return {number: Number(Big(number).round(1))};

    // Get prefix and determine scale
    let prefix = SI_PREFIXES[tier],
        scale = Math.pow(10, tier * 3);

    // Scale the number
    let scaled = number / scale;

    // Format number and add prefix as suffix
    return {number: Number(Big(scaled).round(1)), prefix};
}
