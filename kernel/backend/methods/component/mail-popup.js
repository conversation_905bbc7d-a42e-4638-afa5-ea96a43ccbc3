import _ from 'lodash';
import axios from 'axios';
import MailComposer from 'nodemailer/lib/mail-composer';
import {toLower, toUpper, firstUpper, escapeRegExp} from 'framework/helpers';

function createMongoSearchQuery(searchString, searchFields) {
    const searches = [];

    searchFields.forEach(field => {
        searches.push({
            $or: [
                {
                    [field]: {
                        $regex: toUpper(escapeRegExp(searchString)),
                        $options: 'i'
                    }
                },
                {
                    [field]: {
                        $regex: toLower(escapeRegExp(searchString)),
                        $options: 'i'
                    }
                },
                {
                    [field]: {
                        $regex: firstUpper(escapeRegExp(searchString)),
                        $options: 'i'
                    }
                }
            ]
        });
    });

    return searches;
}

export default [
    {
        name: 'component.mail-popup.initialize',
        async action(payload, params) {
            const app = this.app;
            const {
                formatOptions,
                documentId,
                locale,
                receiver,
                subject,
                subjectTranslationPayload = {},
                message,
                messageTranslationPayload = {},
                printMethod,
                printPayload
            } = payload;
            const t = (k, p = {}) => app.translate(k, locale, p);
            const model = {};

            model.receiver = !!receiver ? [receiver] : [];
            model.subject = t(subject, subjectTranslationPayload);
            model.message = t(message, messageTranslationPayload);

            if (!!printMethod) {
                const {url, title} = await app.rpc(printMethod, {
                    id: documentId,
                    formatOptions,
                    ...(printPayload || {})
                });
                const response = await axios.get(url, {
                    responseType: 'arraybuffer'
                });
                model.attachment = response.data.toString('base64');
                model.attachmentTitle = title;
            }

            return model;
        }
    },
    {
        name: 'component.mail-popup.get-mails',
        async action({query, existing}, params) {
            const app = this.app;
            const mails = [];

            const result = await app.collection('kernel.partners').find({
                $and: [{email: {$exists: true}}, {email: {$ne: null}}, {email: {$ne: ''}}, {email: {$ne: existing}}],
                ...(!!query ? {$or: createMongoSearchQuery(query, ['email'])} : {}),
                $select: ['email'],
                $limit: 10
            });
            const resultExt = await app.collection('kernel.contacts').find({
                $and: [{email: {$exists: true}}, {email: {$ne: null}}, {email: {$ne: ''}}, {email: {$ne: existing}}],
                ...(!!query ? {$or: createMongoSearchQuery(query, ['email'])} : {}),
                $select: ['email'],
                $limit: 10
            });

            mails.push(...result.map(item => item.email));
            mails.push(...resultExt.map(item => item.email));

            return mails;
        }
    },
    {
        name: 'component.mail-popup.send',
        async action(payload, params) {
            const app = this.app;
            const company = await this.app.collection('kernel.company').findOne({});
            const {receiver, cc, subject, message, attachment, attachmentTitle, customAttachments} = payload;
            const from = `${company.name} <${company.email}>`;

            const attachments = [];

            if (!!attachment) {
                attachments.push({
                    filename: `${attachmentTitle}.pdf`,
                    content: Buffer.from(attachment, 'base64')
                });
            }

            if (Array.isArray(customAttachments) && customAttachments.length > 0) {
                for (const customAttachment of customAttachments) {
                    attachments.push({
                        filename: customAttachment.filename,
                        content: Buffer.from(customAttachment.content, 'base64')
                    });
                }
            }

            await app.mail({
                from,
                to: receiver.join(', '),
                ...(Array.isArray(cc) && cc.length > 0 ? {cc: cc.join(', ')} : {}),
                subject,
                html: message,
                ...(attachments.length > 0 ? {attachments} : {})
            });
        }
    },
    {
        name: 'component.mail-popup.download-eml',
        async action(payload, params) {
            const app = this.app;
            const user = params.user;
            const company = await this.app.collection('kernel.company').findOne({});
            const {receiver, cc, subject, message, attachment, attachmentTitle, customAttachments} = payload;
            let from = `${company.name} <${company.email}>`;

            if (!!user) {
                from = `${user.name} <${user.email}>`;
            }

            const attachments = [];

            if (!!attachment) {
                attachments.push({
                    filename: `${attachmentTitle}.pdf`,
                    content: Buffer.from(attachment, 'base64')
                });
            }

            if (Array.isArray(customAttachments) && customAttachments.length > 0) {
                for (const customAttachment of customAttachments) {
                    attachments.push({
                        filename: customAttachment.filename,
                        content: Buffer.from(customAttachment.content, 'base64')
                    });
                }
            }

            const mail = new MailComposer({
                from,
                to: receiver.join(', '),
                ...(Array.isArray(cc) && cc.length > 0 ? {cc: cc.join(', ')} : {}),
                subject,
                html: message,
                headers: {
                    'X-Unsent': '1',
                    'X-Uniform-Type-Identifier': 'com.apple.mail-draft',
                    'X-Mozilla-Draft-Info': 'internal/draft; vcard=0; receipt=0; DSN=0; uuencode=0'
                },
                ...(attachments.length > 0 ? {attachments} : {})
            });

            return new Promise((resolve, reject) => {
                mail.compile().build((err, message) => {
                    if (!!err) return reject(err);

                    resolve(Buffer.from(message).toString('base64'));
                });
            });
        }
    }
];
