import path from 'path';
import _ from 'lodash';
import * as Excel from 'exceljs';
import {firstUpper} from 'framework/helpers';

const sessions = {};

export default {
    name: 'component.export-table-data',
    async action(payload, params) {
        const app = this.app;
        const {id, name, columns, items, isLastBock, summary} = payload;

        let extension = 'xlsx';
        if (payload.format === 'csv') extension = 'csv';
        const file = `${id}.${extension}`;
        const filePath = path.join(app.config('paths.temp'), file);
        let session = sessions[id];
        let workbook = null;
        let worksheet = null;

        if (!session) {
            // Prepare workbook.
            workbook = new Excel.stream.xlsx.WorkbookWriter({
                filename: filePath,
                useStyles: true
            });
            workbook.creator = 'EnterERP';
            workbook.lastModifiedBy = 'EnterERP';
            workbook.created = app.datetime.local().toJSDate();
            workbook.modified = app.datetime.local().toJSDate();
            workbook.lastPrinted = app.datetime.local().toJSDate();

            // Prepare worksheet.
            worksheet = workbook.addWorksheet(name, {
                views: [{state: 'frozen', xSplit: 0, ySplit: 1}]
            });

            // Prepare columns.
            const sheetColumns = [];
            for (const c of columns) {
                const column = {};

                column.header = c.label
                    .split(' ')
                    .map(str => firstUpper(str))
                    .join(' ');
                column.key = c.field;

                if (_.isNumber(c.width)) {
                    column.width = c.width;
                }

                if (!!column.disableFormatting) {
                    column.style = {numFmt: '@'};
                }

                if (column.isCurrencyColumn) {
                    column.style = {numFmt: '#,##0.00'};
                }

                sheetColumns.push(column);
            }
            worksheet.columns = sheetColumns;

            // Set header styles.
            const headerRow = worksheet.getRow(1);
            headerRow.font = {
                color: {argb: 'FFFFFFFF'},
                bold: true
            };
            headerRow.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: {argb: 'FF2196F3'}
            };

            sessions[id] = {
                date: app.datetime.local().toJSDate(),
                workbook,
                worksheet
            };
            session = sessions[id];
        } else {
            workbook = session.workbook;
            worksheet = session.worksheet;

            workbook.lastModifiedBy = 'EnterERP';
            workbook.modified = app.datetime.local().toJSDate();
            workbook.lastPrinted = app.datetime.local().toJSDate();
        }

        // Commit rows.
        for (const row of items) {
            for (const key of Object.keys(row)) {
                if (_.isNumber(row[key]) && !_.isInteger(row[key])) {
                    row[key] = app.roundNumber(row[key], 2);
                }
            }

            await worksheet.addRow(fixDates(row)).commit();
        }

        // Check if session end.
        if (!!isLastBock) {
            // Summary.
            if (!!summary) {
                for (const key of Object.keys(summary)) {
                    if (_.isNumber(summary[key]) && !_.isInteger(summary[key])) {
                        summary[key] = app.roundNumber(summary[key], 2);
                    }
                }

                const summaryRow = worksheet.addRow(fixDates(summary));
                summaryRow.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: {argb: 'FFE9E9ED'}
                };

                await summaryRow.commit();
            }

            // Finished the workbook.
            await workbook.commit();

            // Finished adding data. Commit the worksheet
            await worksheet.commit();

            delete sessions[id];
        }

        return {file, name: `${name}.${extension}`};
    }
};

function fixDates(row) {
    for (const key of Object.keys(row)) {
        if (_.isDate(row[key])) {
            const date = row[key];

            row[key] = new Date(
                Date.UTC(
                    date.getFullYear(),
                    date.getMonth(),
                    date.getDate(),
                    date.getHours(),
                    date.getMinutes(),
                    date.getSeconds()
                )
            );
        }
    }

    return row;
}
