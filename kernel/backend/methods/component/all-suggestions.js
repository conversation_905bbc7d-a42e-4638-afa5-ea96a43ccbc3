export default {
    name: 'component.all-suggestions',
    async action(suggestionParamsMap, params) {
        const result = {};

        for (const key of Object.keys(suggestionParamsMap)) {
            const suggestionParam = suggestionParamsMap[key];

            try {
                result[key] = await this.app.rpc('kernel.component.suggestions', suggestionParam, params);
            } catch {
                result[key] = [];
            }
        }

        return result;
    }
};
