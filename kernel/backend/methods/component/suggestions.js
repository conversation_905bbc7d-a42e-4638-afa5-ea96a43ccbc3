import _ from 'lodash';
import {getMongoSearchQuery, to<PERSON>ower} from 'framework/helpers';

function createMongoSearchQuery(searchString, searchFields) {
    const searches = [];

    searchFields.forEach(field => {
        searches.push(getMongoSearchQuery(field, searchString));
    });

    return searches;
}

export default {
    name: 'component.suggestions',
    // schema: {
    //     collection: {
    //         type: 'string',
    //         required: true
    //     },
    //     valueFrom: {
    //         type: 'string',
    //         default: '_id'
    //     },
    //     labelFrom: {
    //         type: 'string',
    //         default: 'name'
    //     },
    //     value: {
    //         type: 'string',
    //         required: false
    //     },
    //     limit: {
    //         type: 'integer',
    //         default: 10,
    //         max: 100,
    //         min: 1
    //     },
    //     search: {
    //         type: 'string',
    //         required: false
    //     },
    //     extraFields: {
    //         type: [{
    //             type: 'string',
    //             required: false
    //         }],
    //         default: []
    //     },
    //     filters: {
    //         type: 'object',
    //         blackbox: true,
    //         default: {}
    //     },
    //     isPreview: {
    //         type: 'boolean',
    //         default: false
    //     }
    // },
    async action(data, params) {
        const app = this.app;
        const user = params.user;
        let query = {};
        let result = [];
        let skipSearch = false;

        // Get collection definition.
        const collectionDefinitions = app.get('collectionDefinitions') || {};
        const collectionDefinition = collectionDefinitions[data.collection];

        // Set defaults.
        data = {
            valueFrom: '_id',
            labelFrom: 'name',
            limit: 10,
            extraFields: [],
            filters: {},
            ...data
        };

        // Prepare select
        query.$select = [];
        if (Array.isArray(data.filters.$select)) {
            query.$select = query.$select.concat(data.filters.$select);
        }
        if (data.extraFields.length > 0) {
            query.$select = query.$select.concat(data.extraFields);
        }
        if (data.valueFrom !== '_id') {
            query.$select.push(data.valueFrom);
        }
        query.$select.push(data.labelFrom);
        query.$select = _.uniq(query.$select).filter(s => s !== 'id');

        // Prepare sort.
        if (!!data.filters.$sort && typeof data.filters.$sort === 'object') {
            query.$sort = data.filters.$sort;
        } else {
            query.$sort = {[data.labelFrom]: 1};
        }

        // Extra filters.
        if (!!data.filters && typeof data.filters === 'object') {
            query = _.merge(query, data.filters);
        }

        // Hide admin user.
        if (data.collection === 'kernel.users' && !!user && user.email !== '<EMAIL>') {
            if (!Array.isArray(query.$and)) {
                query.$and = [];
            }

            query.$and.push({email: {$ne: '<EMAIL>'}});
        }
        if (data.collection === 'kernel.partners' && !!user && user.email !== '<EMAIL>') {
            if (!Array.isArray(query.$and)) {
                query.$and = [];
            }

            query.$and.push({email: {$ne: '<EMAIL>'}});
        }

        // Prepare limit
        query.$limit = data.limit;

        // When value supplied!
        if (data.value) {
            if (Array.isArray(data.value)) {
                const valueQuery = {
                    [data.valueFrom]: {$in: data.value},
                    $select: query.$select,
                    $disableSoftDelete: true
                };

                if (query.$populate) {
                    valueQuery.$populate = query.$populate;
                }

                let valueResults = await this.app.collection(data.collection).find(valueQuery, {
                    user: params.user,
                    disableInUseCheck: true,
                    disableActiveCheck: true,
                    disablePermissionCheck: true,
                    disableBranchCheck: true
                });

                for (const valueResult of valueResults) {
                    if (data.labelFrom.includes('.')) {
                        skipSearch = _.get(valueResult, data.labelFrom) === data.search;
                    } else {
                        skipSearch = valueResult[data.labelFrom] === data.search;
                    }

                    result.push(valueResult);
                }

                if (!!data.getOnlyValue) return result;
            } else {
                const valueQuery = {
                    [data.valueFrom]: data.value,
                    $select: query.$select,
                    $disableSoftDelete: true
                };

                if (query.$populate) {
                    valueQuery.$populate = query.$populate;
                }

                let valueResult = await this.app.collection(data.collection).findOne(valueQuery, {
                    user: params.user,
                    disableInUseCheck: true,
                    disableActiveCheck: true,
                    disablePermissionCheck: true,
                    disableBranchCheck: true
                });

                if (!!valueResult && typeof valueResult === 'object') {
                    if (data.labelFrom.includes('.')) {
                        skipSearch = _.get(valueResult, data.labelFrom) === data.search;
                    } else {
                        skipSearch = valueResult[data.labelFrom] === data.search;
                    }

                    result.push(valueResult);

                    if (!!data.getOnlyValue) return result;
                }
            }
        }

        // Prepare search.
        if (!skipSearch && typeof data.search === 'string' && data.search.length > 0) {
            if (!!collectionDefinition && collectionDefinition.searchTerms) {
                const searchMethod = app.setting('system.searchMethod');

                if (!Array.isArray(query.$and)) query.$and = [];

                if (searchMethod === 'full-text') {
                    query.$and.push({
                        $text: {
                            $search: `${toLower(data.search)
                                .replaceAll('/', ' ')
                                .replaceAll('-', ' ')
                                .replaceAll(' - ', ' ')
                                .replaceAll(' -', ' ')
                                .replaceAll('- ', ' ')
                                .replaceAll('_', ' ')
                                .replaceAll('.', ' ')}`
                        }
                    });

                    if (data.search.indexOf(' ') !== -1) {
                        const parts = data.search.split(' ');

                        for (const searchText of parts) {
                            query.$and.push(getMongoSearchQuery('searchText', toLower(searchText)));
                        }
                    } else {
                        query.$and.push(getMongoSearchQuery('searchText', data.search));
                    }

                    query.$select.push({confidenceScore: {$meta: 'textScore'}});
                    query.$sort = {confidenceScore: {$meta: 'textScore'}};
                } else {
                    if (data.search.indexOf(' ') !== -1) {
                        const parts = data.search.split(' ');

                        for (const searchText of parts) {
                            query.$and.push(getMongoSearchQuery('searchText', toLower(searchText)));
                        }
                    } else {
                        query.$and.push(getMongoSearchQuery('searchText', data.search));
                    }
                }
            } else {
                const searches = createMongoSearchQuery(data.search, query.$select);

                if (searches.length > 1) {
                    query.$or = searches;
                } else if (searches.length > 0) {
                    query = {...query, ...searches[0]};
                }
            }
        }

        if (!data.isPreview) {
            result = result.concat(await this.app.collection(data.collection).find(query, params));

            if (!!collectionDefinition && collectionDefinition.searchTerms) {
                return _.uniqBy(result, data.valueFrom);
            }
        }

        return _.sortBy(_.uniqBy(result, data.valueFrom), Object.keys(query.$sort)[0]);
    }
};
