export default [
    {
        name: 'component.printer-popup.initialize',
        async action(payload, params) {
            const app = this.app;
            const locale = params.user.language.isoCode;
            const {source, allowedTemplateIds} = payload;
            const data = {};

            const templates = app.get('templates');
            const template = templates[source];

            data.title = app.translate(template.title, locale);
            data.templateId = 'default';

            const defaultTemplates = app.setting('system.defaultTemplates') || [];
            const defaultTemplate = defaultTemplates.find(dt => dt.source === source);
            if (!!defaultTemplate && !!defaultTemplate.templateId) {
                const dt = await app.collection('kernel.templates').findOne({
                    _id: defaultTemplate.templateId,
                    $select: ['_id', 'title']
                });

                data.title = dt.title;
                data.templateId = dt._id;
            }

            data.templateOptions = [{value: 'default', label: app.translate('Default', locale)}];
            const query = {source, $select: ['_id', 'title']};
            if (Array.isArray(allowedTemplateIds)) {
                query._id = {
                    $in:
                        !!data.templateId && data.templateId !== 'default'
                            ? allowedTemplateIds.concat([data.templateId])
                            : allowedTemplateIds
                };
            }
            const savedTemplates = await app.collection('kernel.templates').find(query);
            for (const savedTemplate of savedTemplates) {
                data.templateOptions.push({
                    value: savedTemplate._id,
                    label: savedTemplate.title
                });
            }

            return data;
        }
    }
];
