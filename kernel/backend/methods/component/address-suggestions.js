import {simpleHash} from 'framework/helpers';

export default {
    name: 'component.address-suggestions',
    async action(payload, params) {
        const app = this.app;
        const {query, divisionCode} = payload;
        const cacheKey = `kcas-${simpleHash(JSON.stringify(payload))}`;
        const cached = await app.cache.get(cacheKey);

        if (Array.isArray(cached)) {
            return cached;
        }

        const result = (
            await app
                .collection('kernel.locations')
                .aggregate([
                    {$match: query},
                    {$group: {_id: `$${divisionCode}`}},
                    {$project: {name: '$_id', _id: 0}},
                    {$sort: {name: 1}},
                    {$limit: 7}
                ])
        ).map(location => location.name);

        await app.cache.set(cacheKey, result);

        return result;
    }
};
