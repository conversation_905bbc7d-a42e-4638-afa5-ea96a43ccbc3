import {getMongoSearchQuery, toLower} from 'framework/helpers';
import _ from 'lodash';

export default {
    name: 'component.shell-search',
    async action(search, params) {
        const app = this.app;
        const user = params.user;
        const query = {$and: []};

        query.$and.push({
            $text: {
                $search: `${toLower(search)
                    .replaceAll('/', ' ')
                    .replaceAll('-', ' ')
                    .replaceAll(' - ', ' ')
                    .replaceAll(' -', ' ')
                    .replaceAll('- ', ' ')
                    .replaceAll('_', ' ')
                    .replaceAll('.', ' ')}`
            }
        });
        if (search.indexOf(' ') !== -1) {
            const parts = search.split(' ');

            for (const searchText of parts) {
                query.$and.push(getMongoSearchQuery('text', toLower(searchText)));
            }
        } else {
            query.$and.push(getMongoSearchQuery('text', search));
        }

        query.$and.push({
            $or: [{branchIds: {$exists: false}}, {branchIds: {$eq: null}}, {branchIds: {$in: user.branchIds || []}}]
        });

        const records = await app.collection('kernel.search-indexes').find({
            ...query,
            $select: [
                'view',
                'collection',
                'collectionName',
                'documentId',
                'documentIdentifier',
                'responsibleId',
                {confidenceScore: {$meta: 'textScore'}}
            ],
            $sort: {confidenceScore: {$meta: 'textScore'}},
            $limit: 250
        });
        const result = [];

        for (const item of records) {
            const title = item.documentIdentifier;
            const path = item.collectionName;
            const id = item.documentId;
            const type = app.translate('Record');

            let view = '';
            if (item.view.endsWith('-detail')) {
                view = item.view;
            } else {
                view = `${item.view}-detail`;
            }

            if (!user.isRoot) {
                const collectionName = item.collection;
                const mappedRecordPermissions = app.get('mappedRecordPermissions') || {};
                let permission = null;

                if (
                    _.isPlainObject(mappedRecordPermissions[user._id]) &&
                    !_.isEmpty(mappedRecordPermissions[user._id])
                ) {
                    permission = mappedRecordPermissions[user._id][collectionName];

                    if (!_.isPlainObject(permission)) {
                        permission = (user.recordPermissions || []).find(p => p.name === collectionName);

                        mappedRecordPermissions[user._id][collectionName] = permission;

                        app.set('mappedRecordPermissions', mappedRecordPermissions);
                    }
                } else {
                    permission = (user.recordPermissions || []).find(p => p.name === collectionName);

                    mappedRecordPermissions[user._id] = {};

                    mappedRecordPermissions[user._id][collectionName] = permission;

                    app.set('mappedRecordPermissions', mappedRecordPermissions);
                }

                if (
                    !permission ||
                    permission.read === 'no' ||
                    (permission.read === 'owned' && !(item.responsibleId === user._id || item.createdBy === user._id))
                ) {
                    continue;
                }
            }

            result.push({
                title,
                path,
                view,
                id,
                type
            });
        }

        return result;
    }
};
