import _ from 'lodash';
import {toLower} from 'framework/helpers';

export default [
    {
        name: 'component.get-attachments',
        async action(payload, params) {
            const app = this.app;
            const ids = payload.ids || [];

            if (!Array.isArray(ids) || ids.length < 1) {
                throw new app.errors.NotAcceptable();
            }

            const orderingMap = {};
            let i = 0;
            for (const id of ids) {
                orderingMap[id] = ++i;
            }

            return _.sortBy(
                (await app.collection('kernel.files').find({_id: {$in: ids}})).map(file => {
                    file.order = orderingMap[file._id];

                    return file;
                }),
                'order'
            );
        }
    },
    {
        name: 'component.get-attachment-tags',
        async action(payload, params) {
            const app = this.app;
            let tags = await app.cache.get('attachment-tags');

            if (!Array.isArray(tags)) {
                tags = await app.collection('kernel.files').distinct('tags');

                const processed = [];
                for (const tag of tags) {
                    if (processed.findIndex(p => toLower(p.label).trim() === toLower(tag.label).trim()) === -1) {
                        processed.push(tag);
                    }
                }
                tags = processed;

                if (Array.isArray(tags)) {
                    app.cache.set('attachment-tags', tags);
                }
            }

            return tags || [];
        }
    },
    {
        name: 'component.update-attachment',
        async action(payload, params) {
            const app = this.app;
            const id = payload.id || '';
            const name = payload.name;
            const categoryId = payload.categoryId;
            const tags = payload.tags;
            const description = payload.description;

            if (!id) {
                throw new app.errors.NotAcceptable();
            }

            await app.collection('kernel.files').patch(
                {_id: id},
                {
                    name,
                    categoryId,
                    tags,
                    description
                },
                {user: params.user}
            );
        }
    },
    {
        name: 'component.delete-attachment',
        async action(payload, params) {
            const app = this.app;
            const id = payload.id || '';

            if (!id) {
                throw new app.errors.NotAcceptable();
            }

            await app.collection('kernel.files').remove({_id: id}, {user: params.user});
        }
    }
];
