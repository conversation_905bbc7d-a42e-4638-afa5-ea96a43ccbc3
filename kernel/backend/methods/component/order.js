export default {
    name: 'component.order',
    // schema: {
    //     collection: {
    //         type: 'string',
    //         required: true
    //     },
    //     payload: {
    //         type: [{
    //             _id: 'string',
    //             order: 'number'
    //         }],
    //         default: []
    //     }
    // },
    async action(data, params) {
        const operations = [];

        for (const item of data.payload) {
            operations.push({
                updateOne: {
                    filter: {_id: item._id},
                    update: {
                        $set: {
                            order: item.order
                        }
                    }
                }
            });
        }

        if (operations.length > 0) {
            await this.app.collection(data.collection).bulkWrite(operations);
        }

        return true;
    }
};
