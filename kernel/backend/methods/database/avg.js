import _ from 'lodash';

export default {
    name: 'database.avg',
    // schema: {
    //     servicePath: {
    //         type: 'string',
    //         required: true
    //     },
    //     field: {
    //         type: 'string',
    //         required: true
    //     },
    //     query: {
    //         type: 'object',
    //         blackbox: true,
    //         default: {}
    //     }
    // },
    async action({servicePath, field, query}) {
        query.deleted = {$ne: true};

        let pipeline = [];

        pipeline.push({$match: query});
        pipeline.push({$group: {_id: null, avg: {$avg: `$${field}`}}});

        let result = await this.app.db
            .collection(servicePath.split('/').join('_'))
            .aggregate(pipeline, {
                collation: {locale: this.app.config('app.locale')},
                allowDiskUse: true
            })
            .toArray();
        result = result[0];

        return _.isObject(result) && _.isNumber(result.avg) ? result.avg : null;
    }
};
