import _ from 'lodash';
import {hash} from 'framework/helpers';

export default {
    name: 'database.aggregate',
    // schema: {
    //     servicePath: {
    //         type: 'string',
    //         required: true
    //     },
    //     pipeline: {
    //         type: [{
    //             type: 'object',
    //             blackbox: true
    //         }],
    //         min: 1,
    //         required: true
    //     },
    //     cache: {
    //         type: 'boolean',
    //         default: false
    //     }
    // },
    async action({servicePath, pipeline, cache}) {
        const app = this.app;
        // const cacheKey = `aggregation-caches-${servicePath.split('/').join('.')}-${JSON.stringify(pipeline)}}`;
        let result = null;

        // try {
        //     result = await app.cache.get(cacheKey);
        // } catch (error) {
        //     result = null;
        // }
        //
        // if (result !== null) {
        //     return result;
        // }

        /*
        if (cache) {
            const cacheKey = `${servicePath}-aggregation-result`;
            const hash = JSON.stringify(pipeline);

            // if ((await this.app.cache.has(cacheKey))) {
            //     const cachedResults = (await this.app.cache.get(cacheKey)) || [];
            //     const cachedResultObject = cachedResults.find(r => r.hash === hash);
            //
            //     if (_.isObject(cachedResultObject)) {
            //         return cachedResultObject.result;
            //     } else {
            //         const result = await getResult(this.app, servicePath, pipeline);
            //
            //         cachedResults.push({
            //             hash,
            //             result
            //         });
            //
            //         this.app.cache.set(cacheKey, cachedResults);
            //
            //         return result;
            //     }
            // } else {
            //     const result = await getResult(this.app, servicePath, pipeline);
            //     const cachedResults = [];
            //
            //     cachedResults.push({
            //         hash,
            //         result
            //     });
            //
            //     this.app.cache.set(cacheKey, cachedResults);
            //
            //     return result;
            // }

            const result = await getResult(this.app, servicePath, pipeline);
            const cachedResults = [];

            cachedResults.push({
                hash,
                result
            });

            this.app.cache.set(cacheKey, cachedResults);

            return result;
        }*/

        result = await getResult(app, servicePath, pipeline);

        // try {
        //     await app.cache.set(cacheKey, result, 60 * 60 * 24 * 7);
        // } catch (error) {}

        return result;
    }
};

async function getResult(app, servicePath, pipeline) {
    const matchIndex = _.findIndex(pipeline, p => _.isObject(p.$match));

    if (matchIndex !== -1) {
        const matchStage = pipeline[matchIndex];

        matchStage.$match.deleted = {$ne: true};
    } else {
        pipeline.unshift({
            $match: {deleted: {$ne: true}}
        });
    }

    return app.db
        .collection(servicePath.split('/').join('_'))
        .aggregate(pipeline, {
            allowDiskUse: true,
            collation: {locale: app.config('app.locale')}
        })
        .toArray();
}
