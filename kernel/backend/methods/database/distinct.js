export default {
    name: 'database.distinct',
    // schema: {
    //     servicePath: {
    //         type: 'string',
    //         required: true
    //     },
    //     field: {
    //         type: 'string',
    //         required: true
    //     },
    //     query: {
    //         type: 'object',
    //         blackbox: true,
    //         default: {}
    //     }
    // },
    async action({servicePath, field, query}) {
        query.deleted = {$ne: true};

        return await this.app.db.collection(servicePath.split('/').join('_')).distinct(field, query, {
            collation: {locale: this.app.config('app.locale')}
        });
    }
};
