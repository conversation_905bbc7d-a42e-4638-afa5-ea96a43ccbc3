import _ from 'lodash';
import {ObjectId} from 'mongodb';

export default {
    name: 'database.bulk-write',
    // schema: {
    //     servicePath: {
    //         type: 'string',
    //         required: true
    //     },
    //     operations: {
    //         type: [{
    //             type: 'object',
    //             blackbox: true,
    //             required: false
    //         }]
    //     },
    //     options: {
    //         type: 'object',
    //         blackbox: true,
    //         default: {}
    //     }
    // },
    async action({servicePath, operations, options}, params) {
        const app = this.app;

        options = _.defaults(options, {
            ordered: false
        });

        operations = operations.map(operation => {
            ['updateOne', 'updateMany', 'replaceOne', 'deleteOne', 'deleteMany'].forEach(operator => {
                if (_.isPlainObject(operation[operator]) && _.isPlainObject(operation[operator].filter)) {
                    if (operation[operator].filter._id) {
                        if (_.isString(operation[operator].filter._id)) {
                            operation[operator].filter._id = objectifyId(operation[operator].filter._id);
                        } else if (_.isPlainObject(operation[operator].filter._id)) {
                            operation[operator].filter._id = objectifyIdsInQuery(operation[operator].filter._id);
                        }
                    }

                    ['$and', '$or'].forEach(opt => {
                        if (Array.isArray(operation[operator].filter[opt])) {
                            operation[operator].filter[opt] = operation[operator].filter[opt].map(subQuery => {
                                if (_.isString(subQuery._id)) {
                                    subQuery._id = objectifyId(subQuery._id);
                                } else if (_.isPlainObject(subQuery._id)) {
                                    subQuery._id = objectifyIdsInQuery(subQuery._id);
                                }

                                return subQuery;
                            });
                        }
                    });

                    operation[operator].collation = {
                        locale: app.config('app.locale')
                    };
                }
            });

            return operation;
        });

        if (operations.length > 0) {
            const service = app.service(servicePath);

            if (!service.noCache) {
                try {
                    app.memoryCache.clear(`aggregation-caches-${servicePath.split('/').join('.')}`);
                } catch (e) {
                    console.log(e);
                }

                if (!!service.cache) {
                    try {
                        app.memoryCache.clear(`records-caches-${servicePath.split('/').join('.')}`);
                    } catch (e) {
                        console.log(e);
                    }
                }
            }

            const result = await app.db
                .collection(servicePath.split('/').join('_'))
                .bulkWrite(operations, _.omit(options, 'skipEvents'));

            if (!options.skipEvents && !app.get('disablePublications')) {
                service.emit('created', {
                    collectionName: servicePath.split('/').join('.'),
                    isBulkWrite: true
                });
            }

            // Delete cache.
            // let serviceCacheKeys = [];
            // try {
            //     serviceCacheKeys = await app.cache.get('serviceCacheKeys|' + servicePath)
            // } catch {
            //     serviceCacheKeys = [];
            // }
            // if (!Array.isArray(serviceCacheKeys)) {
            //     serviceCacheKeys = [];
            // }
            // if (serviceCacheKeys.length > 0) {
            //     await app.cache.delete(serviceCacheKeys);
            //     await app.cache.set('serviceCacheKeys|' + servicePath, []);
            // }

            return result;
        }
    }
};

function objectifyId(id) {
    if (ObjectId.isValid(id)) {
        id = new ObjectId(id.toString());
    }

    return id;
}

function objectifyIdsInQuery(idObject) {
    const result = {};

    _.each(idObject, (id, operator) => {
        result[operator] = Array.isArray(id)
            ? id.filter(i => ObjectId.isValid(i)).map(i => new ObjectId(i.toString()))
            : new ObjectId(id.toString());
    });

    return result;
}
