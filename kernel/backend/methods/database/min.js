import _ from 'lodash';

export default {
    name: 'database.min',
    // schema: {
    //     servicePath: {
    //         type: 'string',
    //         required: true
    //     },
    //     field: {
    //         type: 'string',
    //         required: true
    //     },
    //     query: {
    //         type: 'object',
    //         blackbox: true,
    //         default: {}
    //     }
    // },
    async action({servicePath, field, query}) {
        query.deleted = {$ne: true};

        let pipeline = [];

        pipeline.push({$match: query});
        pipeline.push({$group: {_id: null, min: {$min: `$${field}`}}});

        let result = await this.app.db
            .collection(servicePath.split('/').join('_'))
            .aggregate(pipeline, {
                allowDiskUse: true,
                collation: {locale: this.app.config('app.locale')}
            })
            .toArray();
        result = result[0];

        return _.isObject(result) && _.isNumber(result.min) ? result.min : null;
    }
};
