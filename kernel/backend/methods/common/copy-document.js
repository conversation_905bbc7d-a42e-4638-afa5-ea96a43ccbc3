import _ from 'lodash';

export default {
    name: 'common.copy-document',
    async action({documentCollection, documentId}, params) {
        const app = this.app;
        const user = params.user;

        if (!!user) {
            await app.checkPermission({
                user,
                collection: documentCollection,
                method: 'create',
                id: documentId
            });
        }

        const collectionDefinitions = app.get('collectionDefinitions') || {};
        const collectionDefinition = collectionDefinitions[documentCollection];

        if (!collectionDefinition || !_.isFunction(collectionDefinition.copy)) {
            throw new Error('Copy function is not found!');
        }

        const document = await app.collection(documentCollection).findOne({
            _id: documentId,
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });
        if (!document) {
            throw new Error('Document is not found!');
        }

        const newDocument = await collectionDefinition.copy.call({app, translate: this.translate}, document);
        const savedDocument = await app.collection(documentCollection).create(
            {
                ..._.omit(newDocument, [
                    '_id',
                    'isCopy',
                    'isRecurrence',
                    'createdAt',
                    'updatedAt',
                    'createdBy',
                    'updatedBy'
                ]),
                isCopy: true
            },
            {
                ...(!!user ? {user} : {})
            }
        );

        if (_.isFunction(collectionDefinition.afterCopy)) {
            await collectionDefinition.afterCopy.call({app, translate: this.translate}, document, savedDocument);
        }

        return savedDocument;
    }
};
