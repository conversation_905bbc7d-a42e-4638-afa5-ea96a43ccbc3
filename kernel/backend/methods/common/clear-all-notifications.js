import _ from 'lodash';

export default {
    name: 'common.clear-all-notifications',
    async action(payload, params) {
        const app = this.app;

        await app.collection('kernel.notifications').bulkWrite([
            {
                deleteMany: {
                    filter: {
                        userId: params.user._id
                    }
                }
            }
        ]);
    }
};
