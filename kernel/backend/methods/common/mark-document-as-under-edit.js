export default {
    name: 'common.mark-document-as-under-edit',
    async action(payload, params) {
        const app = this.app;
        const {document, documentId} = payload;
        const existing = await app.collection('kernel.documents-under-edit').findOne({
            document,
            documentId
        });

        if (!!existing) {
            return {
                status: 'error',
                payload: existing
            };
        }

        const item = {};

        item.date = app.datetime.local().toJSDate();
        item.document = document;
        item.documentId = documentId;
        item.userId = params.user._id;
        item.userCode = params.user.code;
        item.userName = params.user.name;

        const {insertedId} = await app.db.collection('kernel_documents-under-edit').insertOne(item);
        const result = await app.collection('kernel.documents-under-edit').get(insertedId.toString());

        return {
            status: 'success',
            payload: result
        };
    }
};
