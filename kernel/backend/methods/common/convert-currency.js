import _ from 'lodash';

export default {
    name: 'common.convert-currency',
    // schema: {
    //     from: 'string',
    //     to: 'string',
    //     value: 'decimal',
    //     options: {
    //         type: 'object',
    //         required: false,
    //         blackbox: true
    //     }
    // },
    async action(payload, params) {
        const app = this.app;
        const company = await app.collection('kernel.company').findOne();
        const companyCurrency = company.currency.name;
        const options = payload.options || {};
        const date = _.isDate(options.date) ? app.datetime.fromJSDate(options.date) : app.datetime.local();
        let currencyPrecision = app.setting('system.exchangeRatePrecision');
        const round = app.roundNumber;
        const startDate = date.startOf('day').toJSDate();
        const endDate = date.endOf('day').toJSDate();
        let rates = null;

        if (_.isFinite(options.currencyPrecision)) {
            currencyPrecision = options.currencyPrecision;
        }

        if (!options.fromRateType) {
            options.fromRateType = app.setting('system.defaultExchangeRateType');
        }
        if (!options.toRateType) {
            options.toRateType = app.setting('system.defaultExchangeRateType');
        }

        const cacheKey = `daily-exchange-rates-${options.fromRateType}-${
            options.toRateType
        }-${startDate.getTime()}${endDate.getTime()}-${currencyPrecision}`;
        const cachedRates = await app.cache.get(cacheKey);

        if (Array.isArray(cachedRates) && cachedRates.length > 0) {
            rates = cachedRates;
        } else {
            rates = await app.collection('kernel.exchange-rates').find({
                date: {
                    $gte: date.startOf('day').toJSDate(),
                    $lte: date.endOf('day').toJSDate()
                },
                $sort: {date: -1}
            });

            await app.cache.set(cacheKey, rates);
        }

        if (!Array.isArray(rates) || rates.length < 1) {
            throw new app.errors.NotAcceptable(
                app.translate('No registered exchange rate was found for {{date}}!', {
                    date: date.toFormat('dd.LL.yyyy')
                }),
                {
                    reason: 'noExchangeRateFound',
                    date: date.toJSDate()
                }
            );
        }

        const from = payload.from;
        const to = payload.to;
        const value = payload.value;
        const roundResult = payload.round !== false;
        const fromRate = rates.find(r => r.currencyName === from);
        const toRate = rates.find(r => r.currencyName === to);
        let fromRateValue = null;
        let toRateValue = null;

        if (from === companyCurrency) fromRateValue = 1;
        else if (_.isObject(fromRate)) fromRateValue = fromRate.rate;
        else {
            throw new app.errors.NotAcceptable(
                app.translate('No {{currency}} exchange rate was found for {{date}}!', {
                    date: date.toFormat('dd.LL.yyyy'),
                    currency: from
                }),
                {
                    reason: 'noFromExchangeRateFound',
                    date: date.toJSDate(),
                    from
                }
            );
        }
        if (to === companyCurrency) toRateValue = 1;
        else if (_.isObject(toRate)) toRateValue = toRate.rate;
        else {
            throw new app.errors.NotAcceptable(
                app.translate('No {{currency}} exchange rate was found for {{date}}!', {
                    date: date.toFormat('dd.LL.yyyy'),
                    currency: to
                }),
                {
                    reason: 'noToExchangeRateFound',
                    date: date.toJSDate(),
                    to
                }
            );
        }

        if (from !== companyCurrency && _.isString(options.fromRateType)) {
            if (!_.isNumber(fromRate[options.fromRateType])) {
                throw new app.errors.NotAcceptable(
                    app.translate('No {{currency}} exchange rate was found for {{date}}!', {
                        date: date.toFormat('dd.LL.yyyy'),
                        currency: from
                    }),
                    {
                        reason: 'noFromExchangeRateFound',
                        date: date.toJSDate(),
                        from
                    }
                );
            }

            fromRateValue = fromRate[options.fromRateType];
        }
        if (to !== companyCurrency && _.isString(options.toRateType)) {
            if (!_.isNumber(toRate[options.toRateType])) {
                throw new app.errors.NotAcceptable(
                    app.translate('No {{currency}} exchange rate was found for {{date}}!', {
                        date: date.toFormat('dd.LL.yyyy'),
                        currency: to
                    }),
                    {
                        reason: 'noToExchangeRateFound',
                        date: date.toJSDate(),
                        to
                    }
                );
            }

            toRateValue = toRate[options.toRateType];
        }

        if (!_.isNumber(fromRateValue)) {
            throw new app.errors.NotAcceptable(
                app.translate('No {{currency}} exchange rate was found for {{date}}!', {
                    date: date.toFormat('dd.LL.yyyy'),
                    currency: from
                }),
                {
                    reason: 'noFromExchangeRateFound',
                    date: date.toJSDate(),
                    from
                }
            );
        }
        if (!_.isNumber(toRateValue)) {
            throw new app.errors.NotAcceptable(
                app.translate('No {{currency}} exchange rate was found for {{date}}!', {
                    date: date.toFormat('dd.LL.yyyy'),
                    currency: to
                }),
                {
                    reason: 'noToExchangeRateFound',
                    date: date.toJSDate(),
                    to
                }
            );
        }

        if (roundResult) {
            return round(value * (fromRateValue / toRateValue), currencyPrecision);
        }

        return value * (fromRateValue / toRateValue);
    }
};
