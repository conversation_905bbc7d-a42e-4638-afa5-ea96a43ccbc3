import _ from 'lodash';

const cachedCollections = {};

export default {
    name: 'common.get-cached-collection-records',
    async action(payload, params) {
        if (_.isEmpty(cachedCollections)) {
            const publicParams = this.app.get('publicParams');
            const programs = publicParams.programs;

            for (const program of programs) {
                for (const collection of program.collections) {
                    if (collection.localCache) {
                        // Get populations.
                        const populate = [];
                        if (Array.isArray(collection.attributes)) {
                            for (const attribute of collection.attributes) {
                                if (!attribute.auto) {
                                    populate.push(attribute.field);
                                }
                            }
                        }

                        cachedCollections[collection.name] = {
                            name: collection.name,
                            populate
                        };
                    }
                }
            }
        }

        if (_.isObject(payload) && payload.collectionName) {
            const collection = cachedCollections[payload.collectionName];

            if (payload._id) {
                return await this.app.collection(collection.name).findOne(
                    {
                        _id: payload._id,
                        $populate: collection.populate
                    },
                    {
                        user: params.user,
                        disableActiveCheck: true,
                        disableBranchCheck: true
                        // disableSoftDelete: true
                    }
                );
            }

            return await this.app.collection(collection.name).find(
                {
                    $populate: collection.populate
                },
                {
                    user: params.user,
                    disableActiveCheck: true,
                    disableBranchCheck: true
                    // disableSoftDelete: true
                }
            );
        } else {
            const data = {};

            for (const collectionName of Object.keys(cachedCollections)) {
                const collection = cachedCollections[collectionName];

                data[collection.name] = await this.app.collection(collection.name).find(
                    {
                        $populate: collection.populate
                    },
                    {
                        user: params.user,
                        disableActiveCheck: true,
                        disableBranchCheck: true
                        // disableSoftDelete: true
                    }
                );
            }

            return data;
        }
    }
};
