export default {
    name: 'common.get-document-meta',
    async action(payload, params) {
        const app = this.app;
        const data = await app.collection(payload.collection).findOne({
            _id: payload.id,
            $select: ['createdAt', 'createdBy', 'assignedTo'],
            $disableSoftDelete: true,
            $disableActiveCheck: true
        });
        const meta = {};
        let responsible = null;

        if (!!data.assignedTo) {
            responsible = await app.collection('kernel.users').findOne({
                _id: data.assignedTo,
                $select: ['_id', 'code', 'name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
        } else {
            responsible = await app.collection('kernel.users').findOne({
                _id: data.createdBy,
                $select: ['_id', 'code', 'name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
        }

        meta.createdAt = data.createdAt;
        meta.responsible = responsible;

        return meta;
    }
};
