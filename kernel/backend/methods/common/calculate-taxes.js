import _ from 'lodash';

export default {
    name: 'common.calculate-taxes',
    // schema: {
    //     items: [{
    //         taxId: 'string',
    //         amount: 'decimal',
    //         taxPayload: {
    //             type: 'object',
    //             blackbox: true,
    //             required: false
    //         }
    //     }]
    // },
    async action(payload, params) {
        const app = this.app;
        const currencyPrecision = 6;
        const round = app.roundNumber;
        let currencyRate = params.currencyRate;
        if (!_.isFinite(currencyRate)) {
            currencyRate = payload.currencyRate;

            if (!_.isFinite(currencyRate)) {
                currencyRate = 1;
            }
        }
        const result = [];

        // Get all taxes and sub-taxes.
        let taxIds = [];
        for (const item of payload.items) {
            let hasTaxPayload =
                _.isPlainObject(item.taxPayload) &&
                !_.isEmpty(item.taxPayload) &&
                Array.isArray(item.taxPayload.taxIds) &&
                item.taxPayload.taxIds.length > 0;

            if (hasTaxPayload) {
                taxIds.push(...item.taxPayload.taxIds);
            } else {
                taxIds.push(item.taxId);
            }
        }
        taxIds = _.uniq(taxIds);
        const taxes = await app.collection('kernel.taxes').find({_id: {$in: taxIds}});
        const subTaxIds = [];
        for (const tax of taxes) {
            if (Array.isArray(tax.subTaxIds) && tax.subTaxIds.length > 0) {
                const ids = tax.subTaxIds.filter(
                    id => !_.isObject(taxes.find(t => t._id === id)) && subTaxIds.indexOf(id) === -1
                );

                subTaxIds.push(...ids);
            }
        }
        if (subTaxIds.length > 0) {
            const subTaxes = await app.collection('kernel.taxes').find({_id: {$in: subTaxIds}});

            taxes.push(...subTaxes);
        }

        for (const item of payload.items) {
            let hasTaxPayload =
                _.isPlainObject(item.taxPayload) &&
                !_.isEmpty(item.taxPayload) &&
                Array.isArray(item.taxPayload.taxIds) &&
                item.taxPayload.taxIds.length > 0;
            const tax = hasTaxPayload
                ? {computation: 'group', subTaxIds: item.taxPayload.taxIds}
                : taxes.find(tax => tax._id === item.taxId);

            if (tax.computation === 'group' && tax.subTaxIds.length > 0) {
                let lastTotal = item.amount;
                let lastCalculation = 0;

                for (const subTaxId of tax.subTaxIds) {
                    const subTax = taxes.find(tax => tax._id === subTaxId);
                    let amountToUse = !!subTax.doesntAffectedByPreviousTaxes ? item.amount : lastTotal;
                    let price = null;

                    if (!!subTax.applyToPrevious) {
                        amountToUse = lastCalculation;
                    }

                    if (subTax.affectBaseOfSubsequentTaxes) {
                        price = calculate(
                            subTax.computation,
                            amountToUse,
                            subTax.isDeduction || subTax.isStoppage ? -subTax.amount : subTax.amount,
                            currencyRate,
                            subTax.isIncludedInPrice
                        );

                        price = round(price, currencyPrecision);

                        if (subTax.computation === 'fixed' && !!subTax.useQuantity) {
                            const qty = item.quantity || 1;

                            price = round(price * qty, currencyPrecision);
                        }

                        lastTotal += price;
                        lastCalculation = price;
                    } else {
                        price = calculate(
                            subTax.computation,
                            amountToUse,
                            subTax.isDeduction || subTax.isStoppage ? -subTax.amount : subTax.amount,
                            currencyRate,
                            subTax.isIncludedInPrice
                        );

                        price = round(price, currencyPrecision);

                        if (subTax.computation === 'fixed' && !!subTax.useQuantity) {
                            const qty = item.quantity || 1;

                            price = round(price * qty, currencyPrecision);
                        }

                        lastCalculation = price;
                    }

                    const resultIndex = _.findIndex(result, r => r.taxId === subTax._id);
                    if (resultIndex === -1) {
                        result.push({
                            taxId: subTax._id,
                            unAppliedAmount: amountToUse,
                            amount: price
                        });
                    } else {
                        result[resultIndex].unAppliedAmount += amountToUse;
                        result[resultIndex].amount += price;
                    }
                }
            } else {
                let price = calculate(
                    tax.computation,
                    item.amount,
                    tax.isDeduction || tax.isStoppage ? -tax.amount : tax.amount,
                    currencyRate,
                    tax.isIncludedInPrice
                );

                price = round(price, currencyPrecision);

                if (tax.computation === 'fixed' && !!tax.useQuantity) {
                    const qty = item.quantity || 1;

                    price = round(price * qty, currencyPrecision);
                }

                const resultIndex = _.findIndex(result, r => r.taxId === tax._id);
                if (resultIndex === -1) {
                    result.push({
                        taxId: tax._id,
                        unAppliedAmount: item.amount,
                        amount: price
                    });
                } else {
                    result[resultIndex].unAppliedAmount += item.amount;
                    result[resultIndex].amount += price;
                }
            }
        }

        return result;
    }
};

function calculate(type, amount, rate, currencyRate, includedInPrice = false) {
    let price = null;

    if (includedInPrice) {
        if (type === 'percent') {
            price = amount - amount / (1 + rate / 100);
        } else if (type === 'fixed') {
            price = rate / currencyRate;
        }
    } else {
        if (type === 'percent') {
            price = (amount * rate) / 100;
        } else if (type === 'fixed') {
            price = rate / currencyRate;
        }
    }

    return price;
}
