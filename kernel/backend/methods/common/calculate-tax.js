import _ from 'lodash';

export default {
    name: 'common.calculate-tax',
    // schema: {
    //     taxId: 'string',
    //     amount: 'decimal',
    //     taxPayload: {
    //         type: 'object',
    //         blackbox: true,
    //         required: false
    //     }
    // },
    async action(payload, params) {
        const app = this.app;
        const currencyPrecision = 6;
        const round = app.roundNumber;
        let currencyRate = params.currencyRate;
        if (!_.isFinite(currencyRate)) {
            currencyRate = payload.currencyRate;

            if (!_.isFinite(currencyRate)) {
                currencyRate = 1;
            }
        }
        const applied = [];
        let taxes = [];
        let tax = null;

        // Get all taxes and sub-taxes.
        if (
            _.isPlainObject(payload.taxPayload) &&
            !_.isEmpty(payload.taxPayload) &&
            Array.isArray(payload.taxPayload.taxIds) &&
            payload.taxPayload.taxIds.length > 0
        ) {
            taxes = await app.collection('kernel.taxes').find({_id: {$in: payload.taxPayload.taxIds}});

            tax = {
                computation: 'group',
                subTaxIds: payload.taxPayload.taxIds
            };
        } else {
            taxes = await app.collection('kernel.taxes').find({_id: payload.taxId});

            const subTaxIds = [];
            for (const tax of taxes) {
                if (Array.isArray(tax.subTaxIds) && tax.subTaxIds.length > 0) {
                    const ids = tax.subTaxIds.filter(
                        id => !_.isObject(taxes.find(t => t._id === id)) && subTaxIds.indexOf(id) === -1
                    );

                    subTaxIds.push(...ids);
                }
            }

            if (subTaxIds.length > 0) {
                const subTaxes = await app.collection('kernel.taxes').find({_id: {$in: subTaxIds}});

                taxes.push(...subTaxes);
            }

            tax = taxes.find(tax => tax._id === payload.taxId);
        }

        let unTaxedTotal = payload.amount;
        if (tax.computation === 'group' && tax.subTaxIds.length > 0) {
            let lastTotal = payload.amount;
            let lastCalculation = 0;

            for (const subTaxId of tax.subTaxIds) {
                const subTax = taxes.find(tax => tax._id === subTaxId);
                let amountToUse = !!subTax.doesntAffectedByPreviousTaxes ? payload.amount : lastTotal;
                let price = null;

                if (!!subTax.applyToPrevious) {
                    amountToUse = lastCalculation;
                }

                if (subTax.affectBaseOfSubsequentTaxes) {
                    price = calculate(
                        subTax.computation,
                        amountToUse,
                        subTax.isDeduction || subTax.isStoppage ? -subTax.amount : subTax.amount,
                        currencyRate,
                        subTax.isIncludedInPrice
                    );

                    price = round(price, currencyPrecision);

                    if (subTax.computation === 'fixed' && !!subTax.useQuantity) {
                        const qty = payload.quantity || 1;

                        price = round(price * qty, currencyPrecision);
                    }

                    lastTotal += price;
                    lastCalculation = price;
                } else {
                    price = calculate(
                        subTax.computation,
                        amountToUse,
                        subTax.isDeduction || subTax.isStoppage ? -subTax.amount : subTax.amount,
                        currencyRate,
                        subTax.isIncludedInPrice
                    );

                    price = round(price, currencyPrecision);

                    if (subTax.computation === 'fixed' && !!subTax.useQuantity) {
                        const qty = payload.quantity || 1;

                        price = round(price * qty, currencyPrecision);
                    }

                    lastCalculation = price;
                }

                if (subTax.isIncludedInPrice) {
                    unTaxedTotal -= price;
                }

                if (!!subTax.isDeduction) {
                    const mainTax = taxes.find(t => !t.isDeduction && !t.isStoppage);

                    if (!!mainTax) {
                        subTax.mainTaxId = mainTax._id;
                    }
                }

                const appliedIndex = _.findIndex(applied, r => r._id === subTax._id);
                if (appliedIndex === -1) {
                    applied.push({
                        ...subTax,
                        unAppliedAmount: amountToUse,
                        appliedAmount: price
                    });
                } else {
                    applied[appliedIndex].unAppliedAmount += amountToUse;
                    applied[appliedIndex].appliedAmount += price;
                }
            }
        } else {
            let price = calculate(
                tax.computation,
                payload.amount,
                tax.isDeduction || tax.isStoppage ? -tax.amount : tax.amount,
                currencyRate,
                tax.isIncludedInPrice
            );

            price = round(price, currencyPrecision);

            if (tax.computation === 'fixed' && !!tax.useQuantity) {
                const qty = payload.quantity || 1;

                price = round(price * qty, currencyPrecision);
            }

            if (tax.isIncludedInPrice) {
                unTaxedTotal -= price;
            }

            if (!!tax.isDeduction) {
                const mainTax = taxes.find(t => !t.isDeduction && !t.isStoppage);

                if (!!mainTax) {
                    tax.mainTaxId = mainTax._id;
                }
            }

            const appliedIndex = _.findIndex(applied, r => r._id === tax._id);
            if (appliedIndex === -1) {
                applied.push({
                    ...tax,
                    unAppliedAmount: payload.amount,
                    appliedAmount: price
                });
            } else {
                applied[appliedIndex].unAppliedAmount += payload.amount;
                applied[appliedIndex].appliedAmount += price;
            }
        }

        return {
            unTaxedTotal: round(unTaxedTotal, currencyPrecision),
            taxTotal: _.sumBy(applied, 'appliedAmount'),
            applied
        };
    }
};

function calculate(type, amount, rate, currencyRate, includedInPrice = false) {
    let price = null;

    if (includedInPrice) {
        if (type === 'percent') {
            price = amount - amount / (1 + rate / 100);
        } else if (type === 'fixed') {
            price = rate / currencyRate;
        }
    } else {
        if (type === 'percent') {
            price = (amount * rate) / 100;
        } else if (type === 'fixed') {
            price = rate / currencyRate;
        }
    }

    return price;
}
