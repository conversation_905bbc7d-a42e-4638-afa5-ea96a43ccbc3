import _ from 'lodash';

export default {
    name: 'common.get-allowed-widgets',
    // schema: {
    //     section: {
    //         type: 'string',
    //         allowed: ['panel', 'desktop'],
    //         required: true
    //     },
    //     program: {
    //         type: 'string',
    //         required: false
    //     },
    //     userId: {
    //         type: 'string',
    //         required: true
    //     }
    // },
    async action({section, program, userId}, params) {
        if (section === 'panel' && !program) {
            throw new app.errors.Unprocessable('Program must be provided for panel widgets!');
        }

        const app = this.app;
        const user =
            _.isObject(params.user) && params.user._id === userId
                ? params.user
                : await app.collection('kernel.users').findOne({_id: userId});

        return (app.get('programWidgets') || []).filter(widget => {
            let result = !Array.isArray(widget.applicable) || widget.applicable.indexOf(section) !== -1;

            if (result && section === 'panel') {
                result = widget.program === program;
            }

            if (result && !user.isRoot && Array.isArray(widget.permissions) && widget.permissions.length > 0) {
                for (const wp of widget.permissions) {
                    if (!result) break;

                    const parts = wp.split('|');
                    const collection = parts[0];
                    const method = parts[1];
                    const permission = (user.recordPermissions || []).find(p => p.name === collection);

                    result = permission[method] !== 'no';
                }
            }

            return result;
        });
    }
};
