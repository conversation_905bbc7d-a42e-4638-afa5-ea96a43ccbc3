import _ from 'lodash';
import {ObjectId} from 'mongodb';

export default {
    name: 'common.assign-document',
    async action(payload, params) {
        const app = this.app;
        const user = params.user;
        const t = (message = '', p = {}) => {
            return app.translate(message, user.language.isoCode, p);
        };
        const assignedBy = user;
        const documentCollection = app.collection(payload.collection);
        const document = await documentCollection.findOne({
            _id: payload.documentId
        });
        const partner = await app.collection('kernel.partners').findOne({
            _id: payload.employeeId,
            $select: ['code', 'name', 'userAccountId'],
            $disableSoftDelete: true,
            $disableActiveCheck: true
        });

        // Check assigned by,
        if (!_.isObject(assignedBy)) {
            throw new app.errors.Unprocessable('Assigned by is required!');
        }

        // Prepare collection name.
        const publicParams = app.get('publicParams');
        const program = publicParams.programs.find(p => p.name === payload.collection.split('.')[0]);
        let programTitle = '';
        let collectionName = '';
        if (_.isObject(program) && program.title) {
            programTitle = program.title;
        } else {
            programTitle = 'System';
        }
        if (_.isObject(program)) {
            const definition = program.collections.find(c => c.name === payload.collection);

            if (definition.title) {
                collectionName = `${t(programTitle)} / ${t(definition.title)}`;
            }
        }

        // Prepare document identifier.
        let documentIdentifier = '';
        if (_.isObject(document)) {
            if (document.code && document.name) {
                documentIdentifier = `${document.code} - ${document.name}`;
            } else if (document.code) {
                documentIdentifier = document.code;
            } else if (document.name) {
                documentIdentifier = document.name;
            } else if (document.title) {
                documentIdentifier = document.title;
            } else if (document.label) {
                documentIdentifier = document.label;
            }
        }

        if (!!partner.userAccountId) {
            const assignedTo = await app.collection('kernel.users').findOne({
                _id: partner.userAccountId,
                $select: ['_id', 'code', 'name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            const partnerData = {};

            // Check assigned to,
            if (!_.isObject(assignedTo)) {
                throw new app.errors.Unprocessable('Assigned to is required!');
            }

            // Prepare update data.
            const data = {};
            data.assignedTo = assignedTo._id;
            data.assignedBy = assignedBy._id;
            if (!!payload.organizationScope && !!payload.organizationId) {
                const organization = await app.collection('kernel.organizations').findOne({
                    _id: payload.organizationId,
                    $select: ['team']
                });

                if (_.isObject(organization)) {
                    const team = organization.team || [];
                    const currentMember = team.find(m => m.partnerId === payload.employeeId);

                    data.organizationId = organization._id;

                    if (_.isObject(currentMember)) {
                        if (currentMember.managerId) {
                            data[payload.managerField] = currentMember.managerId;
                        } else {
                            data[payload.managerField] = currentMember.partnerId;
                        }
                    }

                    if (_.isObject(currentMember)) {
                        data[payload.employeeField] = currentMember.partnerId;
                    }

                    if (!!document.partnerId && _.isObject(currentMember)) {
                        if (payload.employeeField === 'salespersonId') {
                            partnerData.salesOrganizationId = organization._id;
                            partnerData.salesManagerId = data[payload.managerField];
                            partnerData.salespersonId = data[payload.employeeField];
                        } else if (payload.employeeField === 'purchaseRepresentativeId') {
                            partnerData.purchaseOrganizationId = organization._id;
                            partnerData.purchaseManagerId = data[payload.managerField];
                            partnerData.purchaseRepresentativeId = data[payload.employeeField];
                        }
                    }
                }
            }

            /* if (!_.isEmpty(partnerData)) {
                partnerData.assignedTo = assignedTo._id;
                partnerData.assignedBy = assignedBy._id;

                await app.collection('kernel.partners').patch({_id: document.partnerId}, partnerData);
            } */

            // Update document.
            await app.db.collection(payload.collection.replace('.', '_')).updateOne(
                {_id: new ObjectId(payload.documentId)},
                {
                    $set: data
                },
                {
                    collation: {locale: app.config('app.locale')}
                }
            );
            app.service(payload.collection.replace('.', '/')).emit('patched', {
                collectionName: payload.collection,
                isBulkWrite: true
            });

            // Create Meri notification.
            const userName = assignedTo.name.split(' ')[0];
            const notification = {};
            notification.type = 'info';
            notification.isMeri = true;
            notification.userId = assignedTo._id;
            notification.title = t('Assignation');
            notification.message = `<p>${t('Hello, {{name}}', {
                name: userName
            })}</p>`;
            if (!!documentIdentifier) {
                notification.message += `
            <p>${t('A document has been assigned to you by {{assignedBy}}.', {
                assignedBy: `${assignedBy.code} - ${assignedBy.name}`
            })} ${t('Details of the document are given below.')}</p>
            <p>${t('Document')}: ${collectionName} / ${documentIdentifier}</p>
            `.trim();
            } else {
                notification.message += `<p>${t('A document has been assigned to you by {{assignedBy}}.', {
                    assignedBy: `${assignedBy.code} - ${assignedBy.name}`
                })}</p>`;
            }
            notification.message += `<p>Meri</p>`;
            notification.shortMessage = t('A document has been assigned to you by {{assignedBy}}.', {
                assignedBy: `${assignedBy.code} - ${assignedBy.name}`
            });
            notification.link = {
                path: payload.documentPath,
                params: {id: payload.documentId}
            };
            await app.collection('kernel.notifications').create(notification, {disableValidation: true});

            // Log assignation.
            const log = {};
            log.level = 'success';
            log.message = t('Document assigned to {{assignedTo}} successfully.', {
                assignedTo: `${assignedTo.code} - ${assignedTo.name}`
            });
            log.date = app.datetime.local().toJSDate();
            log.userId = user._id;
            log.user = {
                name: user.name,
                email: user.email
            };
            log.collection = payload.collection;
            log.documentId = payload.documentId;
            if (!!collectionName) {
                log.collectionName = collectionName;
            }
            if (!!documentIdentifier) {
                log.documentIdentifier = documentIdentifier;
            }
            app.collection('kernel.logs').create(log, {disableValidation: true});
        } else {
            const partnerData = {};

            // Prepare update data.
            const data = {};
            data.assignedBy = assignedBy._id;
            if (!!payload.organizationScope && !!payload.organizationId) {
                const organization = await app.collection('kernel.organizations').findOne({
                    _id: payload.organizationId,
                    $select: ['team']
                });

                if (_.isObject(organization)) {
                    const team = organization.team || [];
                    const currentMember = team.find(m => m.partnerId === payload.employeeId);

                    data.organizationId = organization._id;

                    if (_.isObject(currentMember)) {
                        if (currentMember.managerId) {
                            data[payload.managerField] = currentMember.managerId;
                        } else {
                            data[payload.managerField] = currentMember.partnerId;
                        }
                    }

                    if (_.isObject(currentMember)) {
                        data[payload.employeeField] = currentMember.partnerId;
                    }

                    if (!!document.partnerId && _.isObject(currentMember)) {
                        if (payload.employeeField === 'salespersonId') {
                            partnerData.salesOrganizationId = organization._id;
                            partnerData.salesManagerId = data[payload.managerField];
                            partnerData.salespersonId = data[payload.employeeField];
                        } else if (payload.employeeField === 'purchaseRepresentativeId') {
                            partnerData.purchaseOrganizationId = organization._id;
                            partnerData.purchaseManagerId = data[payload.managerField];
                            partnerData.purchaseRepresentativeId = data[payload.employeeField];
                        }
                    }
                }
            }

            // Update document.
            await app.db.collection(payload.collection.replace('.', '_')).updateOne(
                {_id: new ObjectId(payload.documentId)},
                {
                    $set: data
                },
                {
                    collation: {locale: app.config('app.locale')}
                }
            );
            app.service(payload.collection.replace('.', '/')).emit('patched', {
                collectionName: payload.collection,
                isBulkWrite: true
            });

            /* if (!_.isEmpty(partnerData)) {
                partnerData.assignedTo = assignedTo._id;
                partnerData.assignedBy = assignedBy._id;

                await app.collection('kernel.partners').patch({_id: document.partnerId}, partnerData);
            } */

            // Log assignation.
            const log = {};
            log.level = 'success';
            log.message = t('Document assigned to {{assignedTo}} successfully.', {
                assignedTo: `${partner.code} - ${partner.name}`
            });
            log.date = app.datetime.local().toJSDate();
            log.userId = user._id;
            log.user = {
                name: user.name,
                email: user.email
            };
            log.collection = payload.collection;
            log.documentId = payload.documentId;
            if (!!collectionName) {
                log.collectionName = collectionName;
            }
            if (!!documentIdentifier) {
                log.documentIdentifier = documentIdentifier;
            }
            app.collection('kernel.logs').create(log, {disableValidation: true});
        }
    }
};
