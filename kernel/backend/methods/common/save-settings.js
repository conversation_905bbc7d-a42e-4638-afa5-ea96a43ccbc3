import _ from 'lodash';

export default {
    name: 'common.save-settings',
    // schema: {
    //     program: {
    //         type: 'string',
    //         required: true
    //     },
    //     settings: {
    //         type: 'object',
    //         required: false,
    //         blackbox: true
    //     }
    // },
    async action(payload, params) {
        const app = this.app;
        let bulkOps = [];

        // Reset partner main account.
        if (!!payload.settings.defaultAccounts) {
            await app.cache.clear('partner-main-account-');
        }

        _.each(payload.settings, (value, key) => {
            let ops = {};

            key = key.indexOf('.') === -1 ? `${payload.program}.${key}` : key;

            let entry = this.app.settingsStore[key];

            if (_.isObject(entry)) {
                if (entry.persisted) {
                    ops.filter = {name: key};
                    ops.update = {$set: {value}};

                    bulkOps.push({updateOne: ops});
                } else {
                    entry.value = value;

                    ops.document = entry;

                    bulkOps.push({insertOne: ops});
                }
            } else {
                entry = {name: key, value};

                ops.document = entry;

                bulkOps.push({insertOne: ops});
            }
        });

        await app.collection('kernel.settings').bulkWrite(bulkOps, {ordered: false});
        // (await this.app.collection('kernel.settings').find()).forEach(s => {
        //     this.app.settingsStore[s.name] = {..._.omit(s, '_id'), persisted: true};
        // });
        await app.cache.set('mobile-settings-hash', null);

        const programDefinition = global.__programs.find(p => p.name === payload.program);
        const message = app.translate('{{programName}} settings saved successfully.', {
            programName: app.translate(programDefinition.title)
        });

        if (_.isObject(params.user)) {
            const user = params.user;

            app.log({
                level: 'success',
                message,
                user: {
                    name: user.name,
                    email: user.email
                }
            });
        } else {
            app.log({
                level: 'success',
                message,
                user: {
                    name: 'System'
                }
            });
        }
    }
};
