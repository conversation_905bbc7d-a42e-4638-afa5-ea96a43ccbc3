export default {
    name: 'common.convert-currencies',
    async action(payloads, params) {
        const app = this.app;
        const result = [];

        for (const payload of payloads) {
            try {
                const rate = await app.rpc('kernel.common.convert-currency', payload);

                result.push({
                    ...payload,
                    rate
                });
            } catch (error) {}
        }

        return result;
    }
};
