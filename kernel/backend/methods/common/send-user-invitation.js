import _ from 'lodash';
import Random from 'framework/random';

export default {
    name: 'common.send-user-invitation',
    async action(user, params) {
        if (!user.isVerified) {
            // Get token.
            const token = Random.id(32);
            const url = this.app.absoluteUrl(`/auth/reset-password/${token}`);

            // Save token.
            await this.app.collection('kernel.users').patch(
                {_id: user._id},
                {
                    passwordResetExpires: this.app.datetime.local().plus({hours: 2}).toJSDate(),
                    passwordResetToken: token
                }
            );

            // Send token.
            const company = await this.app.collection('kernel.company').findOne({});
            const from = `${params.user.name} <${params.user.email}>`;
            const template = `
                <mj-section>
                <mj-column>
                    <mj-text>${this.translate('Dear {{name}}', {
                        name: user.name
                    })},</mj-text>
                    <mj-text padding-bottom="32px">${this.translate(
                        'You have been invited by {{invitedBy}} of {{companyName}} to connect on EnterERP. You can create your new password using the link below.',
                        {
                            companyName: company.name,
                            invitedBy: params.user.name
                        }
                    )}</mj-text>
                    <mj-button href="${url}">${this.translate('Accept Invitation')}</mj-button>
                </mj-column>
                </mj-section>
            `;
            return this.app.mail({
                from,
                to: user.email,
                subject: this.translate('Invitation to EnterERP'),
                template
            });
        }
    }
};
