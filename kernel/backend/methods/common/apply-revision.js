import _ from 'lodash';

export default {
    name: 'common.apply-revision',
    async action(payload, params) {
        const app = this.app;
        const user = params.user;
        const documentCollection = app.collection(payload.collection);
        const revisionsCollection = app.collection('kernel.revisions');
        const revision = await revisionsCollection.get(payload.revisionId, {
            user: params.user
        });

        await documentCollection.patch(
            {_id: payload.documentId},
            {
                ..._.omit(revision.document, '_id'),
                revisionId: revision._id,
                revisionName: revision.name,
                hasRevisions: true
            },
            {user}
        );

        const log = {};
        log.level = 'success';
        log.message = app.translate('Revision applied successfully.');
        log.date = app.datetime.local().toJSDate();
        log.userId = user._id;
        log.user = {
            name: user.name,
            email: user.email
        };
        log.collection = payload.collection;
        log.documentId = payload.documentId;
        const publicParams = app.get('publicParams');
        const program = publicParams.programs.find(p => p.name === log.collection.split('.')[0]);
        let programTitle = '';
        if (_.isObject(program) && program.title) {
            programTitle = program.title;
        } else {
            programTitle = 'System';
        }
        if (_.isObject(program)) {
            const definition = program.collections.find(c => c.name === log.collection);

            if (definition.title) {
                log.collectionName = `${app.translate(programTitle)} / ${app.translate(definition.title)}`;
            }
        }
        if (_.isObject(revision.document)) {
            const data = revision.document;
            let documentIdentifier = '';

            if (data.code && data.name) {
                documentIdentifier = `${data.code} - ${data.name}`;
            } else if (data.code) {
                documentIdentifier = data.code;
            } else if (data.name) {
                documentIdentifier = data.name;
            } else if (data.title) {
                documentIdentifier = data.title;
            } else if (data.label) {
                documentIdentifier = data.label;
            }

            if (documentIdentifier) {
                log.documentIdentifier = documentIdentifier;
            }
        }
        app.collection('kernel.logs').create(log, {disableValidation: true});

        return _.omit(revision, 'document');
    }
};
