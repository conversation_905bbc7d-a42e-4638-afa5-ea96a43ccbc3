export default {
    name: 'common.mark-notifications-as-read',
    async action(payload, params) {
        const app = this.app;

        await app.collection('kernel.notifications').bulkWrite([
            {
                updateMany: {
                    filter: {
                        userId: params.user._id
                    },
                    update: {
                        $set: {
                            isRead: true
                        }
                    }
                }
            }
        ]);
    }
};
