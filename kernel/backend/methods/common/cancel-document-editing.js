export default {
    name: 'common.cancel-document-editing',
    async action(query, params) {
        const app = this.app;

        if (!!params.user && params.user._id) {
            query.userId = params.user._id;
        }

        await app.db.collection('kernel_documents-under-edit').deleteMany(query, {
            collation: {locale: app.config('app.locale')},
            ordered: false
        });
    }
};
