import _ from 'lodash';
import {ObjectId} from 'mongodb';
import {leadingZeros} from 'framework/helpers';

export default {
    name: 'common.request-number',
    async action({numberingId, save, date}) {
        const app = this.app;
        const numbering = await app.db.collection('kernel_numbering').findOne(
            {
                _id: new ObjectId(numberingId)
            },
            {
                collation: {locale: app.config('app.locale')}
            }
        );
        let nextNumber = numbering.nextNumber;

        // Refresh
        if (_.isString(numbering.refresh) && numbering.refresh.length > 0) {
            switch (numbering.refresh) {
                case 'daily':
                    const currentDay = app.datetime.local().day;
                    const numberingDay = app.datetime.fromJSDate(numbering.updatedAt).day;

                    if (currentDay > numberingDay) nextNumber = 1;
                    break;
                case 'monthly':
                    const currentMonth = app.datetime.local().month;
                    const numberingMonth = app.datetime.fromJSDate(numbering.updatedAt).month;

                    if (currentMonth > numberingMonth) nextNumber = 1;
                    break;
                case 'yearly':
                    const currentYear = app.datetime.local().year;
                    const numberingYear = app.datetime.fromJSDate(numbering.updatedAt).year;

                    if (currentYear > numberingYear) nextNumber = 1;
                    break;
            }
        }

        // Evaluate the numbering volumes.
        const numberingDate = _.isDate(date) ? app.datetime.fromJSDate(date) : null;
        let volumes = numbering.numberingVolumes ?? [];
        let volume = null;
        let isVolumeExists = false;
        if (!!numbering.useNumberingVolumes && !!numberingDate) {
            if (!Array.isArray(volumes)) {
                volumes = [];
            }

            switch (numbering.refresh) {
                case 'daily':
                    volume = volumes.find(
                        vol =>
                            vol.year === numberingDate.year &&
                            vol.month === numberingDate.month &&
                            vol.day === numberingDate.day
                    );

                    if (!volume) {
                        volume = {
                            year: numberingDate.year,
                            month: numberingDate.month,
                            day: numberingDate.day,
                            nextNumber: 2
                        };

                        nextNumber = 1;

                        volumes.push(volume);
                    } else {
                        nextNumber = volume.nextNumber;

                        volume.nextNumber++;

                        isVolumeExists = true;
                    }
                    break;
                case 'monthly':
                    volume = volumes.find(vol => vol.year === numberingDate.year && vol.month === numberingDate.month);

                    if (!volume) {
                        volume = {
                            year: numberingDate.year,
                            month: numberingDate.month,
                            nextNumber: 2
                        };

                        nextNumber = 1;

                        volumes.push(volume);
                    } else {
                        nextNumber = volume.nextNumber;

                        volume.nextNumber++;

                        isVolumeExists = true;
                    }
                    break;
                case 'yearly':
                    volume = volumes.find(vol => vol.year === numberingDate.year);

                    if (!volume) {
                        volume = {
                            year: numberingDate.year,
                            nextNumber: 2
                        };

                        nextNumber = 1;

                        volumes.push(volume);
                    } else {
                        nextNumber = volume.nextNumber;

                        volume.nextNumber++;

                        isVolumeExists = true;
                    }
                    break;
            }
        }

        // Update next number.
        if (save) {
            if (!!volume) {
                if (isVolumeExists) {
                    switch (numbering.refresh) {
                        case 'daily':
                            await app.db.collection('kernel_numbering').updateOne(
                                {
                                    _id: new ObjectId(numberingId),
                                    'numberingVolumes.year': volume.year,
                                    'numberingVolumes.month': volume.month,
                                    'numberingVolumes.day': volume.day
                                },
                                {
                                    $inc: {
                                        'numberingVolumes.$.nextNumber': 1
                                    }
                                },
                                {collation: {locale: app.config('app.locale')}}
                            );
                            break;
                        case 'monthly':
                            await app.db.collection('kernel_numbering').updateOne(
                                {
                                    _id: new ObjectId(numberingId),
                                    'numberingVolumes.year': volume.year,
                                    'numberingVolumes.month': volume.month
                                },
                                {
                                    $inc: {
                                        'numberingVolumes.$.nextNumber': 1
                                    }
                                },
                                {collation: {locale: app.config('app.locale')}}
                            );
                            break;
                        case 'yearly':
                            await app.db.collection('kernel_numbering').updateOne(
                                {
                                    _id: new ObjectId(numberingId),
                                    'numberingVolumes.year': volume.year
                                },
                                {
                                    $inc: {
                                        'numberingVolumes.$.nextNumber': 1
                                    }
                                },
                                {collation: {locale: app.config('app.locale')}}
                            );
                            break;
                    }
                } else {
                    await app.db.collection('kernel_numbering').updateOne(
                        {
                            _id: new ObjectId(numberingId)
                        },
                        {
                            $set: {numberingVolumes: volumes}
                        },
                        {collation: {locale: app.config('app.locale')}}
                    );
                }
            } else {
                if (_.isNumber(numbering.lastNumber) && numbering.lastNumber > 0 && numbering.lastNumber < nextNumber) {
                    throw new app.errors.Unprocessable(
                        app.translate(
                            'The assigned number is greater than the last number that is allowed by the numerator. Please update the relevant numerator.'
                        )
                    );
                }

                if (nextNumber === 1) {
                    await app.db.collection('kernel_numbering').updateOne(
                        {
                            _id: new ObjectId(numberingId)
                        },
                        {
                            $set: {
                                nextNumber: 2
                            }
                        },
                        {collation: {locale: app.config('app.locale')}}
                    );
                } else {
                    await app.db.collection('kernel_numbering').updateOne(
                        {
                            _id: new ObjectId(numberingId)
                        },
                        {
                            $inc: {
                                nextNumber: 1
                            }
                        },
                        {collation: {locale: app.config('app.locale')}}
                    );
                }
            }
        }

        // Prepare no.
        let parts = [];
        if (!!volume) {
            if (numbering.prefix) parts.push(numbering.prefix);

            if (numbering.includeDay) {
                parts.push(!!volume.day ? leadingZeros(volume.day, 2) : app.datetime.local().toFormat('dd'));
            }
            if (numbering.includeMonth) {
                parts.push(!!volume.month ? leadingZeros(volume.month, 2) : app.datetime.local().toFormat('LL'));
            }
            if (numbering.includeYear) {
                parts.push(!!volume.year ? volume.year : app.datetime.local().toFormat('yyyy'));
            }
        } else {
            if (numbering.prefix) parts.push(numbering.prefix);
            if (numbering.includeDay) parts.push(app.datetime.local().toFormat('dd'));
            if (numbering.includeMonth) parts.push(app.datetime.local().toFormat('LL'));
            if (numbering.includeYear) parts.push(app.datetime.local().toFormat('yyyy'));
        }

        if (_.isNumber(numbering.leadingZeros) && numbering.leadingZeros > 0) {
            parts.push(leadingZeros(nextNumber, numbering.leadingZeros));
        } else {
            parts.push(nextNumber);
        }

        if (numbering.suffix) parts.push(numbering.suffix);
        const no = parts.join(numbering.separator ? numbering.separator : '');

        return no;
    }
};
