// @TODO -> Delete File
// When deleting record, create a before remove hook that check if the record has file field for
// example image, file. If it has such a field, delete that file.

import _ from 'lodash';
import fastCopy from 'fast-copy';
import {ObjectId} from 'mongodb';

export default {
    name: 'common.remove-record',
    // schema: {
    //     collection: 'string',
    //     id: {
    //         type: 'string',
    //         required: false
    //     },
    //     query: {
    //         type: 'object',
    //         required: false,
    //         blackbox: true
    //     }
    // },
    async action(data, params) {
        if (!data.id && _.isUndefined(data.query)) {
            throw new this.app.errors.Unprocessable(
                'Insufficient parameters. Please provide id or query to remove record!'
            );
        }

        const collection = this.app.collection(data.collection);
        let query = data.id ? {_id: data.id} : data.query;

        if (query === 'all') {
            query = {};
        }

        // Fix
        if (_.isObject(query) && _.isObject(query._id) && Array.isArray(query._id.$in)) {
            query._id.$in = query._id.$in.map(id => {
                if (ObjectId.isValid(id)) {
                    id = new ObjectId(id.toString());
                }

                return id;
            });
        }

        const items = await collection.find(
            query,
            _.assign(fastCopy(params), {
                disableInUseCheck: true,
                disableActiveCheck: true
            })
        );
        if (Array.isArray(items) && items.some(item => item.system)) {
            throw new this.app.errors.Forbidden(
                this.app.translate('You cannot delete the records that are created by system!')
            );
        }

        return await collection.remove(query, params);
    }
};
