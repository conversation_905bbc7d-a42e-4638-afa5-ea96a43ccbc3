import _ from 'lodash';
import {ObjectId} from 'mongodb';
import {hooks as authHooks} from '@feathersjs/authentication-local';

const {hashPassword} = authHooks;

export default {
    name: 'reset-user-password',
    authenticate: false,
    schema: {
        password: {
            type: 'string',
            label: 'Password',
            min: 4
        },
        token: {
            type: 'string',
            label: 'Token',
            required: false
        },
        userId: {
            type: 'string',
            label: 'User',
            required: false
        }
    },
    async action(payload, params) {
        const hash = async password => {
            const result = await hashPassword('password')({
                app: this.app,
                data: {password},
                type: 'before'
            });

            return result.data.password;
        };

        if (payload.token) {
            const user = await this.app.collection('kernel.users').findOne({
                passwordResetToken: payload.token,
                passwordResetExpires: {$gte: this.app.datetime.local().toJSDate()},
                $select: ['_id', 'email']
            });

            if (_.isObject(user)) {
                await this.app.db.collection('kernel_users').updateOne(
                    {_id: new ObjectId(user._id)},
                    {
                        $set: {
                            password: await hash(payload.password),
                            isVerified: true
                        },
                        $unset: {passwordResetToken: '', passwordResetExpires: ''}
                    },
                    {
                        collation: {locale: this.app.config('app.locale')}
                    }
                );

                return {email: user.email, password: payload.password};
            } else {
                throw new this.app.errors.Unprocessable('Reset token is invalid!');
            }
        } else if (payload.userId && !!params.user) {
            await this.app.db.collection('kernel_users').updateOne(
                {_id: new ObjectId(payload.userId)},
                {
                    $set: {password: await hash(payload.password)},
                    $unset: {passwordResetToken: '', passwordResetExpires: ''}
                },
                {
                    collation: {locale: this.app.config('app.locale')}
                }
            );
        } else {
            throw new this.app.errors.Unprocessable('Unable to reset password!');
        }
    }
};
