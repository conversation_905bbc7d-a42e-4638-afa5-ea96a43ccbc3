import _ from 'lodash';
import Base64 from 'framework/base64';

export default {
    name: 'common.get-session-data',
    // schema: {
    //     userId: 'string'
    // },
    async action(payload, params) {
        const app = this.app;
        const userId = payload.userId;
        const user = await app.collection('kernel.users').findOne({
            _id: userId,
            $select: [
                'code',
                'name',
                'email',
                'languageId',
                'timezone',
                'groupIds',
                'branchIds',
                'inactiveSessionTimeout',
                'partnerId',
                'avatar',
                'isRoot',
                'isActive',
                'permissions',
                'recordPermissions',
                'menus'
            ]
        });
        const company = await app.collection('kernel.company').findOne({});
        const userData = {};

        if (!_.isPlainObject(user)) {
            throw new Error('User not found!');
        }

        // User
        userData.user = user;

        // Filter un-necessary permissions and menus.
        userData.user.permissions = (userData.user.permissions || []).filter(p => !!p.canDo);
        userData.user.recordPermissions = (userData.user.recordPermissions || []).filter(p => {
            return !(p.read === 'no' && p.create === 'no' && p.update === 'no' && p.delete === 'no');
        });
        userData.user.menus = (userData.user.menus || []).filter(m => !!m.canAccess);

        // Get user group name.
        if (user.groupIds.length > 0) {
            const groups = await app.collection('kernel.user-groups').find({
                _id: {$in: user.groupIds},
                $select: ['code', 'name'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });
            userData.user.group = groups[0];
        }

        // Company
        userData.company = company;

        // Find formatter options.
        const formatterOptions = {};
        formatterOptions.currency = {};
        formatterOptions.currency.symbol = company.currency.symbol;
        formatterOptions.currency.format = company.currency.symbolPosition === 'before' ? '%s %v' : '%v %s';
        formatterOptions.currency.symbolPosition = company.currency.symbolPosition;
        formatterOptions.currency.decimal = user.language.decimalSeparator;
        formatterOptions.currency.thousand = user.language.thousandsSeparator;
        formatterOptions.currency.precision = app.setting('system.currencyPrecision');
        formatterOptions.number = {};
        formatterOptions.number.decimal = user.language.decimalSeparator;
        formatterOptions.number.thousand = user.language.thousandsSeparator;
        formatterOptions.number.precision = app.setting('system.amountPrecision');
        formatterOptions.date = user.language.dateFormat;
        formatterOptions.datetime = `${user.language.dateFormat} ${user.language.timeFormat}`;
        formatterOptions.time = user.language.timeFormat;
        userData.formatterOptions = formatterOptions;

        // User registry
        userData.registry = (await app.collection('kernel.registry').find({userId})).map(entry => _.omit(entry, '_id'));

        // Settings.
        userData.settings = app.settingsStore;

        // Un-read notification count.
        userData.unReadNotificationCount = await app.collection('kernel.notifications').count({
            userId: user._id,
            isRead: false
        });

        // Licence modules and features.
        const license = app.get('license');
        if (!!license && !!license.modules && !!license.features) {
            userData.elm = Base64.encode(JSON.stringify(license));
        }

        return userData;
    }
};
