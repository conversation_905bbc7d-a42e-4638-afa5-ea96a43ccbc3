import path from 'path';
import _ from 'lodash';
import * as Excel from 'exceljs';
import Random from 'framework/random';
import {firstUpper} from 'framework/helpers';

export default {
    name: 'common.export',
    async action(payload, params) {
        const app = this.app;
        const t = app.translate;

        let extension = 'xlsx';
        if (payload.format === 'csv') extension = 'csv';
        const file = `${Random.id(16)}.${extension}`;
        const progressId = `kernel.exports-${params.user._id}`;

        // Start progress.
        app.progress({
            id: progressId,
            status: 'started'
        });

        // Prepare workbook.
        const workbook = new Excel.stream.xlsx.WorkbookWriter({
            filename: path.join(app.config('paths.temp'), file),
            useStyles: true
        });
        workbook.creator = 'EnterERP';
        workbook.lastModifiedBy = 'EnterERP';
        workbook.created = app.datetime.local().toJSDate();
        workbook.modified = app.datetime.local().toJSDate();
        workbook.lastPrinted = app.datetime.local().toJSDate();
        const worksheet = workbook.addWorksheet(t(payload.name), {
            views: [{state: 'frozen', xSplit: 0, ySplit: 1}]
        });

        // Prepare columns.
        const columns = [];
        for (const c of payload.columns) {
            const column = {};

            column.header = t(c.label)
                .split(' ')
                .map(str => firstUpper(str))
                .join(' ');
            column.key = c.field;

            if (_.isNumber(c.width)) {
                column.width = c.width;
            }

            columns.push(column);
        }
        worksheet.columns = columns;

        // Set header styles.
        const headerRow = worksheet.getRow(1);
        headerRow.font = {
            color: {argb: 'FFFFFFFF'},
            bold: true
        };
        headerRow.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: {argb: 'FF2196F3'}
        };

        // Prepare method payload.
        const methodPayload = {};
        methodPayload.fields = payload.columns.map(column => column.field);
        methodPayload.deepFields = methodPayload.fields.filter(field => field.indexOf('.') !== -1);
        methodPayload.filters = payload.scope === 'filtered' ? payload.filters || {} : {};
        if (!_.isPlainObject(payload.query)) {
            methodPayload.filters = {
                ...methodPayload.filters,
                ...payload.query
            };
        }

        // Get sorting.
        let sort = {_id: 1};
        // let sort = payload.sort;
        // if (_.isUndefined(sort)) {
        //     sort = {
        //         [payload.columns[0].field]: 'asc'
        //     };
        // }

        // Get rows.
        const limit = 990;
        let total = 0;
        let page = 0;
        let rowCount = 0;
        do {
            let result = null;

            if (!!payload.collection) {
                // Adjust page.
                methodPayload.filters.$skip = page * limit;
                methodPayload.filters.$limit = limit;

                // Adjust fields
                methodPayload.filters.$select = methodPayload.fields;

                // Set sorting.
                if (_.isPlainObject(sort)) {
                    methodPayload.filters.$sort = {
                        [Object.keys(sort)[0]]: sort[Object.keys(sort)[0]] === 'asc' ? 1 : -1
                    };
                }

                result = await app.collection(payload.collection).find(methodPayload.filters, {
                    paginate: {
                        default: methodPayload.filters.$limit
                    },
                    user: params.user
                });
            } else {
                // Adjust page.
                methodPayload.skip = page * limit;
                methodPayload.limit = limit;

                // Set sorting.
                if (_.isPlainObject(sort)) {
                    methodPayload.sort = {
                        [Object.keys(sort)[0]]: sort[Object.keys(sort)[0]] === 'asc' ? 1 : -1
                    };
                }

                result = await app.rpc(payload.method, methodPayload, {
                    user: params.user
                });
            }

            // Update total.
            total = result.total;

            // Commit rows.
            for (let row of result.data) {
                // Fix dates.
                row = fixDates(row);

                for (const field of methodPayload.deepFields) {
                    if (_.isUndefined(row[field])) {
                        row[field] = _.get(row, field);
                    }
                }

                await worksheet.addRow(row).commit();

                rowCount++;
            }

            // Send progress percentage.
            if (total !== 0) {
                const percentage = (rowCount / total) * 100;
                app.progress({
                    id: progressId,
                    status: 'info',
                    percentage
                });
            }

            page++;
        } while (total > 0 && rowCount < total);

        // Finished adding data. Commit the worksheet
        await worksheet.commit();

        // Finished the workbook.
        await workbook.commit();

        // Finalize progress.
        app.progress({
            id: progressId,
            status: 'success',
            percentage: 100
        });

        return {file, name: `${payload.name}.${extension}`};
    }
};

function fixDates(row) {
    for (const key of Object.keys(row)) {
        if (_.isDate(row[key])) {
            const date = row[key];

            row[key] = new Date(
                Date.UTC(
                    date.getFullYear(),
                    date.getMonth(),
                    date.getDate(),
                    date.getHours(),
                    date.getMinutes(),
                    date.getSeconds()
                )
            );
        }
    }

    return row;
}
