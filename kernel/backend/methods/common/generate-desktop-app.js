// import path from 'path';
// import fs from 'fs-extra';
// import _ from 'lodash';
// import archiver from 'archiver';
// import UAParser from 'ua-parser-js';
// import nativefier from 'nativefier';
// import Random from 'framework/random';

export default {
    name: 'common.generate-desktop-app',
    async action(payload, params) {
        // const app = this.app;
        // const ua = (new UAParser(params.headers['user-agent'])).getResult();
        // // const platform = ua.os.name === 'Mac OS'
        // //     ? 'osx'
        // //     : ua.os.name === 'Windows' ? 'windows' : 'other';
        // const platform = 'windows'
        // const arch = platform === 'windows' ? 'x64' : 'is32';
        // const tempPath = app.config('paths.temp');
        // const buildPath = path.join(tempPath, Random.id(8));
        //
        // if (platform === 'other')
        //     throw new app.errors.Unprocessable('Un-supported platform');
        //
        // const options = {
        //     name: 'EnterERP',
        //     targetUrl: app.absoluteUrl(),
        //     platform,
        //     arch,
        //     version: app.config('app.version'),
        //     out: buildPath,
        //     overwrite: true,
        //     asar: true,
        //     // icon: '~/Desktop/icon.png',
        //     // counter: false,
        //     // bounce: false,
        //     showMenuBar: false,
        //     maximize: true,
        //     ignoreCertificate: true,
        //     singleInstance: true,
        //     tray: true,
        //     fileDownloadOptions: {
        //         saveAs: true
        //     },
        //     browserwindowOptions: {
        //     },
        //     processEnvs: {
        //         //"GOOGLE_API_KEY": "<your-google-api-key>"
        //     },
        //     slient: true
        // };
        // const appPath = await new Promise((resolve, reject) => {
        //     nativefier(options, function(error, appPath) {
        //         if (error) {
        //             return reject(error);
        //         }
        //
        //         return resolve(appPath);
        //     });
        // });
        //
        // let finalPath = '';
        // let finalName = '';
        // if (platform === 'osx') {
        //     finalName = 'EnterERP.zip';
        //     finalPath = `${Random.id(8)}.zip`;
        //
        //     await new Promise(async (resolve, reject) => {
        //         const output = fs.createWriteStream(path.join(tempPath, finalPath));
        //         const archive = archiver('zip', {
        //             zlib: {level: 1} // Sets the compression level.
        //         });
        //
        //         output.on('close', () => {
        //             resolve();
        //         });
        //         output.on('error', error => {
        //             reject(error);
        //         });
        //
        //         archive.pipe(output);
        //         archive.directory(path.join(appPath, 'EnterERP.app'), 'EnterERP.app');
        //         archive.finalize();
        //     });
        // } else {
        //     finalName = 'EnterERP.zip';
        //     finalPath = `${Random.id(8)}.zip`;
        //
        //     await new Promise(async (resolve, reject) => {
        //         const output = fs.createWriteStream(path.join(tempPath, finalPath));
        //         const archive = archiver('zip', {
        //             zlib: {level: 1} // Sets the compression level.
        //         });
        //
        //         output.on('close', () => {
        //             resolve();
        //         });
        //         output.on('error', error => {
        //             reject(error);
        //         });
        //
        //         archive.pipe(output);
        //         archive.directory(appPath, false);
        //         archive.finalize();
        //     });
        // }
        //
        // await fs.remove(buildPath);
        //
        // return {
        //     file: finalPath,
        //     name: finalName
        // };
    }
};
