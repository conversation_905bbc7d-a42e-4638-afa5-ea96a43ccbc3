import _ from 'lodash';
import {v4 as uuid} from 'uuid';
import pluralize from 'pluralize';
import fastCopy from 'fast-copy';

const normalizeObject = (objects, keysOnly) => {
    const normalized = {};

    for (const key of Object.keys(objects)) {
        let value = objects[key];

        if (!keysOnly && (value === '' || value === undefined || _.isNaN(value))) {
            value = null;
        }

        normalized[_.snakeCase(key)] = value;
    }

    return normalized;
};

export default {
    name: 'common.sync-collection-op-to-report-db',
    async action(payload, params) {
        const app = this.app;

        try {
            const db = app.reportDb;
            if (!db) {
                return;
            }

            if (await app.cache.has('report-sync-in-progress')) {
                return;
            }

            const {schemaName, document, operation} = payload;

            // Get params.
            const programSchemas = app.get('programSchemas') || {};
            const schema = programSchemas[schemaName];
            const tableName = _.snakeCase(schema.name);
            const now = app.datetime.local().toJSDate();
            const id = document._id;
            let fields = fastCopy(schema.fields);
            if (_.isFunction(fields)) {
                fields = await fields(app);
            }
            fields = fields.filter(field => !field.workflowOnly);
            const fieldsMap = {};
            for (const field of fastCopy(fields)) {
                fieldsMap[field.name] = field;

                if (Array.isArray(field.fields) && field.fields.length > 0) {
                    const subFieldsMap = {};

                    for (const subField of field.fields) {
                        subFieldsMap[subField.name] = subField;
                    }

                    fieldsMap[field.name].fields = subFieldsMap;
                }
            }

            const arrayFieldNames = fields.filter(field => field.type === 'array').map(field => field.name);

            // Delete existing.
            for (const arrayFieldName of arrayFieldNames) {
                const subSchema = fields.find(field => field.name === arrayFieldName);
                if (!subSchema) {
                    continue;
                }

                const subTableName = _.snakeCase(`${tableName}.${arrayFieldName}`);
                const schemaNameParts = schema.name.split('.');
                const lastSchemaNamePart = schemaNameParts[schemaNameParts.length - 1];
                const relationFieldName = _.snakeCase(`${pluralize.singular(lastSchemaNamePart)}_id`);

                try {
                    await db(subTableName).where(relationFieldName, id).del();
                } catch (error) {}
            }

            try {
                await db(tableName).where('id', id).del();
            } catch (error) {}

            if (operation === 'create' || operation === 'update' || operation === 'patch') {
                // Get users.
                let users = [];
                let usersMap = {};
                let userIds = [];
                if (!!document.createdBy) {
                    userIds.push(document.createdBy);
                }
                if (!!document.updatedBy) {
                    userIds.push(document.updatedBy);
                }
                userIds = _.uniq(userIds);
                if (userIds.length > 0) {
                    users = await app.collection('kernel.users').find({
                        _id: {$in: userIds},
                        $select: ['_id', 'code', 'name'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });

                    for (const itemUser of users) {
                        usersMap[itemUser._id] = itemUser;
                    }
                }

                // Get data.
                const createdBy = usersMap[document.createdBy];
                const updatedBy = usersMap[document.updatedBy];
                const normalFieldNames = fields.filter(field => field.type !== 'array').map(field => field.name);
                const arrayFieldNames = fields.filter(field => field.type === 'array').map(field => field.name);
                let bulkDocumentExtra = null;
                if (_.isFunction(schema.bulkDocumentExtra)) {
                    bulkDocumentExtra = await schema.bulkDocumentExtra(app, schema, [document]);
                }
                const data = await schema.data(app, schema, document, bulkDocumentExtra);
                const dataWithoutArrayFields = _.pick(_.omit(data, arrayFieldNames), normalFieldNames);

                for (const key of Object.keys(dataWithoutArrayFields)) {
                    const field = fieldsMap[key];

                    if (!field) {
                        delete dataWithoutArrayFields[key];

                        continue;
                    }

                    if (field.type === 'boolean' && typeof dataWithoutArrayFields[key] !== 'boolean') {
                        let value = dataWithoutArrayFields[key];

                        if (typeof value === 'string') {
                            value = value.trim();
                        }

                        dataWithoutArrayFields[key] = !!value;
                    } else if (
                        (field.type === 'integer' || field.type === 'decimal' || field.type === 'float') &&
                        typeof dataWithoutArrayFields[key] !== 'number'
                    ) {
                        let value = dataWithoutArrayFields[key];

                        if (typeof value === 'string') {
                            value = value.trim();
                        }

                        value = parseFloat(value);

                        if (!isNaN(value)) {
                            dataWithoutArrayFields[key] = !value;
                        } else {
                            delete dataWithoutArrayFields[key];
                        }
                    }
                }

                dataWithoutArrayFields.id = id;
                dataWithoutArrayFields.created_at = _.isDate(document.createdAt) ? document.createdAt : now;
                dataWithoutArrayFields.updated_at = _.isDate(document.updatedAt) ? document.updatedAt : now;
                if (!!createdBy) {
                    dataWithoutArrayFields.created_by_code = createdBy.code;
                    dataWithoutArrayFields.created_by_name = createdBy.name;
                }
                if (!!updatedBy) {
                    dataWithoutArrayFields.updated_by_code = updatedBy.code;
                    dataWithoutArrayFields.updated_by_name = updatedBy.name;
                }

                // Insert data.
                try {
                    await db(tableName).insert(normalizeObject(dataWithoutArrayFields));
                } catch (error) {}

                // Insert sub items.
                for (const arrayFieldName of arrayFieldNames) {
                    const items = data[arrayFieldName];

                    if (!Array.isArray(items) || items.length < 1) {
                        continue;
                    }

                    const subSchema = fields.find(field => field.name === arrayFieldName);
                    if (!subSchema) {
                        continue;
                    }

                    const subTableName = _.snakeCase(`${tableName}.${arrayFieldName}`);
                    const schemaNameParts = schema.name.split('.');
                    const lastSchemaNamePart = schemaNameParts[schemaNameParts.length - 1];
                    const relationFieldName = _.snakeCase(`${pluralize.singular(lastSchemaNamePart)}_id`);
                    const subNormalFieldNames = subSchema.fields.map(field => field.name);

                    const rows = [];
                    for (const subItem of items) {
                        const row = _.pick({...subItem}, subNormalFieldNames);

                        for (const key of Object.keys(row)) {
                            const field = fieldsMap[arrayFieldName].fields[key];

                            if (!field) {
                                delete row[key];

                                continue;
                            }

                            if (field.type === 'boolean' && typeof row[key] !== 'boolean') {
                                let value = row[key];

                                if (typeof value === 'string') {
                                    value = value.trim();
                                }

                                row[key] = !!value;
                            } else if (
                                (field.type === 'integer' || field.type === 'decimal' || field.type === 'float') &&
                                typeof row[key] !== 'number'
                            ) {
                                let value = row[key];

                                if (typeof value === 'string') {
                                    value = value.trim();
                                }

                                value = parseFloat(value);

                                if (!isNaN(value)) {
                                    row[key] = !value;
                                } else {
                                    delete row[key];
                                }
                            }
                        }

                        row.id = uuid();
                        row[relationFieldName] = id;
                        row.created_at = _.isDate(document.createdAt) ? document.createdAt : now;
                        row.updated_at = _.isDate(document.updatedAt) ? document.updatedAt : now;
                        if (!!createdBy) {
                            row.created_by_code = createdBy.code;
                            row.created_by_name = createdBy.name;
                        }
                        if (!!updatedBy) {
                            row.updated_by_code = updatedBy.code;
                            row.updated_by_name = updatedBy.name;
                        }

                        rows.push(normalizeObject(row));
                    }

                    try {
                        await db(subTableName).insert(rows);
                    } catch (error) {}
                }
            }
        } catch (error) {
            // console.log(error.message || 'Report sync error!');
        }
    }
};
