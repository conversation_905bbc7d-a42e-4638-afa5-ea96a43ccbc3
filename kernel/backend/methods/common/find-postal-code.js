import _ from 'lodash';
import {escapeRegExp, firstUpper, toLower, toUpper} from 'framework/helpers';

export default {
    name: 'common.find-postal-code',
    async action(payload) {
        const app = this.app;
        const query = {
            countryId: payload.countryId,
            $select: ['postalCode'],
            $and: []
        };

        const citySearches = [];
        const city = payload.city.trim();
        for (let part of city.split(' ')) {
            part = part.trim();

            citySearches.push(
                ...[
                    {
                        city: {
                            $regex: toUpper(escapeRegExp(part)),
                            $options: 'i'
                        }
                    },
                    {
                        city: {
                            $regex: toLower(escapeRegExp(part)),
                            $options: 'i'
                        }
                    },
                    {
                        city: {
                            $regex: firstUpper(escapeRegExp(part)),
                            $options: 'i'
                        }
                    }
                ]
            );
        }
        query.$and.push({
            $or: citySearches
        });

        const districtSearches = [];
        const district = payload.district.trim();
        for (let part of district.split(' ')) {
            part = part.trim();

            districtSearches.push(
                ...[
                    {
                        district: {
                            $regex: toUpper(escapeRegExp(part)),
                            $options: 'i'
                        }
                    },
                    {
                        district: {
                            $regex: toLower(escapeRegExp(part)),
                            $options: 'i'
                        }
                    },
                    {
                        district: {
                            $regex: firstUpper(escapeRegExp(part)),
                            $options: 'i'
                        }
                    }
                ]
            );
        }
        query.$and.push({
            $or: districtSearches
        });

        const subDistrictSearches = [];
        const subDistrict = payload.subDistrict.trim();
        for (let part of subDistrict.split(' ')) {
            part = part.trim();

            subDistrictSearches.push(
                ...[
                    {
                        subDistrict: {
                            $regex: toUpper(escapeRegExp(part)),
                            $options: 'i'
                        }
                    },
                    {
                        subDistrict: {
                            $regex: toLower(escapeRegExp(part)),
                            $options: 'i'
                        }
                    },
                    {
                        subDistrict: {
                            $regex: firstUpper(escapeRegExp(part)),
                            $options: 'i'
                        }
                    }
                ]
            );
        }
        query.$and.push({
            $or: subDistrictSearches
        });

        const result = await app.collection('kernel.locations').find(query);

        if (result.length === 1) {
            return result[0].postalCode;
        }

        return null;
    }
};
