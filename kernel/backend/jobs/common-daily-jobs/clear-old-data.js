export default async function (app) {
    // Remove old jobs.
    await app.db.collection('kernel_jobs').deleteMany(
        {
            repeatInterval: {$exists: false},
            lastRunAt: {$exists: true},
            $or: [{nextRunAt: {$exists: false}}, {nextRunAt: {$eq: null}}]
        },
        {
            collation: {locale: app.config('app.locale')}
        }
    );

    // Remove old notifications.
    await app.db.collection('kernel_notifications').deleteMany(
        {
            createdAt: {
                $lt: app.datetime
                    .local()
                    .minus({
                        months: app.setting('system.notificationsStorageTime')
                    })
                    .toJSDate()
            }
        },
        {
            collation: {locale: app.config('app.locale')}
        }
    );

    // Delete old logs.
    await app.db.collection('kernel_logs').deleteMany(
        {
            date: {
                $lt: app.datetime
                    .local()
                    .minus({months: app.setting('system.logsStorageTime')})
                    .toJSDate()
            }
        },
        {collation: {locale: app.config('app.locale')}}
    );

    // Delete old sessions.
    await app.collection('kernel_sessions').deleteMany(
        {
            startDate: {
                $lt: app.datetime
                    .local()
                    .minus({months: app.setting('system.logsStorageTime')})
                    .toJSDate()
            }
        },
        {collation: {locale: app.config('app.locale')}}
    );

    // Fix dead sessions.
    // if (app.get('isPackaged') && app.isMaster) {
    //     const deadSessions = await app.collection('kernel.sessions').find({
    //         isOnline: true,
    //         $select: ['_id', 'startDate', 'lastActiveDate']
    //     });
    //     const operations = [];
    //
    //     for (const deadSession of deadSessions) {
    //         operations.push({
    //             updateOne: {
    //                 filter: {_id: deadSession._id},
    //                 update: {
    //                     $set: {
    //                         isOnline: false,
    //                         endDate: deadSession.lastActiveDate || deadSession.startDate
    //                     }
    //                 }
    //             }
    //         });
    //     }
    //
    //     if (operations.length > 0) {
    //         await app.collection('kernel.sessions').bulkWrite(operations);
    //     }
    // }
}
