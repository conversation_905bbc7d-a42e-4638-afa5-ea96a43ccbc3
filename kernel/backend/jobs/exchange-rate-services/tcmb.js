import _ from 'lodash';
import Request from 'framework/request';
import axios from 'axios';
import {constants} from 'crypto';
import {Agent} from 'https';
import XML from 'framework/xml';

export default async function (app, date = null) {
    const repeatLastExchangeRateWhenUpdateFailed = app.setting('system.repeatLastExchangeRateWhenUpdateFailed');
    const defaultExchangeRateType = app.setting('system.defaultExchangeRateType');
    const currencies = await app.collection('kernel.currencies').find();
    let now = app.datetime.local().startOf('day');
    let start = now.minus({weeks: 1});
    if (_.isDate(date)) {
        now = app.datetime.fromJSDate(date).endOf('day');
        start = app.datetime.fromJSDate(date).minus({weeks: 1});
    }

    // if ((await app.collection('kernel.exchange-rates').count()) < 1) {
    //     start = app.datetime.local(app.datetime.local().year, 1, 1, 0, 0);
    // }

    let lastItems = null;
    while (now.diff(start).as('days') >= 0) {
        const path = start.toFormat('yyyyLL/ddLLyyyy');
        let response = null;
        let tryCount = 0;

        do {
            try {
                const res = await axios.get(`https://www.tcmb.gov.tr/kurlar/${path}.xml`, {
                    httpsAgent: new Agent({
                        secureOptions: constants.SSL_OP_LEGACY_SERVER_CONNECT,
                        rejectUnauthorized: false
                    })
                });

                response = {
                    statusCode: 200,
                    body: res.data
                };
                // response = await Request.custom({
                //     url: `https://www.tcmb.gov.tr/kurlar/${path}.xml`
                // });
            } catch (error) {
                response = {
                    statusCode: 400,
                    body: ''
                };
            }

            tryCount++;
        } while (response.statusCode !== 200 && tryCount < 10);

        if (!response) {
            return;
        }

        let date = null;
        let items = [];

        if (response.statusCode === 200) {
            const result = (await XML.parse(response.body))['Tarih_Date'];

            date = app.datetime.fromFormat(result.$.Date, 'LL/dd/yyyy').toJSDate();

            for (const row of result['Currency']) {
                const currency = currencies.find(c => c.name === row.$.CurrencyCode);

                if (_.isPlainObject(currency)) {
                    let item = {};

                    item.currencyId = currency._id;
                    item.currencyName = currency.name;
                    if (Array.isArray(row.ForexBuying) && row.ForexBuying.length > 0 && row.ForexBuying[0])
                        item.forexBuying = parseFloat(row.ForexBuying[0]);
                    if (Array.isArray(row.ForexSelling) && row.ForexSelling.length > 0 && row.ForexSelling[0])
                        item.forexSelling = parseFloat(row.ForexSelling[0]);
                    if (Array.isArray(row.BanknoteBuying) && row.BanknoteBuying.length > 0 && row.BanknoteBuying[0])
                        item.banknoteBuying = parseFloat(row.BanknoteBuying[0]);
                    if (Array.isArray(row.BanknoteSelling) && row.BanknoteSelling.length > 0 && row.BanknoteSelling[0])
                        item.banknoteSelling = parseFloat(row.BanknoteSelling[0]);

                    items.push(item);
                }
            }

            if (items.length > 0) {
                lastItems = items;
            }
        } else if (repeatLastExchangeRateWhenUpdateFailed && !_.isNull(lastItems)) {
            items = lastItems;
        }

        if (_.isNull(date)) {
            date = start.toJSDate();
        }

        for (const item of items) {
            const startDate = app.datetime.fromJSDate(date).startOf('day').toJSDate();
            const endDate = app.datetime.fromJSDate(date).endOf('day').toJSDate();
            const rate = await app.collection('kernel.exchange-rates').findOne({
                currencyId: item.currencyId,
                date: {
                    $gte: startDate,
                    $lte: endDate
                },
                $sort: {date: -1}
            });
            let data = _.assign(_.omit(rate || {}, '_id'), {
                date: startDate,
                ...item
            });

            if (_.isNumber(data.forexBuying)) {
                data.rate = data.forexBuying;
            }
            if (!!defaultExchangeRateType && _.isNumber(data[defaultExchangeRateType])) {
                data.rate = data[defaultExchangeRateType];
            }

            if (_.isObject(rate)) {
                if (_.isNumber(rate.rate)) delete data.rate;
                if (_.isNumber(rate.forexBuying)) delete data.forexBuying;
                if (_.isNumber(rate.forexSelling)) delete data.forexSelling;
                if (_.isNumber(rate.banknoteBuying)) delete data.banknoteBuying;
                if (_.isNumber(rate.banknoteSelling)) delete data.banknoteSelling;

                if (!_.isEmpty(_.omit(data, ['date', 'currencyId']))) {
                    await app.collection('kernel.exchange-rates').patch({_id: rate._id}, data);
                }
            } else {
                await app.collection('kernel.exchange-rates').create(data);
            }
        }

        start = start.plus({days: 1});
    }

    await app.cache.clear('daily-exchange-rates-');
}
