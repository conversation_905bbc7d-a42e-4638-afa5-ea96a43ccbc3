import _ from 'lodash';

export default {
    name: 'update-numberings',
    repeatEvery: '30 0 * * *',
    async action(app) {
        const numberings = await app.collection('kernel.numberings').find({
            $select: ['_id', 'code']
        });

        for (const numbering of numberings) {
            await app.collection('kernel.numberings').patch({_id: numbering._id}, {code: numbering.code});
        }
    }
};
