import _ from 'lodash';

export default {
    name: 'sync-index-records',
    repeatEvery: '0 0 * * *',
    async action(app) {
        const collections = await app.db.collections();
        const now = app.datetime.local().toJSDate();
        const recordsOperations = [];

        const existingRecords = await app.collection('kernel.index-records').find({});
        const existingRecordsMap = {};
        for (const existingRecord of existingRecords) {
            const existingRecordKey = `${existingRecord.collectionName}${existingRecord.indexName}`;

            existingRecordsMap[existingRecordKey] = existingRecord;
        }

        for (const collection of collections) {
            const collectionName = collection.collectionName;
            const indexStats = await (await collection.aggregate([{$indexStats: {}}])).toArray();
            const documentCount = await collection.estimatedDocumentCount();

            for (const indexStat of indexStats) {
                const record = {};

                record.status = 'active';
                record.collectionName = collectionName;
                record.indexName = indexStat.name;
                record.documentCount = documentCount;
                record.usageCount = (indexStat.accesses ?? {}).ops;
                record.createdAt = (indexStat.accesses ?? {}).since;
                record.updatedAt = now;

                if (!record.collectionName) {
                    continue;
                }
                if (!record.indexName) {
                    continue;
                }
                if (!_.isFinite(record.usageCount)) {
                    continue;
                }
                if (!_.isDate(record.createAt)) {
                    continue;
                }

                const existingRecordKey = `${record.collectionName}${record.indexName}`;
                const existingRecord = existingRecordsMap[existingRecordKey];
                if (!!existingRecord) {
                    recordsOperations.push({
                        updateOne: {
                            filter: {_id: existingRecord._id},
                            update: {
                                $set: {
                                    documentCount: record.documentCount,
                                    usageCount: existingRecord.usageCount + record.usageCount,
                                    updatedAt: record.updatedAt
                                }
                            }
                        }
                    });
                } else {
                    recordsOperations.push({
                        insertOne: {document: record}
                    });
                }
            }
        }

        if (recordsOperations.length > 0) {
            await app.collection('kernel.index-records').bulkWrite(recordsOperations);
        }

        const unusedIndexes = await app.collection('kernel.index-records').find({
            status: 'active',
            usageCount: 0,
            documentCount: {$gt: 0},
            createdAt: {$lt: app.datetime.local().minus({days: 15}).toJSDate()}
        });
        const unusedIndexesOperations = [];
        for (const unusedIndex of unusedIndexes) {
            await app.db.collection(unusedIndex.collectionName).dropIndex(unusedIndex.indexName);

            unusedIndexesOperations.push({
                updateOne: {
                    filter: {_id: unusedIndex._id},
                    update: {
                        $set: {
                            status: 'deleted'
                        }
                    }
                }
            });
        }
        if (unusedIndexesOperations.length > 0) {
            await app.collection('kernel.index-records').bulkWrite(unusedIndexesOperations);
        }
    }
};
