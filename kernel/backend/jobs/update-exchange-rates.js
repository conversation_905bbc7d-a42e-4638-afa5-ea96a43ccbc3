import _ from 'lodash';

import ecb from './exchange-rate-services/ecb';
import fta from './exchange-rate-services/fta';
import tcmb from './exchange-rate-services/tcmb';

export default {
    name: 'update-exchange-rates',
    repeatEvery: '30 0 * * *',
    async action(app) {
        const services = {ecb, fta, tcmb};
        const automaticExchangeRate = app.setting('system.automaticExchangeRate');
        const exchangeRateService = app.setting('system.exchangeRateService');

        if (automaticExchangeRate) {
            await services[exchangeRateService](app);

            if (_.isFunction(app.log)) {
                app.log({
                    level: 'success',
                    message: app.translate('Exchange rates updated successfully.')
                });
            }

            app.emit('exchange-rates-changed');
        }
    }
};
