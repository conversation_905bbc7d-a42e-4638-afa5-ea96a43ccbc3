import axios from 'axios';

export default {
    name: 'perform-automatic-backup',
    repeatEvery: '45 3 * * *',
    async action(app) {
        if (app.setting('system.automaticBackup') && !app.get('isDevelopment')) {
            // Remove old backups.
            const oldBackups = await app.collection('kernel.backups').find({
                date: {
                    $lt: app.datetime
                        .local()
                        .minus({
                            days: 2
                        })
                        .toJSDate()
                },
                $select: ['_id']
            });
            for (const oldBackup of oldBackups) {
                try {
                    await app.backups.remove(oldBackup._id);
                } catch (error) {
                    console.log('Backup remove error:', error.message);
                }
            }

            try {
                await app.backups.create('Periodic backup', 'System');

                app.log({
                    level: 'success',
                    message: app.translate('Backup created successfully.')
                });
            } catch (error) {
                console.log(error);
            }
        }

        if (app.absoluteUrl() !== 'https://main.entererp.com' && !(await app.cache.has('report-sync-in-progress'))) {
            await axios.get('http://localhost:2999/maintenance');
        }
    }
};
