import _ from 'lodash';
import fastCopy from 'fast-copy';
import {ObjectId} from 'mongodb';
import Proto from 'uberproto';
import errors from '@feathersjs/errors';
import {select, filterQuery} from './utils';
import {hash} from 'framework/helpers';

function errorHandler(error) {
    let appError = error;

    // NOTE (EK): The list of error code is way too massive to map
    // them to a specific error object so we'll use a generic one.
    // See https://github.com/mongodb/mongo/blob/master/docs/errors.md

    if (error.name === 'MongoError') {
        appError = new errors.GeneralError(error, {
            ok: error.ok,
            code: error.code
        });
    }

    throw appError;
}

function getGroupAggregationPipeline(filters, query) {
    const groupBy = filters.$groupBy;
    const pipeline = [];
    const $facet = {result: [], total: []};

    // Document must have corresponding value for grouping field.
    if (!Array.isArray(query.$and)) query.$and = [];
    query.$and = query.$and.concat([{[groupBy.field]: {$exists: true, $ne: ''}}, {[groupBy.field]: {$ne: null}}]);

    // Common match stage.
    pipeline.push({$match: query});

    // Result group stage.
    const $group = {};
    $group._id = `$${groupBy.field}`;
    _.each(groupBy.aggregate, (accumulator, field) => {
        if (accumulator === 'count') {
            $group[field] = {$sum: 1};
        } else {
            $group[field] = {[`$${accumulator}`]: `$${field}`};
        }
    });
    $facet.result.push({$group});

    // Result project stage.
    const $project = {};
    $project['_id'] = 1;
    $project[groupBy.field] = '$_id';
    _.each(groupBy.aggregate, (accumulator, field) => {
        $project[field] = 1;
    });
    $facet.result.push({$project});

    // Result sort stage.
    if (filters.$sort) {
        $facet.result.push({$sort: filters.$sort});
    }

    // Result skip stage
    if (filters.$skip) {
        $facet.result.push({$skip: filters.$skip});
    }

    // Result limit stage.
    if (filters.$limit) {
        $facet.result.push({$limit: filters.$limit});
    }

    // Total.
    $facet.total.push({$group});
    $facet.total.push({$count: 'count'});

    pipeline.push({$facet});

    return pipeline;
}

// Create the service.
class Service {
    constructor(options) {
        if (!options) {
            throw new Error('MongoDB options have to be provided');
        }

        this.Model = options.Model;
        this.id = options.id || '_id';
        this.events = options.events || [];
        this.paginate = options.paginate || {};
    }

    extend(obj) {
        return Proto.extend(obj, this);
    }

    _objectifyId(id) {
        if (this.id === '_id' && ObjectId.isValid(id)) {
            id = new ObjectId(id.toString());
        }

        return id;
    }

    _objectifyIdsInQuery(idObject) {
        const result = {};

        _.each(idObject, (id, operator) => {
            if (Array.isArray(id)) {
                result[operator] = [];

                for (const i of id) {
                    if (ObjectId.isValid(i)) {
                        result[operator].push(new ObjectId(i.toString()));
                    }
                }
            } else {
                result[operator] = new ObjectId(id.toString());
            }

            // result[operator] = Array.isArray(id)
            //     ? id.filter(i => ObjectId.isValid(i)).map(i => new ObjectId(i.toString()))
            //     : new ObjectId(id.toString());
        });

        return result;
    }

    _multiOptions(id, params) {
        let query = filterQuery(params.query || {}).query,
            options = Object.assign({multi: true}, params.mongodb || params.options);

        if (id !== null) {
            options.multi = false;
            query[this.id] = this._objectifyId(id);
        }

        return {query, options};
    }

    _getSelect(select) {
        if (Array.isArray(select)) {
            let result = {};

            for (const name of select) {
                if (name.indexOf('.') !== -1) {
                    const parts = name.split('.');

                    if (select.some(s => s === parts[0])) {
                        continue;
                    }
                }

                result[name] = 1;
            }

            return result;
        }

        return select;
    }

    async _find(params, count, getFilter = filterQuery) {
        // Start with finding all, and limit when necessary.
        let {filters, query} = getFilter(params.query || {});
        let cacheKey = null;

        // Try to load from cache first.
        if (!!this.cache) {
            let result = null;

            try {
                cacheKey = `records-caches-${this.collectionName}-${count}-${hash(query)}-${hash(filters)}`;
                result = this.cacheManager.get(cacheKey);
            } catch (error) {
                cacheKey = null;
                result = null;
            }

            if (result !== null) {
                return result;
            }
        }

        // Objectify the id field if it's present
        if (!params.dontObjectifyId) {
            if (query[this.id]) {
                if (_.isString(query[this.id])) {
                    query[this.id] = this._objectifyId(query[this.id]);
                } else if (_.isObject(query[this.id])) {
                    query[this.id] = this._objectifyIdsInQuery(query[this.id]);
                }
            }
            ['$and', '$or'].forEach(operator => {
                if (Array.isArray(query[operator])) {
                    query[operator] = query[operator].map(subQuery => {
                        if (_.isString(subQuery[this.id])) {
                            subQuery[this.id] = this._objectifyId(subQuery[this.id]);
                        } else if (_.isObject(subQuery[this.id])) {
                            subQuery[this.id] = this._objectifyIdsInQuery(subQuery[this.id]);
                        }

                        return subQuery;
                    });
                }
            });
        }

        if (!_.isNull(filters.$groupBy)) {
            const pipeline = getGroupAggregationPipeline(filters, query);

            const q = this.Model.aggregate(pipeline, {
                allowDiskUse: true,
                collation: _.isObject(params.collation) ? params.collation : {locale: 'en'}
            });

            const data = await q.toArray();
            let total = 0;
            if (Array.isArray(data) && data.length > 0 && Array.isArray(data[0].total) && data[0].total.length > 0) {
                total = data[0].total[0].count;
            }

            const result = {
                total,
                limit: filters.$limit,
                skip: filters.$skip || 0,
                data: data[0].result
            };

            if (!!this.cache && !!cacheKey) {
                try {
                    await this.cacheManager.set(cacheKey, result, 60 * 60 * 24 * 7);
                } catch (error) {}
            }

            return result;
        } else {
            const q = this.Model.find(query);

            if (filters.$select) {
                q.project(this._getSelect(filters.$select));
            }

            if (filters.$sort) {
                q.sort(filters.$sort);
            }

            if (params.collation) {
                q.collation(params.collation);
            }

            if (filters.$limit) {
                q.limit(filters.$limit);
            }

            if (filters.$skip) {
                q.skip(filters.$skip);
            }

            let runQuery = total => {
                return q.toArray().then(data => {
                    return {
                        total,
                        limit: filters.$limit,
                        skip: filters.$skip || 0,
                        data: data.map(d => {
                            if (!!d._id.toString) {
                                d._id = d._id.toString();
                            }

                            return d;
                        })
                    };
                });
            };

            if (filters.$limit === 0) {
                runQuery = total => {
                    return Promise.resolve({
                        total,
                        limit: filters.$limit,
                        skip: filters.$skip || 0,
                        data: []
                    });
                };
            }

            let result = null;

            if (count) {
                result = await runQuery(await this.Model.countDocuments(query));
            } else {
                result = await runQuery();
            }

            if (!!this.cache && !!cacheKey) {
                try {
                    await this.cacheManager.set(cacheKey, result, 60 * 60 * 24 * 7);
                } catch (error) {}
            }

            return result;
        }
    }

    find(params) {
        const paginate = params && typeof params.paginate !== 'undefined' ? params.paginate : this.paginate;
        const result = this._find(params, !!paginate.default, query => filterQuery(query, paginate));

        if (!paginate.default) {
            return result.then(page => page.data);
        }

        return result;
    }

    count(params) {
        let {query} = filterQuery(params.query || {});

        // Objectify the id field if it's present
        if (!params.dontObjectifyId) {
            if (query[this.id]) {
                if (_.isString(query[this.id])) {
                    query[this.id] = this._objectifyId(query[this.id]);
                } else if (_.isObject(query[this.id])) {
                    query[this.id] = this._objectifyIdsInQuery(query[this.id]);
                }
            }
            ['$and', '$or'].forEach(operator => {
                if (Array.isArray(query[operator])) {
                    query[operator] = query[operator].map(subQuery => {
                        if (_.isString(subQuery[this.id])) {
                            subQuery[this.id] = this._objectifyId(subQuery[this.id]);
                        } else if (_.isObject(subQuery[this.id])) {
                            subQuery[this.id] = this._objectifyIdsInQuery(subQuery[this.id]);
                        }

                        return subQuery;
                    });
                }
            });
        }

        return this.Model.countDocuments(query);
    }

    _get(id, params) {
        let dontObjectifyId = false;

        if (_.isObject(params) && _.isBoolean(params.dontObjectifyId)) {
            dontObjectifyId = params.dontObjectifyId;
        }

        id = !dontObjectifyId ? this._objectifyId(id) : id;

        return this.Model.findOne({[this.id]: id})
            .then(data => {
                if (!data) {
                    throw new errors.NotFound(`No record found for id '${id}'`);
                }

                if (!!data._id.toString) {
                    data._id = data._id.toString();
                }

                return data;
            })
            .then(select(params, this.id))
            .catch(errorHandler);
    }

    async get(id, params) {
        let cacheKey = null;

        if (!!this.cache) {
            let result = null;

            try {
                cacheKey = `records-caches-${this.collectionName}-${id}}`;
                result = this.cacheManager.get(cacheKey);
            } catch (error) {
                cacheKey = null;
                result = null;
            }

            if (result !== null) {
                return result;
            }
        }

        let result = await this._get(id, params);

        if (!!this.cache && !!cacheKey) {
            try {
                await this.cacheManager.set(cacheKey, result, 60 * 60 * 24 * 7);
            } catch (error) {}
        }

        return result;
    }

    _findOrGet(id, params) {
        if (id === null) {
            return this._find(params).then(page => page.data);
        }

        return this._get(id, params);
    }

    create(data, params) {
        const setId = item => {
            const entry = Object.assign({}, item);

            // Generate a MongoId if we use a custom id
            if (this.id !== '_id' && typeof entry[this.id] === 'undefined') {
                entry[this.id] = new ObjectId().toHexString();
            }

            return entry;
        };

        const isMultiple = Array.isArray(data);
        const method = isMultiple ? 'insertMany' : 'insertOne';

        return this._deleteCache().then(() =>
            this.Model[method](isMultiple ? data.map(setId) : setId(data))
                .then(result => {
                    result = result.ops.length > 1 ? result.ops : result.ops[0];

                    if (Array.isArray(result)) {
                        result = result.map(r => {
                            if (!!r._id.toString) {
                                r._id = r._id.toString();
                            }

                            return r;
                        });
                    } else {
                        if (result._id.toString) {
                            result._id = result._id.toString();
                        }
                    }

                    return result;
                })
                .then(select(params, this.id))
                .catch(errorHandler)
        );
    }

    _normalizeId(id, data) {
        if (this.id === '_id') {
            // Default Mongo IDs cannot be updated. The Mongo library handles
            // this automatically.
            return _.omit(data, this.id);
        } else if (id !== null) {
            // If not using the default Mongo _id field set the ID to its
            // previous value. This prevents orphaned documents.
            return Object.assign({}, data, {[this.id]: id});
        } else {
            return data;
        }
    }

    // Map stray records into $set
    _remapModifiers(data) {
        let set = {};

        data = fastCopy(data);

        // Step through the root
        for (let key in data) {
            // Check for keys that aren't modifiers
            if (key.charAt(0) !== '$') {
                // Move them to set, and remove their record
                set[key] = data[key];
                delete data[key];
            }

            // If the '$set' modifier is used, add that to the temp variable
            if (key === '$set') {
                set = Object.assign(set, data[key]);
                delete data[key];
            }
        }

        // If we have a $set, then attach to the data object
        if (Object.keys(set).length > 0) {
            data['$set'] = set;
        }

        return data;
    }

    patch(id, data, params) {
        let {query, options} = this._multiOptions(id, params);
        const mapIds = page => page.data.map(current => current[this.id]);

        // By default we will just query for the one id. For multi patch
        // we create a list of the ids of all items that will be changed
        // to re-query them after the update
        const ids = id === null ? this._find(params).then(mapIds) : Promise.resolve([id]);

        let dontObjectifyId = false;
        if (_.isObject(params) && _.isBoolean(params.dontObjectifyId)) {
            dontObjectifyId = params.dontObjectifyId;
        }

        // Objectify the id field if it's present
        if (query['_id'] && _.isPlainObject(query['_id']) && !dontObjectifyId) {
            query['_id'] = this._objectifyIdsInQuery(query['_id']);
        }

        if (params.collation) {
            options.collation = params.collation;
        }

        // Run the query
        return this._deleteCache().then(() =>
            ids
                .then(idList => {
                    // Create a new query that re-queries all ids that
                    // were originally changed
                    const findParams = Object.assign({}, params, {
                        query: {
                            [this.id]: {$in: idList}
                        }
                    });

                    return this.Model.updateMany(
                        query,
                        this._remapModifiers(this._normalizeId(id, data)),
                        options
                    ).then(() => this._findOrGet(id, findParams));
                })
                .then(select(params, this.id))
                .catch(errorHandler)
        );
    }

    update(id, data, params) {
        if (Array.isArray(data) || id === null) {
            return Promise.reject(new errors.BadRequest('Not replacing multiple records. Did you mean `patch`?'));
        }

        let {query, options} = this._multiOptions(id, params);

        if (params.collation) {
            options.collation = params.collation;
        }

        return this._deleteCache().then(() =>
            this.Model.updateMany(query, this._normalizeId(id, data), options)
                .then(() => this._findOrGet(id))
                .then(select(params, this.id))
                .catch(errorHandler)
        );
    }

    remove(id, params) {
        let {query, options} = this._multiOptions(id, params);

        if (query['_id'] && _.isPlainObject(query['_id'])) {
            query['_id'] = this._objectifyIdsInQuery(query['_id']);
        }

        if (params.collation) {
            options.collation = params.collation;
        }

        return this._deleteCache().then(() =>
            this._findOrGet(id, params)
                .then(items =>
                    this.Model.deleteMany(query, options)
                        .then(() => items)
                        .then(select(params, this.id))
                )
                .catch(errorHandler)
        );
    }

    _deleteCache() {
        return new Promise(async resolve => {
            try {
                await this.cacheManager.clear(`aggregation-caches-${this.collectionName}`);
            } catch (e) {
                console.log(e);
            }

            if (this.cache) {
                try {
                    this.cacheManager.clear(`records-caches-${this.collectionName}`);
                } catch (e) {
                    console.log(e);
                }
            }

            resolve();
        });
    }
}

export default function init(options) {
    return new Service(options);
}

init.Service = Service;
