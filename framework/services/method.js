import _ from 'lodash';
import {isPromise} from 'framework/helpers';

class Service {
    create(data, params) {
        // Fix errors "First parameter for 'create' must be an object".
        if (!_.isUndefined(data.__value)) {
            data = data.__value;
        }

        let result = this.action.call(this, data, params);

        if (!isPromise(result)) {
            return Promise.resolve(result);
        }

        return result;
    }
}

export default function init(options) {
    return new Service(options);
}

init.Service = Service;
