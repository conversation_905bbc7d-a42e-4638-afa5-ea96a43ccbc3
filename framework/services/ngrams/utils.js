// import {toLower} from 'framework/helpers';

export const replaceSymbols = replaceLanguageCharacters => (text, escapeSpecialCharacters) => {
    // text = text.toLowerCase();
    // text = toLower(text);

    if (escapeSpecialCharacters) {
        text = text.replace(/[!"#%&'()*+,-./:;<=>?@[\\\]^`{|}~]/g, ''); // remove special characters
    }

    text = text.replace(/_/g, ' ');
    text = replaceLanguageCharacters(text);

    return text;
};
