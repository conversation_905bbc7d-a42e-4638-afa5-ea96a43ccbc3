import _ from 'lodash';

function orderResult(items, sortFields, sortDirections) {
    if (sortFields.some(field => field.indexOf('.') !== -1)) {
        return _.orderBy(items, item => _.get(item, sortFields[0]), sortDirections[0]);
    }

    return _.orderBy(items, sortFields, sortDirections);
}

// An in-memory sorting function according to the
// $sort special query parameter
export function sort(values, $sort) {
    const sortFields = [];
    const sortDirections = [];

    _.each($sort, (direction, field) => {
        sortFields.push(field);
        sortDirections.push(direction === 1 || direction === 'asc' ? 'asc' : 'desc');
    });

    return orderResult(values, sortFields, sortDirections);
}

// Return a function that filters a result object or array
// and picks only the fields passed as `params.query.$select`
// and additional `otherFields`
export function select(params, ...otherFields) {
    const fields = params && params.query && params.query.$select;

    if (Array.isArray(fields) && otherFields.length) {
        fields.push(...otherFields);
    }

    const convert = result => {
        if (!Array.isArray(fields)) {
            return result;
        }

        return _.pick(result, ...fields);
    };

    return result => {
        if (Array.isArray(result)) {
            return result.map(convert);
        }

        return convert(result);
    };
}

// Converts Feathers special query parameters and pagination settings
// and returns them separately a `filters` and the rest of the query
// as `query`
export function filterQuery(query, paginate) {
    // Officially supported query parameters ($populate is kind of special)
    const PROPERTIES = [
        '$sort',
        '$limit',
        '$skip',
        '$select',
        '$populate',
        '$groupBy',
        '$disableActiveCheck',
        '$disableSoftDelete',
        '$isSearching'
    ];

    function parse(number) {
        if (typeof number !== 'undefined') {
            return Math.abs(parseInt(number, 10));
        }
    }

    // Returns the pagination limit and will take into account the
    // default and max pagination settings
    function getLimit(limit, paginate) {
        if (paginate && paginate.default) {
            const lower = typeof limit === 'number' ? limit : paginate.default;
            const upper = typeof paginate.max === 'number' ? paginate.max : Number.MAX_VALUE;

            return Math.min(lower, upper);
        }

        return limit;
    }

    // Makes sure that $sort order is always converted to an actual number
    function convertSort(sort) {
        if (typeof sort !== 'object' || Array.isArray(sort)) {
            return sort;
        }

        const result = {};

        Object.keys(sort).forEach(key => {
            result[key] = typeof sort[key] === 'object' ? sort[key] : parseInt(sort[key], 10);
        });

        return result;
    }

    function getGroup(groupBy) {
        if (_.isString(groupBy)) {
            return {
                field: groupBy,
                aggregate: {
                    count: 'count'
                }
            };
        } else if (_.isPlainObject(groupBy)) {
            if (!_.isString(groupBy.field)) {
                throw new Error('Invalid group by definition!');
            }

            return {
                field: groupBy.field,
                aggregate: _.isObject(groupBy.aggregate) ? groupBy.aggregate : {count: 'count'}
            };
        }

        return null;
    }

    let filters = {
        $sort: convertSort(query.$sort),
        $limit: getLimit(parse(query.$limit), paginate),
        $skip: parse(query.$skip),
        $select: query.$select,
        $populate: query.$populate,
        $groupBy: getGroup(query.$groupBy)
    };

    return {filters, query: _.omit(query, ...PROPERTIES)};
}
