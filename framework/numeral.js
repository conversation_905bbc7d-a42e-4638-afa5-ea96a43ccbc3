import _ from 'lodash';
import Big from 'big.js';

export default class Numeral {
    constructor(options) {
        this.options = _.defaults(options, {
            currency: {
                symbol: '$', // default currency symbol is '$'
                format: '%s%v', // controls output: %s = symbol, %v = value (can be object, see docs)
                decimal: '.', // decimal point separator
                thousand: ',', // thousands separator
                precision: 2, // decimal places
                grouping: 3 // digit grouping (not implemented yet)
            },
            number: {
                precision: 0, // default precision on numbers is 0
                grouping: 3, // digit grouping (not implemented yet)
                thousand: ',',
                decimal: '.'
            }
        });
    }

    /**
     * Implementation of toFixed() that treats floats more like decimals
     *
     * Fixes binary rounding issues (eg. (0.615).toFixed(2) === "0.61") that present
     * problems for accounting- and finance-related software.
     */
    toFixed(value, precision) {
        precision = this._checkPrecision(precision, this.options.number.precision);

        return Big(this.unFormat(value)).toFixed(precision);

        // precision = this._checkPrecision(precision, this.options.number.precision);
        //
        // let exponentialForm = Number(this.unFormat(value) + 'e' + precision),
        //     rounded = Math.round(exponentialForm);
        //
        // return Number(rounded + 'e-' + precision).toFixed(precision);
    }

    /**
     * Format a number, with comma-separated thousands and custom precision/decimal places
     *
     * Localise by overriding the precision and thousand / decimal separators
     * 2nd parameter `precision` can be an object matching `option.number`
     */
    format(number, precision, thousand, decimal) {
        // Recursively format arrays:
        if (Array.isArray(number)) {
            return _.map(number, val => this.format(val, precision, thousand, decimal));
        }

        // Clean up number:
        number = this.unFormat(number);

        // Fix: very small number
        // Note: Tried but not solved. Problem on Exchange rate table
        // if (_.isNaN(Number(this.unFormat(number) + 'e' + precision))) {
        //     number = 0;
        // }

        // Build options object from second param (if object) or all params, extending defaults:
        let opts = _.defaults(
            _.isObject(precision)
                ? precision
                : {
                      precision: precision,
                      thousand: thousand,
                      decimal: decimal
                  },
            this.options.number
        );

        // Clean up precision
        let usePrecision = this._checkPrecision(opts.precision);

        // Do some calc:
        let negative = number < 0 ? '-' : '',
            base = parseInt(this.toFixed(Math.abs(number || 0), usePrecision), 10) + '',
            mod = base.length > 3 ? base.length % 3 : 0;

        // Format the number:
        return (
            negative +
            (mod ? base.substr(0, mod) + opts.thousand : '') +
            base.substr(mod).replace(/(\d{3})(?=\d)/g, '$1' + opts.thousand) +
            (usePrecision ? opts.decimal + this.toFixed(Math.abs(number), usePrecision).split('.')[1] : '')
        );
    }

    /**
     * Format a number into currency
     *
     * Usage: number.formatMoney(number, symbol, precision, thousandsSep, decimalSep, format)
     * defaults: (0, "$", 2, ",", ".", "%s%v")
     *
     * Localise by overriding the symbol, precision, thousand / decimal separators and format
     * Second param can be an object matching `options.currency` which is the easiest way.
     *
     * To do: tidy up the parameters
     */
    formatCurrency(number, symbol, precision, thousand, decimal, format) {
        // Recursively format arrays:
        if (Array.isArray(number)) {
            return _.map(number, val => this.formatCurrency(val, symbol, precision, thousand, decimal, format));
        }

        // Clean up number:
        number = this.unFormat(number);

        // Build options object from second param (if object) or all params, extending defaults:
        let opts = _.defaults(
            _.isObject(symbol)
                ? symbol
                : {
                      symbol: symbol,
                      precision: precision,
                      thousand: thousand,
                      decimal: decimal,
                      format: format
                  },
            this.options.currency
        );

        // Fix: very small number
        if (_.isNaN(Number(this.unFormat(number) + 'e' + opts.precision))) {
            number = 0;
        }

        // Check format (returns object with pos, neg and zero):
        let formats = this._checkCurrencyFormat(opts.format);

        // Choose which format to use for this value:
        let useFormat = number > 0 ? formats.pos : number < 0 ? formats.neg : formats.zero;

        // Return with currency symbol added:
        return useFormat
            .replace('%s', opts.symbol)
            .replace(
                '%v',
                this.format(Math.abs(number), this._checkPrecision(opts.precision), opts.thousand, opts.decimal)
            );
    }

    /**
     * Takes a string/array of strings, removes all formatting/cruft and returns the raw float value
     *
     * Decimal must be included in the regular expression to match floats (defaults to
     * options.number.decimal), so if the number uses a non-standard decimal
     * separator, provide it as the second argument.
     *
     * Also matches bracketed negatives (eg. "$ (1.99)" => -1.99)
     *
     * Doesn't throw any errors (`NaN`s become 0) but this may change in future
     */
    unFormat(value, decimal) {
        // Recursively unformat arrays:
        if (Array.isArray(value)) {
            return _.map(value, val => this.unFormat(val, decimal));
        }

        // Return the value as-is if it's already a number:
        if (_.isNumber(value)) return value;

        // Default decimal point comes from settings, but could be set to eg. "," in opts:
        decimal = decimal || this.options.number.decimal;

        // Build regex to strip out everything except digits, decimal point and minus sign:
        let regex = new RegExp('[^0-9-' + decimal + ']', ['g']),
            unFormatted = parseFloat(
                ('' + value)
                    .replace(/\((?=\d+)(.*)\)/, '-$1') // replace bracketed values with negatives
                    .replace(regex, '') // strip out any cruft
                    .replace(decimal, '.') // make sure decimal point is standard
            );

        // This will fail silently which may cause trouble, let's wait and see:
        return !isNaN(unFormatted) ? unFormatted : false;
    }

    /**
     * Check and normalise the value of precision
     * (must be positive integer)
     */
    _checkPrecision(val, base) {
        val = Math.round(Math.abs(val));

        return isNaN(val) ? base : val;
    }

    /**
     * Parses a format string or object and returns format obj for use in rendering
     *
     * `format` is either a string with the default (positive) format, or object
     * containing `pos` (required), `neg` and `zero` values (or a function returning
     * either a string or object)
     *
     * Either string or format.pos must contain "%v" (value) to be valid
     */
    _checkCurrencyFormat(format) {
        const defaults = this.options.currency.format;

        // Allow function as format parameter (should return string or object):
        if (_.isFunction(format)) format = format();

        // Format can be a string, in which case `value` ("%v") must be present:
        if (_.isString(format) && format.match('%v')) {
            // Create and return positive, negative and zero formats:
            return {
                pos: format,
                neg: format.replace('-', '').replace('%v', '-%v'),
                zero: format
            };
        } else if (!format || !format.pos || !format.pos.match('%v')) {
            // If no format, or object is missing valid positive value, use defaults:

            // If defaults is a string, casts it to an object for faster checking next time:
            return !_.isString(defaults)
                ? defaults
                : (this.options.currency.format = {
                      pos: defaults,
                      neg: defaults.replace('%v', '-%v'),
                      zero: defaults
                  });
        }

        // Otherwise, assume format was fine:
        return format;
    }
}
