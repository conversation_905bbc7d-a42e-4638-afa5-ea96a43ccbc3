import {DateTime, Duration, Interval, Settings} from 'luxon';
import TimeAgo from 'javascript-time-ago';
import TimeAgoEN from 'javascript-time-ago/locale/en';
import TimeAgoTR from 'javascript-time-ago/locale/tr';

function setLocale(locale) {
    Settings.defaultLocale = locale;
}

function setZone(zone) {
    Settings.defaultZoneName = zone;
}

Settings.throwOnInvalid = true;

TimeAgo.addLocale(TimeAgoEN);
TimeAgo.addLocale(TimeAgoTR);

export function timeAgo(date = Date.now(), style = 'default') {
    let locale = 'en-US';

    if (Settings.defaultLocale === 'tr') {
        locale = 'tr-TR';
    }

    const timeAgo = new TimeAgo(locale);

    return timeAgo.format(date, style);
}

export {DateTime, Duration, Interval, setLocale, setZone};
