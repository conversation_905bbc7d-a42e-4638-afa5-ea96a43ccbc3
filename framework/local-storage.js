import _ from 'lodash';
import <PERSON><PERSON><PERSON><PERSON> from './ejson';

export default class LocalStorage {
    constructor(clientVersion = '') {
        this.clientVersion = clientVersion;
    }

    get(key) {
        key = `${this.clientVersion}_${key}`;

        const item = localStorage.getItem(key);

        if (!_.isString(item)) {
            return null;
        }

        return EJSON.parse(item);
    }

    set(key, data) {
        key = `${this.clientVersion}_${key}`;

        localStorage.setItem(key, EJSON.stringify(data));
    }

    remove(key) {
        key = `${this.clientVersion}_${key}`;

        localStorage.removeItem(key);
    }

    clear() {
        localStorage.clear();
    }
}
