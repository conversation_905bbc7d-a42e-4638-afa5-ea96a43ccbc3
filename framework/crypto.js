import crypto from 'crypto';

const algorithm = 'aes-256-ctr';
const secretKey = `eRv8fnpxAvpSqWXqEb0pW6c5mmzZbZyT`;

export function encrypt(text) {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv(algorithm, secretKey, iv);
    const encrypted = Buffer.concat([cipher.update(text), cipher.final()]);

    return {
        iv: iv.toString('hex'),
        content: encrypted.toString('hex')
    };
}

export function decrypt(hash) {
    const decipher = crypto.createDecipheriv(algorithm, secretKey, Buffer.from(hash.iv, 'hex'));

    return Buffer.concat([decipher.update(Buffer.from(hash.content, 'hex')), decipher.final()]).toString();
}
