import _ from 'lodash';
import request from 'request';

export default class Request {
    static get(url, options = {}) {
        return new Promise((resolve, reject) => {
            request(
                {
                    url,
                    method: 'GET',
                    ..._.omit(options, ['url', 'method'])
                },
                (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        resolve(body);
                    }
                }
            );
        });
    }

    static post(url, data = {}, options = {}) {
        return new Promise((resolve, reject) => {
            request(
                {
                    url,
                    method: 'POST',
                    body: data,
                    json: true,
                    ..._.omit(options, ['url', 'method', 'body', 'json'])
                },
                (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        resolve(body);
                    }
                }
            );
        });
    }

    static put(url, data = {}, options = {}) {
        return new Promise((resolve, reject) => {
            request(
                {
                    url,
                    method: 'PUT',
                    body: data,
                    json: true,
                    ..._.omit(options, ['url', 'method', 'body', 'json'])
                },
                (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        resolve(body);
                    }
                }
            );
        });
    }

    static patch(url, data = {}, options = {}) {
        return new Promise((resolve, reject) => {
            request(
                {
                    url,
                    method: 'PATCH',
                    body: data,
                    json: true,
                    ..._.omit(options, ['url', 'method', 'body', 'json'])
                },
                (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        resolve(body);
                    }
                }
            );
        });
    }

    static delete(url, data = {}, options = {}) {
        return new Promise((resolve, reject) => {
            request(
                {
                    url,
                    method: 'DELETE',
                    body: data,
                    json: true,
                    ..._.omit(options, ['url', 'method', 'body', 'json'])
                },
                (error, response, body) => {
                    if (error) {
                        reject(error);
                    } else {
                        resolve(body);
                    }
                }
            );
        });
    }

    static custom(options) {
        return new Promise((resolve, reject) => {
            request(options, (error, response, body) => {
                if (error) {
                    reject(error);
                } else {
                    resolve(response);
                }
            });
        });
    }
}
