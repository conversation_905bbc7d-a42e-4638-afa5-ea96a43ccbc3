import _ from 'lodash';

const normalizeStrategy = (_settings = [], ..._strategies) =>
    typeof _settings === 'string' ? {strategies: _.flatten([_settings, ..._strategies])} : _settings;

export default function (...strategies) {
    const settings = normalizeStrategy(...strategies);

    return async function (request, response, next) {
        const {app} = request;
        const service = app.defaultAuthentication ? app.defaultAuthentication(settings.service) : null;

        if (service === null) {
            return next();
        }

        const {authStrategies = []} = service.configuration;

        if (authStrategies.length === 0) {
            return next();
        }

        request.feathers = {
            provider: 'rest',
            headers: request.headers
        };

        let authentication = null;
        try {
            authentication = await service.parse(request, response, ...authStrategies);
        } catch (error) {
            return next(error);
        }

        if (authentication) {
            _.merge(request, {
                authentication,
                feathers: {authentication}
            });
        }

        try {
            const authResult = await service.authenticate(authentication, request.feathers, ...settings.strategies);

            _.merge(request, authResult);
        } catch (error) {
            return next(error);
        }

        next();
    };
}
