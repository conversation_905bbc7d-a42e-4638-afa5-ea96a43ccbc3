import path from 'path';
import fs from 'fs';
import os from 'os';
import {exec} from 'child_process';
import Big from 'big.js';

function round(number, precision = 2) {
    return Number(Big(number).round(precision));
}

function sanitizeLinuxCpuInfo(value) {
    if (value && value.includes('%')) {
        return value.split('%')[0];
    }

    return value;
}

function cpuStats() {
    return new Promise((resolve, reject) => {
        if (process.platform === 'linux') {
            const top = 'top -bn3 | grep Cpu';

            exec(top, {stdio: ['inherit', 'pipe', 'pipe']}, (error, output) => {
                if (!!error) return reject(error);

                const lines = output.split('\n');
                for (let line of lines) {
                    if (line.includes('Cpu')) {
                        line = line.replace(/\s+/g, ' ');

                        const segments = line.split(',');
                        const user = parseFloat(sanitizeLinuxCpuInfo(segments[0].split(' ')[1]));
                        const sys = parseFloat(sanitizeLinuxCpuInfo(segments[1].split(' ')[1]));

                        resolve({usage: round(user + sys)});

                        break;
                    }
                }
            });
        } else if (process.platform === 'darwin') {
            const top = 'top -l1 | grep "CPU usage"';

            exec(top, {stdio: ['inherit', 'pipe', 'pipe']}, (error, output) => {
                if (!!error) return reject(error);

                const segments = output.split(',');
                const user = Number(parseFloat(segments[0].split(' ')[2].split('%')[0]).toFixed(2));
                const sys = Number(parseFloat(segments[1].split(' ')[1].split('%')[0]).toFixed(2));

                resolve({usage: round(user + sys)});
            });
        } else if (process.platform === 'win32') {
            const wmic = 'wmic cpu get loadpercentage /value | find "Load"';

            exec(wmic, (error, output) => {
                if (!!error) return reject(error);

                resolve({
                    usage: round(parseFloat(output.split('=')[1].split('\r\r\n')[0]))
                });
            });
        } else {
            reject(new Error('Un-supported platform!'));
        }
    });
}

function memoryStats() {
    const stats = {};

    stats.free = round(os.freemem() / Math.pow(1024, 2), 2);
    stats.total = round(os.totalmem() / Math.pow(1024, 2), 2);
    stats.used = round(stats.total - stats.free, 2);
    stats.usage = round((stats.used / stats.total) * 100, 2);

    return stats;
}

function getFirstExistingParentPath(directoryPath) {
    let parentDirectoryPath = directoryPath;
    let parentDirectoryFound = fs.existsSync(parentDirectoryPath);

    while (!parentDirectoryFound) {
        parentDirectoryPath = path.normalize(parentDirectoryPath + '/..');
        parentDirectoryFound = fs.existsSync(parentDirectoryPath);
    }

    return parentDirectoryPath;
}

function mapDiskCommandOutput(stdout, filter, mapping, coefficient = 1) {
    const parsed = stdout
        .trim()
        .split('\n')
        .slice(1)
        .map(line => {
            return line.trim().split(/\s+(?=[\d/])/);
        });

    let filtered = parsed.filter(filter);

    if (filtered.length === 0) {
        throw new Error('No match found!');
    }

    filtered = filtered[0];

    return {
        diskPath: filtered[mapping.diskPath],
        free: parseInt(filtered[mapping.free], 10) * coefficient,
        size: parseInt(filtered[mapping.size], 10) * coefficient
    };
}

function checkDisk(cmd, filter, mapping, coefficient = 1) {
    return new Promise((resolve, reject) => {
        exec(cmd, (error, stdout) => {
            if (error) {
                reject(error);
            }

            try {
                resolve(mapDiskCommandOutput(stdout, filter, mapping, coefficient));
            } catch (error2) {
                reject(error2);
            }
        });
    });
}

function checkWin32Disk(directoryPath) {
    if (directoryPath.charAt(1) !== ':') {
        return new Promise((resolve, reject) => {
            reject(new Error(`The following path is invalid (should be X:\\...): ${directoryPath}`));
        });
    }

    return checkDisk(
        'wmic logicaldisk get size,freespace,caption',
        driveData => {
            // Only get the drive which match the path
            const driveLetter = driveData[0];

            return directoryPath.startsWith(driveLetter);
        },
        {
            diskPath: 0,
            free: 1,
            size: 2
        }
    );
}

function checkUnixDisk(directoryPath) {
    if (!path.normalize(directoryPath).startsWith(path.sep)) {
        return new Promise((resolve, reject) => {
            reject(new Error(`The following path is invalid (should start by ${path.sep}): ${directoryPath}`));
        });
    }

    return checkDisk(
        `df -Pk "${getFirstExistingParentPath(directoryPath)}"`,
        () => true, // We should only get one line, so we did not need to filter
        {
            diskPath: 5,
            free: 3,
            size: 1
        },
        1024 // We get sizes in kB, we need to convert that to bytes
    );
}

async function diskStats() {
    const currentPath = process.cwd();
    const result = process.platform === 'win32' ? await checkWin32Disk(currentPath) : await checkUnixDisk(currentPath);
    const stats = {};

    stats.free = round(result.free / Math.pow(1024, 3), 2);
    stats.total = round(result.size / Math.pow(1024, 3), 2);
    stats.used = round(stats.total - stats.free, 2);
    stats.usage = round((stats.used / stats.total) * 100, 2);

    return stats;
}

export default async function () {
    const cpu = await cpuStats();
    const memory = memoryStats();
    const disk = await diskStats();

    return {
        cpu,
        memory,
        disk
    };
}
