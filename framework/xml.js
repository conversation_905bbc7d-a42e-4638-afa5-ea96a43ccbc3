import {parseString, Builder} from 'xml2js';

export default class XML {
    static parse(xml) {
        return new Promise((resolve, reject) => {
            parseString(xml, (error, result) => {
                if (error) {
                    reject(error);
                } else {
                    resolve(result);
                }
            });
        });
    }

    static build(object) {
        return new Builder().buildObject(object);
    }
}
