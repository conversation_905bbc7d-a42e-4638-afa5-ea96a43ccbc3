import _ from 'lodash';
import * as yup from 'yup';
import {regexp, traverse} from '../helpers';

const normalizedSchemasMap = {};

export const clean = async (app, schema, data, options = {}) => {
    options.requiredByDefault = !(!!options.isModifier || !!options.modifier);
    if (!!options.isModifier || !!options.modifier) {
        options.stripUnknown = false;
    }
    if (!options.locale) {
        if (app.get('arch') === 'backend') {
            options.locale = app.config('app.locale');
        } else {
            options.locale = app.get('locale');
        }
    }
    options.forCleaning = true;

    let normalizedSchema = null;
    if (typeof options.schemaKey === 'string' && !!options.schemaKey) {
        const schemaKey = `${JSON.stringify(options)}-${options.schemaKey}`;

        normalizedSchema = normalizedSchemasMap[schemaKey];

        if (!normalizedSchema) {
            normalizedSchema = normalizedSchemasMap[schemaKey] = buildSchema(app, schema, options);
        }
    } else {
        normalizedSchema = buildSchema(app, schema, options);
    }

    try {
        data = normalizedSchema.cast(data, {
            stripUnknown: options.stripUnknown !== false
        });
    } catch (error) {
        console.log(error.message);
    }

    return data;
};

export const validate = async (app, schema, data, options = {}) => {
    options.requiredByDefault = !(!!options.isModifier || !!options.modifier);
    if (!!options.isModifier || !!options.modifier) {
        options.stripUnknown = false;
    }
    if (!options.locale) {
        if (app.get('arch') === 'backend') {
            options.locale = app.config('app.locale');
        } else {
            options.locale = app.get('locale');
        }
    }

    const t = (msg, locale = options.locale) => app.translate(msg, locale);
    let isValid = false;
    let errors = [];

    yup.setLocale({
        mixed: {
            default: t('${path} is invalid!'),
            required: t('${path} is a required field!'),
            oneOf: t('${path} is invalid!'),
            notOneOf: t('${path} is invalid!')
        },
        string: {
            length: t('${path} must be exactly ${length} characters!'),
            min: t('${path} must be at least ${min} characters!'),
            max: t('${path} must be at most ${max} characters!'),
            matches: t('${path} is invalid!'),
            email: t('${path} must be a valid email!')
        },
        number: {
            min: t('${path} must be greater than or equal to ${min}!'),
            max: t('${path} must be less than or equal to ${max}!'),
            lessThan: t('${path} must be less than ${less}!'),
            moreThan: t('${path} must be greater than ${more}!'),
            positive: t('${path} must be a positive number!'),
            negative: t('${path} must be a negative number!'),
            integer: t('${path} must be an integer!')
        },
        date: {
            min: t('${path} field must be later than ${min}!'),
            max: t('${path} field must be at earlier than ${max}!')
        },
        array: {
            min: t('${path} must have at least ${min} item(s)!'),
            max: t('${path} must have at most ${max} item(s)!')
        }
    });

    let normalizedSchema = null;
    if (typeof options.schemaKey === 'string' && !!options.schemaKey) {
        const schemaKey = `${JSON.stringify(options)}-${options.schemaKey}`;

        normalizedSchema = normalizedSchemasMap[schemaKey];

        if (!normalizedSchema) {
            normalizedSchema = normalizedSchemasMap[schemaKey] = buildSchema(app, schema, options);
        }
    } else {
        normalizedSchema = buildSchema(app, schema, options);
    }

    try {
        if (!!options.clean) {
            try {
                data = await clean(app, schema, data, options);
            } catch (e) {
                console.log(e.message);
            }
        }

        await normalizedSchema.validate(
            traverse(data).map(function (x) {
                if ((typeof x === 'string' && x.trim() === '') || typeof x === 'undefined' || x === null) {
                    this.remove();
                }
            }),
            {
                abortEarly: false,
                strict: true,
                stripUnknown: false
            }
        );

        isValid = true;
    } catch (error) {
        for (const item of error.inner) {
            errors.push({
                field: item.path,
                message: item.errors[0]
            });
        }

        isValid = false;
    }

    return {isValid, data, errors};
};

class BlackboxSchema extends yup.BaseSchema {
    static create() {
        return new BlackboxSchema();
    }

    constructor() {
        super({
            spec: {
                strip: false,
                strict: false,
                abortEarly: false,
                recursive: false,
                nullable: true,
                presence: 'optional'
            }
        });
    }

    _typeCheck(value) {
        return true;
    }
}

yup.addMethod(yup.string, 'stringify', function () {
    return this.transform(function (value) {
        if (typeof value !== 'string') {
            if (value === null || value === undefined) {
                return '';
            }

            return value;
        }

        return value.trim();
    });
});

const KNOWN_TYPES = ['all', 'object', 'string', 'boolean', 'integer', 'decimal', 'number', 'date', 'datetime', 'time'];

function buildSchema(app, schema, options = {}) {
    const t = (msg, locale = options.locale) => app.translate(msg, locale);
    const shape = {};

    for (const key of Object.keys(schema)) {
        let definition = schema[key];
        let s = yup;

        if (!definition) {
            continue;
        }

        if (typeof definition === 'string') {
            shape[key] = prepareDefinition(app, s, t, options, {type: definition});
        } else if (Array.isArray(definition) && definition.length > 0) {
            let subDefinition = definition[0];

            if (typeof subDefinition === 'string') {
                shape[key] = s
                    .array()
                    .of(prepareDefinition(app, s, t, options, {type: subDefinition}))
                    .default(() => []);
            } else if (typeof subDefinition === 'object' && subDefinition !== null) {
                subDefinition = _.omit(subDefinition, [
                    'form',
                    'column',
                    'editor',
                    'index',
                    'unique',
                    'translate',
                    'text',
                    'richContent'
                ]);

                if (KNOWN_TYPES.includes(subDefinition.type)) {
                    shape[key] = s
                        .array()
                        .of(prepareDefinition(app, s, t, options, subDefinition))
                        .default(() => []);
                } else if (Object.keys(subDefinition).length > 0) {
                    shape[key] = s
                        .array()
                        .of(buildSchema(app, subDefinition, options))
                        .default(() => []);
                }
            }
        } else if (typeof definition === 'object' && definition !== null) {
            definition = _.omit(definition, ['form', 'column', 'editor', 'index', 'unique', 'translate', 'text']);

            if (KNOWN_TYPES.includes(definition.type)) {
                shape[key] = prepareDefinition(app, s, t, options, definition);
            } else if (Array.isArray(definition.type) && definition.type.length > 0) {
                let subDefinition = definition.type[0];

                if (typeof subDefinition === 'string') {
                    shape[key] = s
                        .array()
                        .of(prepareDefinition(app, s, t, options, {type: subDefinition}))
                        .default(() => []);
                } else if (typeof subDefinition === 'object' && subDefinition !== null) {
                    subDefinition = _.omit(subDefinition, [
                        'form',
                        'column',
                        'editor',
                        'index',
                        'unique',
                        'translate',
                        'text',
                        'richContent'
                    ]);

                    if (KNOWN_TYPES.includes(subDefinition.type)) {
                        shape[key] = s
                            .array()
                            .of(prepareDefinition(app, s, t, options, subDefinition))
                            .default(() => []);
                    } else if (Object.keys(subDefinition).length > 0) {
                        shape[key] = s
                            .array()
                            .of(buildSchema(app, subDefinition, options))
                            .default(() => []);
                    }
                }

                if (typeof definition.label === 'string') {
                    shape[key] = shape[key].label(app.translate(definition.label));
                }
                if (typeof definition.min === 'number') {
                    shape[key] = shape[key].min(definition.min);
                }
                if (typeof definition.max === 'number') {
                    shape[key] = shape[key].max(definition.max);
                }
            } else if (Object.keys(definition).length > 0) {
                shape[key] = buildSchema(app, definition, options);
            }
        }

        if (!shape[key]) {
            throw new Error('Invalid schema definition');
        }
    }

    return yup.object().shape(shape);
}

function prepareDefinition(app, s, t, options, definition) {
    // Get type.
    const type = definition.type;
    if (!type) {
        throw new Error('Invalid schema definition');
    }
    if (!KNOWN_TYPES.includes(type)) {
        throw new Error(`Unknown type ${type}`);
    }

    if (type === 'object') {
        s = BlackboxSchema.create();
    } else if (type === 'boolean') {
        s = s.boolean().transform(function (value, originalValue) {
            // Check to see if the previous transform already parsed the boolean
            if (this.isType(value)) return value;

            if (typeof this.spec.default === 'function') {
                return this.spec.default();
            }

            if (typeof originalValue === 'string' && originalValue.trim() === '') {
                return null;
            }

            return value;
        });
    } else if (type === 'string') {
        s = s.string().stringify();

        if (typeof definition.regexp !== 'undefined') {
            if (typeof definition.regexp === 'string') {
                if (typeof regexp[definition.regexp] !== 'undefined') {
                    s = s.matches(regexp[definition.regexp], {
                        excludeEmptyString: true,
                        ...(definition.regexp === 'Email' || definition.regexp === 'EmailWithTLD'
                            ? {message: t('Invalid email address')}
                            : {})
                    });
                } else {
                    throw new Error(`Given regExp pattern is not found!`);
                }
            } else {
                s = s.matches(definition.regexp, {excludeEmptyString: true});
            }
        }
    } else if (type === 'integer') {
        s = s
            .number()
            .integer()
            .transform(function (value, originalValue) {
                // Check to see if the previous transform already parsed the integer
                if (this.isType(value)) return value;

                if (typeof this.spec.default === 'function') {
                    return this.spec.default();
                }

                if (typeof originalValue === 'string' && originalValue.trim() === '') {
                    return null;
                }

                return value;
            });
    } else if (type === 'decimal' || type === 'number') {
        s = s.number().transform(function (value, originalValue) {
            // Check to see if the previous transform already parsed the number
            if (this.isType(value)) return value;

            if (typeof this.spec.default === 'function') {
                return this.spec.default();
            }

            if (typeof originalValue === 'string' && originalValue.trim() === '') {
                return null;
            }

            return value;
        });
    } else if (type === 'date' || type === 'datetime' || type === 'time') {
        s = s.date().transform(function (value, originalValue) {
            // Check to see if the previous transform already parsed the date
            if (this.isType(value)) return value;

            if (typeof this.spec.default === 'function') {
                return this.spec.default();
            }

            if (typeof originalValue === 'string' && originalValue.trim() === '') {
                return null;
            }

            return value;
        });
    } else if (type === 'all') {
        s = BlackboxSchema.create();
    }

    if (typeof definition.label === 'string') {
        s = s.label(t(definition.label));
    }

    if (typeof definition.default === 'undefined') {
        if (options.requiredByDefault) {
            if (definition.required === false) {
                s = s.nullable().notRequired();
            } else {
                s = s.required();
            }
        } else {
            s = s.notRequired();
        }
    } else {
        s = s.notRequired();
    }

    if (typeof definition.default !== 'undefined') {
        if ((type === 'date' || type === 'datetime' || type === 'time') && definition.default === 'date:now') {
            if (type === 'datetime' || type === 'time') {
                s = s.default(() => app.datetime.local().toJSDate());
            } else {
                s = s.default(() => app.datetime.local().startOf('day').toJSDate());
            }
        } else {
            s = s.default(() => definition.default);
        }
    }

    if (Array.isArray(definition.allowed)) {
        s = s.oneOf(definition.allowed);
    }

    if (typeof definition.min === 'number') {
        s = s.min(definition.min);
    }
    if (typeof definition.max === 'number') {
        s = s.max(definition.max);
    }
    if (typeof definition.exclusiveMin === 'number') {
        s = s.min(definition.exclusiveMin + 1);
    }
    if (typeof definition.exclusiveMax === 'number') {
        s = s.max(definition.exclusiveMax - 1);
    }

    return s;
}
