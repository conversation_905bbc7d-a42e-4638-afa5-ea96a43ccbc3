import _ from 'lodash';

let store = {};

export default {
    get(key) {
        if (!this.has(key)) {
            throw new Error(`Couldn't find the configuration with the given key: "${key}"`);
        }

        return _.get(store, key);
    },

    set(key, value) {
        _.set(store, key, value);
    },

    has(key) {
        return _.has(store, key);
    },

    setStore(s) {
        store = s;
    }
};
