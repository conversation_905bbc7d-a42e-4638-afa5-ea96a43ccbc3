{"useLongScale": false, "baseSeparator": " e ", "unitSeparator": "e ", "andWhenTrailing": true, "base": {"0": "zero", "1": "um", "2": "dois", "3": "três", "4": "quatro", "5": "cinco", "6": "seis", "7": "sete", "8": "oito", "9": "nove", "10": "dez", "11": "onze", "12": "doze", "13": "treze", "14": "cator<PERSON>", "15": "quinze", "16": "<PERSON><PERSON><PERSON><PERSON>", "17": "dezessete", "18": "<PERSON><PERSON><PERSON>", "19": "<PERSON><PERSON><PERSON>", "20": "vinte", "30": "trinta", "40": "quarenta", "50": "cinquenta", "60": "sessenta", "70": "<PERSON><PERSON>a", "80": "oitenta", "90": "noventa", "100": "cem", "200": "<PERSON><PERSON><PERSON>", "300": "trezentos", "400": "quatrocentos", "500": "quin<PERSON><PERSON>", "600": "seiscentos", "700": "setecentos", "800": "oitocentos", "900": "novecentos", "1000": "mil"}, "unitExceptions": {"1": "um"}, "units": [{"singular": "cento", "useBaseInstead": true, "useBaseException": [1], "useBaseExceptionWhenNoTrailingNumbers": true, "andException": true}, {"singular": "mil", "avoidPrefixException": [1], "andException": true}, {"singular": "mi<PERSON><PERSON><PERSON>", "plural": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"singular": "bilhão", "plural": "bilhõ<PERSON>"}, {"singular": "trilhão", "plural": "t<PERSON><PERSON><PERSON><PERSON>"}, {"singular": "quadrilhão", "plural": "quadrilhão"}, {"singular": "quin<PERSON><PERSON><PERSON>", "plural": "quin<PERSON><PERSON><PERSON><PERSON>"}, {"singular": "sextilhão", "plural": "sextilh<PERSON><PERSON>"}, {"singular": "septil<PERSON><PERSON>", "plural": "septil<PERSON><PERSON><PERSON>"}, {"singular": "octilhão", "plural": "octilhõ<PERSON>"}, {"singular": "nonilhão", "plural": "<PERSON>il<PERSON><PERSON><PERSON>"}, {"singular": "decilhão", "plural": "<PERSON>cil<PERSON><PERSON><PERSON>"}, {"singular": "undecilhão", "plural": "undecilhõ<PERSON>"}, {"singular": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "plural": "<PERSON><PERSON><PERSON>l<PERSON>õ<PERSON>"}, {"singular": "tredecilhão", "plural": "tredecilhõ<PERSON>"}]}