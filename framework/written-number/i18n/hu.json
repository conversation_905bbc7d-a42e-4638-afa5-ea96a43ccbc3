{"useLongScale": true, "baseSeparator": "", "unitSeparator": "és ", "base": {"0": "nulla", "1": "egy", "2": "kettő", "3": "<PERSON><PERSON><PERSON>", "4": "négy", "5": "öt", "6": "hat", "7": "<PERSON><PERSON><PERSON>", "8": "nyolc", "9": "kilenc", "10": "tíz", "11": "tizenegy", "12": "tizenkettő", "13": "t<PERSON><PERSON><PERSON><PERSON>", "14": "tizennégy", "15": "<PERSON>ize<PERSON><PERSON><PERSON>", "16": "tizenhat", "17": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "18": "tizennyolc", "19": "tizenkilenc", "20": "<PERSON><PERSON><PERSON>", "21": "hus<PERSON><PERSON>", "22": "huszonkettő", "23": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "24": "huszonnégy", "25": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "26": "<PERSON><PERSON><PERSON><PERSON>", "27": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "28": "huszonnyolc", "29": "huszon<PERSON>lenc", "30": "harminc", "40": "negyven", "50": "ötven", "60": "<PERSON><PERSON>", "70": "hetven", "80": "nyolcvan", "90": "kile<PERSON><PERSON>", "100": "száz", "200": "kétszáz", "300": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "400": "négyszáz", "500": "ötszáz", "600": "hats<PERSON><PERSON><PERSON>", "700": "h<PERSON>tsz<PERSON><PERSON>", "800": "nyolcszáz", "900": "kilencsz<PERSON>z", "1000": "ezer"}, "unitExceptions": {"1": "egy"}, "units": [{"singular": "száz", "useBaseInstead": true, "useBaseException": [1]}, {"singular": "ezer", "avoidPrefixException": [1]}, {"singular": "milli<PERSON>", "avoidPrefixException": [1]}, {"singular": "<PERSON><PERSON><PERSON><PERSON>", "avoidPrefixException": [1]}, {"singular": "-<PERSON><PERSON><PERSON>", "avoidPrefixException": [1]}, {"singular": "<PERSON><PERSON><PERSON><PERSON>", "avoidPrefixException": [1]}, {"singular": "trilli<PERSON>", "avoidPrefixException": [1]}, {"singular": "trill<PERSON><PERSON><PERSON>", "avoidPrefixException": [1]}, {"singular": "kvadrillió", "avoidPrefixException": [1]}, {"singular": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avoidPrefixException": [1]}, {"singular": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avoidPrefixException": [1]}, {"singular": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avoidPrefixException": [1]}, {"singular": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avoidPrefixException": [1]}, {"singular": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "avoidPrefixException": [1]}, {"singular": "<PERSON><PERSON><PERSON><PERSON>", "avoidPrefixException": [1]}, {"singular": "<PERSON><PERSON><PERSON><PERSON>", "avoidPrefixException": [1]}]}