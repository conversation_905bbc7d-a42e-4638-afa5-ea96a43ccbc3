{"useLongScale": true, "baseSeparator": " e ", "unitSeparator": "e ", "andWhenTrailing": true, "base": {"0": "zero", "1": "um", "2": "dois", "3": "três", "4": "quatro", "5": "cinco", "6": "seis", "7": "sete", "8": "oito", "9": "nove", "10": "dez", "11": "onze", "12": "doze", "13": "treze", "14": "cator<PERSON>", "15": "quinze", "16": "<PERSON><PERSON><PERSON><PERSON>", "17": "dezassete", "18": "<PERSON><PERSON><PERSON>", "19": "<PERSON><PERSON><PERSON>", "20": "vinte", "30": "trinta", "40": "quarenta", "50": "cinquenta", "60": "sessenta", "70": "<PERSON><PERSON>a", "80": "oitenta", "90": "noventa", "100": "cem", "200": "<PERSON><PERSON><PERSON>", "300": "trezentos", "400": "quatrocentos", "500": "quin<PERSON><PERSON>", "600": "seiscentos", "700": "setecentos", "800": "oitocentos", "900": "novecentos", "1000": "mil"}, "unitExceptions": {"1": "um"}, "units": [{"singular": "cento", "useBaseInstead": true, "useBaseException": [1], "useBaseExceptionWhenNoTrailingNumbers": true, "andException": true}, {"singular": "mil", "avoidPrefixException": [1], "andException": true}, {"singular": "mi<PERSON><PERSON><PERSON>", "plural": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"singular": "bilião", "plural": "biliõ<PERSON>"}, {"singular": "trilião", "plural": "triliõ<PERSON>"}, {"singular": "quadrilião", "plural": "quadriliões"}, {"singular": "quintil<PERSON><PERSON>", "plural": "quintil<PERSON><PERSON><PERSON>"}, {"singular": "<PERSON><PERSON><PERSON><PERSON>", "plural": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"singular": "septili<PERSON>", "plural": "sept<PERSON><PERSON><PERSON>"}, {"singular": "octilião", "plural": "octiliõ<PERSON>"}, {"singular": "nonilião", "plural": "<PERSON>ili<PERSON><PERSON>"}, {"singular": "decilião", "plural": "deciliões"}]}