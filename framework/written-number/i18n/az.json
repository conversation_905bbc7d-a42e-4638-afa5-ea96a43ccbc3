{"useLongScale": false, "baseSeparator": " ", "unitSeparator": "", "base": {"0": "sı<PERSON><PERSON>r", "1": "bir", "2": "iki", "3": "üç", "4": "<PERSON><PERSON><PERSON>", "5": "beş", "6": "altı", "7": "yeddi", "8": "səkkiz", "9": "<PERSON><PERSON><PERSON><PERSON>", "10": "on", "20": "<PERSON><PERSON><PERSON><PERSON>", "30": "otuz", "40": "qırx", "50": "əlli", "60": "altmış", "70": "<PERSON><PERSON><PERSON>", "80": "səksən", "90": "do<PERSON><PERSON>"}, "units": [{"singular": "yüz", "avoidPrefixException": [1]}, {"singular": "min", "avoidPrefixException": [1]}, "milyon", "milyard", "t<PERSON>on", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kent<PERSON>on", "sekstilyon", "septilyon", "<PERSON><PERSON><PERSON>", "nonilyon", "<PERSON><PERSON><PERSON>", "and<PERSON><PERSON><PERSON>", "dodes<PERSON>on", "tredesilyon", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kend<PERSON><PERSON><PERSON>"], "unitExceptions": []}