{"useLongScale": false, "baseSeparator": " ", "unitSeparator": "", "base": {"0": "nulo", "1": "unu", "2": "du", "3": "tri", "4": "kvar", "5": "kvin", "6": "ses", "7": "sep", "8": "ok", "9": "naŭ", "10": "dek", "20": "dudek", "30": "tridek", "40": "<PERSON><PERSON><PERSON>", "50": "<PERSON><PERSON><PERSON>", "60": "<PERSON><PERSON><PERSON>", "70": "sep<PERSON>", "80": "<PERSON><PERSON>", "90": "<PERSON><PERSON><PERSON>", "100": "cent", "200": "ducent", "300": "tricent", "400": "<PERSON><PERSON><PERSON>", "500": "kvin<PERSON>", "600": "sescent", "700": "sepcent", "800": "okcent", "900": "naŭcent"}, "units": [{"useBaseInstead": true, "useBaseException": []}, {"singular": "mil", "avoidPrefixException": [1]}, {"singular": "mi<PERSON>o", "plural": "<PERSON><PERSON><PERSON><PERSON>", "avoidPrefixException": [1]}, {"singular": "milia<PERSON>", "plural": "<PERSON><PERSON><PERSON><PERSON>", "avoidPrefixException": [1]}, {"singular": "bi<PERSON>o", "plural": "<PERSON><PERSON><PERSON><PERSON>", "avoidPrefixException": [1]}], "unitExceptions": []}