{"useLongScale": false, "baseSeparator": " ", "unitSeparator": "", "base": {"0": "sı<PERSON><PERSON>r", "1": "bir", "2": "iki", "3": "üç", "4": "<PERSON><PERSON><PERSON>", "5": "beş", "6": "altı", "7": "yedi", "8": "sekiz", "9": "dokuz", "10": "on", "20": "yirmi", "30": "otuz", "40": "kırk", "50": "elli", "60": "altmış", "70": "<PERSON><PERSON><PERSON>", "80": "<PERSON><PERSON><PERSON>", "90": "<PERSON><PERSON><PERSON>"}, "units": [{"singular": "yüz", "avoidPrefixException": [1]}, {"singular": "bin", "avoidPrefixException": [1]}, "milyon", "<PERSON>lyar", "t<PERSON>on", "<PERSON><PERSON><PERSON><PERSON>", "kent<PERSON>on", "sekstilyon", "septilyon", "<PERSON><PERSON><PERSON>", "nonilyon", "<PERSON><PERSON><PERSON>", "and<PERSON><PERSON><PERSON>", "dodes<PERSON>on", "tredesilyon", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kend<PERSON><PERSON><PERSON>"], "unitExceptions": []}