/**
 * Merges a set of default keys with a target object
 * (Like _.defaults, but will also extend onto null/undefined)
 *
 * @param {Object} [target] The object to extend
 * @param {Object} defaults The object to default to
 * @return {Object} extendedTarget
 */

function defaults(target, defs) {
    if (target == null) target = {};
    let ret = {};
    let keys = Object.keys(defs);
    for (let i = 0, len = keys.length; i < len; i++) {
        let key = keys[i];
        ret[key] = target[key] || defs[key];
    }
    return ret;
}

exports.defaults = defaults;
