import bark from './bark';

export default function parse(code) {
    const result = {
        format: 'CODE128',
        barcode: code,
        originalBarcode: code
    };

    if (code.length === 8) result.format = 'EA8';
    else if (code.length === 12) result.format = 'UPC';
    else if (code.length === 14) result.format = 'ITF14';

    const parsed = bark(code);

    if (parsed.elements.length > 0) {
        for (const element of parsed.elements) {
            if (element.title === 'GTIN') {
                result.barcode = element.value;

                if (result.barcode.length === 8) result.format = 'EA8';
                else if (result.barcode.length === 12) result.format = 'UPC';
                else if (result.barcode.length === 14) result.format = 'ITF14';
            } else if (element.title === 'SERIAL') {
                result.serialNumber = element.value;
            } else if (element.title === 'BATCH/LOT') {
                result.lotNumber = element.value;
            } else if (element.title === 'USE BY OR EXPIRY') {
                result.expirationDate = new Date(Date.parse(element.value));
            }
        }
    } else {
        result.barcode = code;
    }

    return result;
}
