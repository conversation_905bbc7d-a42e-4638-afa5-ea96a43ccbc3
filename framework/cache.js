import _ from 'lodash';
import Redis from 'ioredis';
import notepack from 'notepack.io';

export default class Cache {
    constructor(options = {}) {
        const {port, host, client, password, database, prefix} = options;

        if (!port && !host) {
            this.client = new Redis();
        } else {
            const opts = Object.assign({}, options, {prefix: null});

            this.client = new Redis(port, host, opts);
        }

        if (password) {
            this.client.auth(password, error => {
                if (error) throw error;
            });
        }

        if (database) {
            this.client.select(database, error => {
                if (error) throw error;
            });
        }

        this.prefix = prefix || 'entererp-cache:';
    }

    async get(key, defaultValue = null) {
        key = `${this.prefix}${key}`;

        try {
            const data = await this.client.getBuffer(key);

            if (_.isNull(data) || _.isUndefined(data)) return defaultValue;

            return notepack.decode(data);
        } catch (error) {
            return null;
        }
    }

    async has(key) {
        key = `${this.prefix}${key}`;

        return (await this.client.exists(key)) === 1;
    }

    async set(key, value, ttl = false) {
        key = `${this.prefix}${key}`;
        value = notepack.encode(value);

        if (_.isNumber(ttl)) {
            await this.client.set(key, value, 'ex', ttl);
        } else {
            await this.client.set(key, value);
        }
    }

    delete(key) {
        key = `${this.prefix}${key}`;

        return this.client.del(key);
    }

    clear(pattern = '') {
        return this._deleteByPattern(`${this.prefix}${pattern}*`);
    }

    clearAll() {
        return this.client.flushall();
    }

    _deleteByPattern(pattern) {
        return new Promise((resolve, reject) => {
            const stream = this.client.scanStream({
                match: pattern,
                count: 100
            });
            const keys = [];

            stream.on('data', resultKeys => {
                for (let i = 0; i < resultKeys.length; i++) {
                    keys.push(resultKeys[i]);
                }
            });

            stream.on('end', async () => {
                if (keys.length > 0) {
                    try {
                        await this.client.unlink(keys);

                        resolve();
                    } catch (error) {
                        reject(error);
                    }
                } else {
                    resolve();
                }
            });
        });
    }
}
