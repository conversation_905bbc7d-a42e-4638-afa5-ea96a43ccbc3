// noinspection NonAsciiCharacters,JSDuplicatedDeclaration,EqualityComparisonWithCoercionJS,JSUnusedLocalSymbols,JSNonASCIINames

const caseObj = {
    أ: 'a',
    ب: 'b',
    ج: 'j',
    د: 'd',
    ه: 'h',
    و: 'o',
    ز: 'z',
    ح: 'h',
    ط: 't',
    ي: 'i',
    ك: 'k',
    ل: 'l',
    م: 'm',
    ن: 'n',
    س: 's',
    ع: 'a',
    ف: 'f',
    ص: 's',
    ق: 'q',
    ر: 'r',
    ش: 'sh',
    ت: 't',
    ث: 'th',
    خ: 'kh',
    ذ: 'th',
    ض: 'dh',
    ظ: 'z',
    غ: 'gh',
    ا: 'a',
    ئ: 'a',
    ى: 'a',
    ؤ: 'u',
    ء: 'a',
    ة: 'h',
    إ: 'i',
    آ: 'a',
    ' ': ' ',
    'َ': '',
    'ً': '',
    'ُ': '',
    'ِ': '',
    'ٍ': '',
    'ْ': '',
    'ِ': '',
    'ٌ': '',
    'ّ': '',
    ی: 'a'
};
const nextObj = {
    م: {
        ح: 'u',
        ث: 'u',
        ص: 'u',
        خ: 'u',
        ر: 'a',
        ه: 'u',
        ق: 'u',
        ع: 'u',
        ف: 'u',
        ن: 'u',
        ك: 'a',
        ي: 'a'
    },
    س: {
        ه: 'u',
        ن: 'a',
        و: 'a',
        ي: 'a',
        ل: 'a',
        ع: 'a',
        د: 'a',
        م: 'a',
        ح: 'a'
    },
    ن: {
        ب: 'a',
        د: 'a',
        ج: 'a',
        ص: 'a',
        ز: 'a',
        ش: 'a',
        ظ: 'i',
        س: 'i',
        ه: 'u'
    },
    ح: {
        س: 'u',
        ي: 'a',
        م: 'a',
        ق: 'a',
        ز: 'u',
        ن: 'a',
        ب: 'a',
        ج: 'a',
        ر: 'a'
    },
    ر: {
        ح: 'a',
        ف: 'a',
        ب: 'a',
        ض: 'a'
    },
    ت: {
        ق: 'u',
        ن: 'i'
    },
    ز: {
        ه: 'u',
        ي: 'a',
        ن: 'i'
    },
    غ: {
        ف: 'u',
        م: 'a'
    },
    ك: {
        م: 'a',
        ر: 'a',
        ل: 'a'
    },
    د: {
        ع: 'u'
    },
    ض: {
        ح: 'u',
        ر: 'u'
    },
    ق: {
        ي: 'a',
        ص: 'u',
        ر: 'u',
        ت: 'u',
        د: 'a',
        ح: 'a'
    },
    ج: {
        د: 'a',
        ب: 'a',
        ه: 'a'
    },
    ب: {
        د: 'a'
    },
    ؤ: {
        ي: 'a'
    },
    ط: {
        ن: 'a',
        ي: 'a'
    },
    خ: {
        ل: 'a',
        ت: 'i'
    },
    ي: {
        م: 'a',
        ز: 'a'
    },
    و: {
        ر: 'a'
        // ر: "",
    },
    ه: {
        د: 'a'
    },
    ش: {
        ه: 'a'
    },
    ص: {
        د: 'a',
        ر: 'a',
        ف: 'a',
        ك: 'a',
        ن: 'a',
        ه: 'u'
    },
    ف: {
        ذ: 'a',
        ن: 'a',
        ر: 'a',
        ض: 'a',
        ج: 'a',
        ك: 'i',
        ق: 'a',
        خ: 'a',
        ز: 'a'
    },

    ل: {
        م: 'a',
        ي: 'a',
        ق: 'i'
    }
};
const firstObj = {
    و: 'w',
    ي: 'y'
};
const specialObj = {
    و: {
        ا: 'w',
        ج: 'w',
        action: 'slice'
    },
    م: {
        ع: 'u',
        action: ''
    },
    ئ: {
        ل: 'e',
        action: 'slice'
    },
    ا: {
        ش: 'I',
        action: 'slice'
    }
};
const lastObj = {
    ه: {action: 'slice', ه: 'ah'},
    ة: {action: 'slice', ة: 'ah'}
    //   ي: { action: "slice", ي: "y" },
    //   ح: { action: "slice", ح: "ah" },
};
const threeObj = {
    م: {
        ح: {
            م: 'u',
            ا: 'a',
            أ: 'a',
            ب: 'a',
            ف: 'a',
            ج: 'a'
        },
        ي: {
            ر: 'e',
            ث: 'e'
        },
        ج: {
            ي: 'a'
        },
        ن: {
            ي: 'a'
        }
    },
    و: {
        س: {
            ف: 'u'
        }
    },
    س: {
        ل: {
            ي: 'u'
        },
        م: {
            ا: 'a'
        },
        ف: {
            ي: 'u'
        },
        ق: {
            ي: 'u'
        },
        و: {
            ا: 'a'
        },
        ي: {
            ا: 'a'
        },
        م: {
            ر: 'a',
            ا: 'a'
        },
        ن: {
            ا: 'a',
            د: 'a'
        },
        ر: {
            ى: 'u'
        }
    },
    ع: {
        م: {
            ا: 'm'
        },
        د: {
            ي: 'u'
        },
        ز: {
            ت: 'i'
        },
        ط: {
            ا: 'a',
            ب: 'a',
            س: 'a',
            ي: 'a',
            ز: 'a',
            ر: 'i',
            ق: 'a',
            ش: 'a',
            ف: 'a',
            ا: 'a'
        }
    },
    ب: {
        ر: {
            ا: 'a'
        },
        د: {
            ر: 'a'
        },
        ل: {
            ا: 'i'
        }
    },
    خ: {
        د: {
            ر: 'a'
        },
        ض: {
            ر: 'i'
        },
        ط: {
            ا: 'a'
        },
        ظ: {
            ا: 'a'
        },
        م: {
            ا: 'a',
            ي: 'a'
        },
        ي: {
            ر: 'a'
        }
    },
    ح: {
        ظ: {
            ي: 'a'
        },
        ك: {
            ي: 'a'
        },
        ل: {
            و: 'i',
            ي: 'a'
        },
        س: {
            ن: 'a'
        }
    },
    ش: {
        م: {
            س: 'a'
        },
        ر: {
            و: 'u'
        },
        ه: {
            ر: 'u'
        },
        ي: {
            ت: 'e'
        },
        ج: {
            ر: 'a'
        }
    },
    د: {
        ر: {
            و: 'a'
        }
    },
    ز: {
        م: {
            ن: 'a',
            ا: 'a'
        },
        ه: {
            ي: 'u',
            ر: 'u'
        },
        ي: {
            د: 'a',
            ن: 'a'
        }
    },
    ر: {
        ق: {
            ي: 'u'
        },
        و: {
            ا: 'a'
        },
        ي: {
            ا: 'a'
        }
    },
    ل: {
        م: {
            ى: 'u'
        }
    },
    ن: {
        و: {
            ر: ''
        }
    },
    ه: {
        ي: {
            ف: 'a',
            ل: 'a',
            م: 'a'
        }
    },
    ص: {
        ب: {
            ا: 'a',
            ر: 'a'
        }
    },
    ط: {
        ل: {
            ا: 'a'
        }
    }
};
const middleObj = {
    ب: {
        ك: {
            ر: 'a'
        },
        ع: {
            ر: 'a'
        },
        خ: {
            ر: 'a'
        },
        ص: {
            ا: 'a'
        }
    },

    ز: {
        ا: {
            ل: 'a'
        },
        ع: {
            ت: 'a'
        }
    },
    ي: {
        د: {
            ن: 'e'
        },
        ش: {
            ت: 'e',
            ن: 'e',
            م: 'e',
            ح: 'e'
        },
        ت: {
            ش: 'e',
            ن: 'e',
            م: 'e',
            ح: 'e'
        }
    },
    ه: {
        ن: {
            ر: 'u'
        }
    },

    ر: {
        ج: {
            ح: 'a'
        },
        ح: {
            ج: 'a'
        }
    },
    م: {
        ح: {
            د: 'a'
        }
    },
    ي: {
        ن: {
            ن: 'e'
        }
    },

    د: {
        خ: {
            ر: 'a'
        },
        ا: {
            ق: 'i'
        },
        ع: {
            ي: 'a'
        },
        ا: {
            م: 'a'
        },
        أ: {
            م: 'a'
        },
        آ: {
            م: 'a'
        }
    },

    ض: {
        خ: {
            ر: 'i'
        }
    },
    ف: {
        ر: {
            ل: 'a'
        },
        ا: {
            ي: 'i'
        }
    },
    ب: {
        ز: {
            ا: 'a'
        }
    },

    م: {
        ز: {
            ن: 'a'
        }
    },
    ن: {
        ا: {
            س: 'a',
            ي: 'e'
        },
        أ: {
            س: 'a',
            ي: 'e'
        },
        إ: {
            س: 'e',
            ي: 'e'
        },
        س: {
            د: 'a'
        }
    },
    ح: {
        ا: {
            ب: 'i'
        },
        س: {
            ر: 'a'
        }
    },

    ل: {
        ع: {
            ا: 'a',
            ك: 'a',
            م: 'a',
            ش: 'a',
            ق: 'a'
        }
    },

    ه: {
        ا: {
            ر: 'i'
        }
    },

    ج: {
        ع: {
            ب: 'a',
            ر: 'a'
        },
        ن: {
            ب: 'a',
            ر: 'a'
        }
    },

    ي: {
        ف: {
            ف: 'e'
        }
    }
};
const namesdb = {
    زينب: 'Zainab',
    هيف: 'haif',
    احمد: 'Ahamd',
    أخصيب: 'Akhsib',
    أسماعيل: 'Isamail',
    أكبر: 'Akbr',
    أكرم: 'Akarm',
    أمجد: 'Amjad',
    أمير: 'Ameer',
    أنعام: 'Anaam',
    أياد: 'Ayad',
    أيمن: 'Ayman',
    ئازاد: 'Aazad',
    ئاودير: 'Aaodir',
    اباذر: 'Abathr',
    ابتسام: 'Abtsam',
    ابتهال: 'Ibtihal',
    ابرار: 'Abarar',
    ابراهيم: 'Ibrahem',
    ابريسم: 'Abrisam',
    ابريش: 'Abrish',
    ابو: 'Abo',
    ابوبكر: 'Abobkar',
    ابوجالي: 'Abwjalai',
    اثير: 'Atheer',
    اجبير: 'Ajabir',
    اجعاز: 'Ajaaz',
    اجود: 'Ajwad',
    احتراس: 'Ahtras',
    احد: 'Ahd',
    احسان: 'Ihusan',
    احلام: 'Ahlam',
    احمد: 'Ahmad',
    عبدالعزيز: 'Abdulaziz',
    احمظ: 'Ahamz',
    احميد: 'Ahamaid',
    اخلاص: 'Ikhalas',
    اخليف: 'Akhalaif',
    ادريس: 'Idris',
    ادم: 'Adam',
    ادمون: 'Adamon',
    ادهم: 'Adham',
    اديان: 'Adian',
    اديب: 'Adeeb',
    ارا: 'Ara',
    اراد: 'Arad',
    اراز: 'Araz',
    اراس: 'Aras',
    ارام: 'Aram',
    ارجمند: 'Arjmunad',
    ارحيل: 'Arahail',
    ارحيم: 'Arahaiam',
    ارزوقي: 'Arzoqai',
    ارزيك: 'Arzaik',
    ارشد: 'Arshd',
    اركان: 'Arkan',
    ارميض: 'Armaidh',
    اروى: 'Aroa',
    اريس: 'Aris',
    اريكنازان: 'Ariknazan',
    ازاد: 'Azad',
    ازغير: 'Azghir',
    ازل: 'Azal',
    ازهر: 'Azuhr',
    اسامة: 'Osamah',
    استبرق: 'Istbrq',
    اسحاق: 'Isahaq',
    اسد: 'Asad',
    اسدي: 'Asadi',
    إسراء: 'Israa',
    اسعد: 'Asaad',
    اسكندر: 'Isknadr',
    اسلام: 'Isalam',
    اسماء: 'asamaa',
    اسماعيل: 'isamaail',
    اسود: 'Asaod',
    اسيل: 'Asail',
    اشراق: 'Ishraq',
    اشرف: 'ashraf',
    اشواق: 'ashwaq',
    اصيل: 'Aseel',
    اطياف: 'Ataiaf',
    اعجمي: 'Aajmai',
    اعطيه: 'Aaataiah',
    اغادير: 'Aghadir',
    افان: 'Afan',
    افتخار: 'iftkhar',
    افراح: 'ifarah',
    افراسياب: 'Afarasaiab',
    افليح: 'Aflaih',
    اقبال: 'iqbal',
    اكديمي: 'Akdiamai',
    اكرام: 'ikaram',
    اكو: 'Ako',
    ال: 'Al',
    الاء: 'Alaa',
    الائمة: 'Alaamah',
    الاسدي: 'Alasadi',
    الاله: 'Alalah',
    الامير: 'Alameer',
    الباسط: 'Albast',
    الباقي: 'Albaqai',
    البدران: 'Albadran',
    البرماني: 'Albrmanei',
    البكري: 'Albkari',
    التفاة: 'Altfaah',
    الجبار: 'Aljabar',
    الجعيفري: 'Aljaifari',
    الجليل: 'Aljlail',
    الجنابي: 'Aljnabi',
    الجواد: 'Aljwad',
    الحافظ: 'Alihafz',
    الحاوي: 'Alihaoi',
    الحر: 'Alhor',
    الحسن: 'Alhassan',
    الحسين: 'Alhussain',
    حسن: 'hassan',
    حسين: 'hussain',
    الحق: 'Alhaq',
    الحكم: 'Alhkam',
    الحماني: 'Alhamanei',
    الحمداني: 'Alhamdanei',
    الحمزة: 'Alhamzah',
    الحميد: 'Alhamaid',
    الخالق: 'Alkhaliq',
    الخضر: 'Alkhidhuir',
    الخنياب: 'Alkhniab',
    الدين: 'Aldin',
    الرحمن: 'Alrahamun',
    الرحيم: 'Alrahaiam',
    الرحييم: 'Alrahaiiam',
    الرزاق: 'Alrzaq',
    الرسول: 'Alrsaol',
    الرض: 'Alradh',
    الرضا: 'Alradha',
    الرضه: 'Alradhah',
    الرفيعي: 'Alrafiai',
    الزهرة: 'Alzuhrah',
    السادة: 'Alsadah',
    السلام: 'Alsalam',
    السيد: 'Alsaid',
    الشحماني: 'Alshhamanei',
    الشكور: 'Alshkoar',
    الشيخ: 'Alshikh',
    الصاحب: 'Alsahaib',
    الصادق: 'Alsadq',
    الصمد: 'Alsmd',
    الطائي: 'Altaai',
    الطالب: 'Altalb',
    الطيب: 'Altaib',
    الطيف: 'Altaif',
    العابدي: 'Alaabadi',
    العابدين: 'Alaabadin',
    العالي: 'Alaalai',
    العباس: 'Alabas',
    العباسة: 'Alabasah',
    العباياجي: 'Alabaiaji',
    العزيز: 'Alazaiaz',
    العظيم: 'Alaziam',
    العيساوي: 'Alaisaoi',
    العيوس: 'Alaios',
    الغزالي: 'Alghzalai',
    الغفور: 'Alghufoar',
    الغفورصالح: 'Alghufoarsalih',
    الغني: 'Alghni',
    الفاضل: 'Alfadhl',
    الفتاح: 'Alftah',
    الفداء: 'Alfdaa',
    القادر: 'Aliqadr',
    القدوس: 'Aliqados',
    الكاظم: 'Alkazm',
    الكريم: 'Alkariam',
    الكسندر: 'Alksanaadr',
    اللطيف: 'Alltaif',
    الله: 'Allah',
    الم: 'Alam',
    الماز: 'Alamaz',
    المجيد: 'Alamajid',

    المحسن: 'Alamuhasan',
    المختار: 'Alamukhitar',
    المطلب: 'Alamtlb',
    المكطوف: 'Alamaktof',
    المنعم: 'Alamunam',
    المهدي: 'Alamuhadi',
    الناصر: 'Alnasar',
    النبي: 'Alnabi',
    النجف: 'Alnajf',
    النور: 'Alnoar',
    الهادي: 'Alhadi',
    الهاشم: 'AlhIshm',
    الهام: 'Alham',
    الهدى: 'Alhada',

    الواحد: 'Alwahd',
    الوس: 'Alos',
    الوهاب: 'Alohab',
    الياس: 'Alaias',
    الين: 'Alain',
    امال: 'Amal',
    اماني: 'Amanei',
    امثل: 'Amuthl',
    امجد: 'Amjad',
    امد: 'Amd',
    امل: 'Aml',
    امنه: 'Amunuah',
    امني: 'Amani',
    امير: 'Ameir',
    اميره: 'Ameirah',
    اميريحيى: 'Ameirihaia',
    اميمه: 'Amaiamuah',
    امين: 'Amain',
    انترانيك: 'Antraneik',
    انتصار: 'Antsar',
    انتيش: 'Antish',
    انذير: 'Anthir',
    انس: 'Anias',
    انصيف: 'Anasif',
    انعام: 'Anaam',
    انعيم: 'Anaiam',
    انغام: 'Angham',
    انغيمش: 'Anghiamsh',
    انمار: 'Anmar',
    انهر: 'Anuhr',
    انوار: 'Anwar',
    انيس: 'Aneis',
    انيسة: 'Aneisah',
    اهميل: 'Ahmail',
    اهويل: 'Ahoil',
    اوج: 'Awj',
    اوس: 'Aws',
    اوسيد: 'Aosaid',
    اوعد: 'Aoad',
    اوميد: 'Aomaid',
    ايات: 'Aiat',
    اياد: 'Aiad',
    ايزار: 'Aiazar',
    ايسر: 'Aisr',
    ايسل: 'Aisal',
    ايمان: 'iman',
    ايناس: 'Ainas',
    ايه: 'Aiah',
    ايهاب: 'Aihab',
    ايوب: 'Aiob',
    باجي: 'Baji',
    بادي: 'Badi',
    باذر: 'Bathr',
    بارح: 'Barah',
    بارق: 'Barq',
    بارون: 'Baron',
    باسل: 'Basal',
    باسم: 'Basam',
    باسمه: 'Basamuah',
    باشاعا: 'BIshaaa',
    باشي: 'BIshi',
    باقر: 'Baqur',
    بان: 'Ban',
    بانه: 'Banuah',
    باني: 'Banei',
    بةشدار: 'Bhshdar',
    بةمو: 'Bhmo',
    بةهمةن: 'Bhhmhn',
    بجاي: 'Bjai',
    بحر: 'Bhar',
    بختيار: 'Bkhitiar',
    بخيت: 'Bkhit',
    بداع: 'Badaa',
    بدر: 'Badir',
    بدران: 'Badran',
    بدري: 'Badri',
    بديم: 'Badiam',
    بديوي: 'Badioi',
    براء: 'Baraa',
    براق: 'Buraq',
    براك: 'Barak',
    بربون: 'Brabon',
    بردي: 'Bardi',
    برزان: 'Barzan',
    بركان: 'Burkan',
    برهان: 'burhan',
    برو: 'Bro',
    بروانت: 'Brawant',
    بريس: 'Bris',
    بريسم: 'Brisam',
    بسته: 'Bstah',
    بشار: 'Bshar',
    بشرى: 'Bshra',
    بشن: 'Bshn',
    بشير: 'Bshir',
    بصير: 'Bsir',
    بصيص: 'Bsis',
    بطاح: 'Btah',
    بطي: 'Btai',
    بكر: 'Bkar',
    بكي: 'Bki',
    بلاسم: 'Bilasam',
    بلال: 'Bilal',
    بلسم: 'Blsam',
    بلم: 'Blam',
    بلند: 'Blnad',
    بلوش: 'Blosh',
    بليغ: 'Blaigh',
    بنين: 'Bnin',
    بهاء: 'Bhaa',
    بهاءالدين: 'Bhaaaldin',
    بهاءجاسم: 'Bhaajasam',
    بهجت: 'Bhjt',
    بهجه: 'Bhjaah',
    بيجان: 'Bijan',
    بيداء: 'Bidaa',
    بير: 'Bir',
    بيرام: 'Biram',
    بيرق: 'Birq',
    بيكرد: 'Bikard',
    تابان: 'Taban',
    تالي: 'Talai',
    تايه: 'Taiah',
    تبارك: 'Tbarak',
    تجيل: 'Tjil',
    تحرير: 'Tharir',
    تحسين: 'Tahseen',
    ترتيب: 'Trtib',
    تركان: 'Trkan',
    تركي: 'Trki',
    ترياك: 'Traiak',
    تعبان: 'Taban',
    تقى: 'Tuqa',
    تقي: 'Tuqai',
    تكليف: 'Tkalaif',
    تمارا: 'Tmara',
    تموز: 'Tmoz',
    تميم: 'Tmaiam',
    تهاني: 'Thanei',
    توانا: 'Twana',
    توفيق: 'Tofiq',
    تيسير: 'Tisair',
    ثائر: 'Thaar',
    ثابت: 'Thabt',
    ثامر: 'Thamir',
    ثاني: 'Thanei',
    ثجيل: 'Thjil',
    ثروة: 'Throah',
    ثعبان: 'Thaban',
    ثميل: 'Thmail',
    ثناء: 'Thnaa',
    ثوره: 'Thoarah',
    جابر: 'Jabr',
    جاد: 'Jad',
    جادر: 'Jadr',
    جار: 'Jar',
    جارالله: 'Jarallah',
    جارح: 'Jarah',
    جاري: 'Jari',
    جازع: 'Jaza',
    جاسب: 'Jasb',
    جاسم: 'Jasam',
    جاعد: 'Jaad',
    جامل: 'Jaml',
    جاهل: 'Jahl',
    جاوان: 'Jawan',
    جايد: 'Jaid',
    جبار: 'Jabar',
    جبر: 'Jabr',
    جبرعبد: 'Jabrabad',
    جبلي: 'Jablai',
    جبوري: 'Jaboari',
    جبير: 'Jabir',
    جبيره: 'Jabirah',
    جداح: 'Jadah',
    جدوع: 'Jadoa',
    جديع: 'Jadia',
    جري: 'Jri',
    جريان: 'Jraian',
    جزاع: 'Jzaa',
    جسام: 'Jsam',
    جسم: 'Jsam',
    جعاز: 'Jaaz',
    جعفر: 'Jafar',
    جعفرحسين: 'Jafarahusain',
    جفات: 'Jfat',
    جلاب: 'Jilab',
    جلال: 'Jalal',
    جلوب: 'Jalob',
    جلود: 'Jlod',
    جليل: 'Jalail',
    جمال: 'Jamal',
    جمر: 'Jamir',
    جمعة: 'Jmuaah',
    جميل: 'Jamel',
    جميله: 'Jamelah',
    جنان: 'Jnan',
    جنجر: 'Jnajar',
    جنديل: 'Jnadil',
    جنرال: 'Jnral',
    جهاد: 'Jehad',
    جهادي: 'Jahadi',
    جهل: 'Jahl',
    جهلول: 'Jahlol',
    جواد: 'Jwad',
    جوامير: 'Jwameir',
    جودة: 'Jodah',
    جودي: 'Jodi',
    جوهر: 'Jawhar',
    جويد: 'Joid',
    جوير: 'Joir',
    جياد: 'Jiad',
    جيثوم: 'Jithom',
    جيجان: 'Jijan',
    جيليل: 'Jilail',
    جينه: 'Jinuah',
    جيهان: 'Jihan',
    حاتم: 'Hatim',
    حاجي: 'Haji',
    حارث: 'Harith',
    حازم: 'Hazm',
    حاشوش: 'HIshosh',
    حافظ: 'Hafiz',
    حاكم: 'Hakam',
    حامد: 'Hamid',
    حبش: 'Habsh',
    حبي: 'Hobi',
    حبيب: 'Habib',
    حرج: 'Haraj',
    حرز: 'Harz',
    حريز: 'Hariaz',
    حريش: 'Harish',
    حرين: 'Harin',
    حزام: 'Hizam',
    حسام: 'Husam',
    حساوي: 'Husaoi',
    حسناوي: 'Hasanaoi',
    حسنه: 'Hasanuah',
    حسنين: 'Hassanin',
    حسون: 'Hasaon',
    حسوني: 'Husaoni',
    حسيب: 'Husaib',
    حسيبه: 'Husaibah',
    حظي: 'Hazi',
    حفظي: 'Hfzi',
    حقي: 'Haqai',
    حكمت: 'Hikmat',
    حكيم: 'Hakiam',
    حلان: 'Hlan',
    حلبوص: 'Hlbos',
    حلو: 'Hilo',
    حلواص: 'Hilwas',
    حليف: 'Halaif',
    حليم: 'Haleem',
    حماد: 'Hamad',
    حمادي: 'Hamadi',
    حماي: 'Hamai',
    حمد: 'Hamad',
    حمدالله: 'Hamdallah',
    حمدان: 'Hamdan',
    حمدي: 'Hamdi',
    حمديه: 'Hamdiah',
    حمزة: 'Hamzah',
    حمزه: 'Hamzuah',
    حممود: 'Hammod',
    حمه: 'Hamuah',
    حمو: 'Hamo',
    حمود: 'Hamod',
    حمودي: 'Hamodi',
    حميد: 'Hamaid',
    حميده: 'Hamaidah',
    حميدي: 'Hamaidi',
    حنا: 'Hana',
    حنان: 'Hanan',
    حنتوش: 'Hantosh',
    حنظل: 'Hanizl',
    حنوش: 'Hanosh',
    حنوف: 'Hanof',
    حنون: 'Hanon',
    حنين: 'Hanin',
    حوراء: 'Hoaraa',
    حوكر: 'Hokar',
    حويدي: 'Hoidi',
    حياد: 'Haiad',
    حياوي: 'Haiaoi',
    حيدر: 'Haidr',
    حيدرجميل: 'Haidrjmail',
    حيدرشاكر: 'Haidrshakar',
    حيدرعبدلامير: 'Haidrabadlameir',
    حيدرمحسن: 'Haidrmuhasan',
    حير: 'Hair',
    خاجيك: 'Khajik',
    خادم: 'Khadam',
    خاشع: 'KhIsha',
    خاف: 'Khaf',
    خالد: 'Khalid',
    خالدجاسم: 'Khaldjasam',
    خالده: 'Khaldah',
    خالص: 'Khals',
    خالند: 'Khalnad',
    ختال: 'Khital',
    ختام: 'Khitam',
    خدام: 'Khdam',
    خدر: 'Khidar',
    خرباط: 'Khrabat',
    خربيط: 'Khrabit',
    خريبط: 'Khirbet',
    خريج: 'Khrij',
    خزعل: 'Khzal',
    خشان: 'Khshan',
    خشن: 'Khshn',
    خشيت: 'Khsheit',
    خضر: 'Khidhuir',
    خضير: 'Khdhir',
    خطاب: 'Khatab',
    خطار: 'Khatar',
    خلاوي: 'Khalaoi',
    خلخال: 'Khalkhal',
    خلدون: 'Khaldon',
    خلف: 'Khalf',
    خلود: 'Khalod',
    خليبص: 'Khalaibs',
    خليف: 'Khalaif',
    خليفة: 'Khalaifah',
    خليفه: 'Khalaifah',
    خليل: 'Khalail',
    خمائل: 'Khamael',
    خماس: 'Khamas',
    خماط: 'Khamat',
    خمو: 'Khmo',
    خميس: 'Khamais',
    خنجر: 'Khnajar',
    خننيه: 'Khnniah',
    خنياب: 'Khniab',
    خورشيد: 'Khoarshid',
    خوله: 'Kholah',
    خير: 'Khair',
    خيرالله: 'Khairallah',
    خيري: 'Khairi',
    خيريه: 'Khairiah',
    خيون: 'Khion',
    داخل: 'Dakhal',
    دادود: 'Dadod',
    داغر: 'Daghr',
    داليا: 'Dalaia',
    دانا: 'Dana',
    دانيال: 'Daneial',
    داود: 'Daod',
    دايخ: 'Daikh',
    دايش: 'Daish',
    دبش: 'Dbsh',
    دحام: 'Daham',
    دحدوح: 'Dhdoh',
    دخان: 'Dkhan',
    دخيل: 'Dkhil',
    در: 'Dr',
    دراج: 'Draj',
    درباش: 'DrabIsh',
    درويش: 'Daroish',
    دريا: 'Draia',
    دريد: 'Drid',
    دريس: 'Dris',
    دريول: 'Driol',
    دشر: 'Dshr',
    دعاء: 'Duaa',
    دعام: 'Duaam',
    دعبول: 'Duabol',
    دعيدش: 'Duaidsh',
    دلال: 'Dlal',
    دلزار: 'Dlzar',
    دلول: 'Dlol',
    دلي: 'Dlai',
    دليا: 'Dlaia',
    دليمي: 'Dlaiamai',
    دنبوش: 'Dnabosh',
    دنيا: 'Dnia',
    دهام: 'Dham',
    دهش: 'Dhsh',
    دونه: 'Donuah',
    دوهان: 'Dohan',
    دويح: 'Doih',
    ديلي: 'Dilai',
    دينا: 'Dina',
    ديوان: 'Diwan',
    ذبيح: 'Thbih',
    ذكاء: 'Thkaa',
    ذكريات: 'Thkaraiat',
    ذنون: 'Thanon',
    ذهيب: 'Thhib',
    ذوالفقار: 'Thwalfaqar',
    ذياب: 'Thiab',
    ذيبان: 'Thiban',
    رأفت: 'Raft',
    رؤاد: 'Ruad',
    رؤوف: 'Ruof',
    رؤى: 'Ruaa',
    رائد: 'Raad',
    رابح: 'Rabh',
    راتب: 'Ratb',
    راجح: 'Rajh',
    راجي: 'Raji',
    رازميك: 'Razmaik',
    راسم: 'Rasam',
    راشد: 'RIshd',
    راضي: 'Radhi',
    راغب: 'Raghb',
    رافد: 'Rafd',
    رافع: 'Rafa',
    رافي: 'Rafii',
    راكع: 'Raka',
    رامل: 'Raml',
    رامي: 'Rami',
    رانيا: 'Rania',
    رانيه: 'Raneiah',
    راهي: 'Rahi',
    راويه: 'Raoiah',
    ربا: 'Ruba',
    رباب: 'Rabab',
    رباش: 'RabIsh',
    رباح: 'Rabah',
    ربيع: 'Rabia',
    ربيعه: 'Rabiaah',
    رجا: 'Rjaa',
    رجاء: 'Rjaa',
    رجاب: 'Rjab',
    رجب: 'Rajab',
    رحاب: 'Rahab',
    رحم: 'Raham',
    رحمان: 'Rahaman',
    رحمة: 'Rahamah',
    رحمن: 'Rahamun',
    رحمه: 'Rahamuah',
    رحيل: 'Rahail',
    رحيم: 'Rahaiam',
    رحيمة: 'Rahaiamah',
    رحيمه: 'Rahaiamuah',
    رخيص: 'Rkhis',
    رداد: 'Rdad',
    ردام: 'Rdam',
    ردي: 'Rdi',
    رديف: 'Rdif',
    رزاق: 'Rzaq',
    رزكار: 'Rzkar',
    رزن: 'Rzin',
    رزوق: 'Rzoq',
    رزوقي: 'Rzoqai',
    رزوك: 'Rzok',
    رسل: 'Rsal',
    رسم: 'Rsam',
    رسميه: 'Rsamaiah',
    رسن: 'Rsan',
    رسول: 'Rsaol',
    رشا: 'Rasha',
    رشاد: 'Rshad',
    رشاش: 'RshIsh',
    رشدي: 'Rshdi',
    رشلا: 'Rshla',
    رشم: 'Rshm',
    رشيد: 'Rshid',
    رضا: 'Radha',
    رضوان: 'Radhwan',
    رعد: 'Rad',
    رغد: 'Raghad',
    رغدة: 'Raghdah',
    رغده: 'Raghdah',
    رغيان: 'Rghian',
    رغير: 'Raghir',
    رغيله: 'Raghilah',
    رفاء: 'Rafaa',
    رفاق: 'Rafaq',
    رفعت: 'Rafat',
    رفل: 'Rafal',
    رفيق: 'Rafiq',
    رقيه: 'Ruqaiah',
    ركبان: 'Rkban',
    رمح: 'Rmuh',
    رمزي: 'Rmzai',
    رمضان: 'Rmdhan',
    رنا: 'Rana',
    ره: 'Rah',
    رهك: 'Rhk',
    رهيج: 'Rhij',
    رهيف: 'Rhaif',
    رواء: 'Rawaa',
    رواد: 'Rawad',
    رواهب: 'Rawahb',
    روضان: 'Rodhan',
    روكان: 'Rokan',
    رومي: 'Romai',
    رويح: 'Roih',
    رياح: 'Raiah',
    رياض: 'Raiadh',
    ريام: 'Raiam',
    ريباز: 'Ribaz',
    ريبوار: 'Ribwar',
    ريسان: 'Risan',
    ريكةوت: 'Rikhot',
    ريمان: 'Riaman',
    ريژنه: 'Riژnuah',
    زائر: 'Zaar',
    زامل: 'Zaml',
    زانا: 'Zana',
    زاهد: 'Zahad',
    زاهر: 'Zahir',
    زاهرحسن: 'Zahirahasan',
    زايد: 'Zaid',
    زاير: 'Zair',
    زبال: 'Zbaal',
    زباله: 'Zbaalah',
    زبن: 'Zbn',
    زبون: 'Zbon',
    زبيري: 'Zbiri',
    زبين: 'Zbin',
    زحل: 'Zhl',
    زرزور: 'Zrzoar',
    زعبيل: 'Zabil',
    زعلان: 'Zalaan',
    زعيان: 'Zaian',
    زعيبل: 'Zaibl',
    زغير: 'Zghir',
    زكريا: 'Zkaraia',
    زكي: 'Zki',
    زكيان: 'Zkian',
    زمان: 'Zaman',
    زمن: 'Zamuan',
    زهراء: 'Zuhraa',
    زهراو: 'Zuhrao',
    زهرة: 'Zuhrah',
    زهيبان: 'Zuhiban',
    زهير: 'Zuhir',
    زوه: 'Zoah',
    زيا: 'Zaia',
    زياد: 'Zaiad',
    زياره: 'Zaiarah',
    زيد: 'Zaid',
    زيدان: 'Zaidan',
    زيدصائب: 'Zaidsaab',
    زيدون: 'Zaidon',
    زين: 'Zain',
    زينب: 'Zainab',
    زينل: 'Zainl',
    زينه: 'Zainuah',
    سؤدد: 'Sudd',
    ساجت: 'Sajt',
    ساجد: 'Sajad',
    ساجدة: 'Sajadah',
    ساجده: 'Sajadah',
    سارا: 'Sara',
    سارة: 'Sarah',
    ساره: 'Sarah',
    سالار: 'Salar',
    سالم: 'Salam',
    سام: 'Sam',
    سامح: 'Samuh',
    سامر: 'Samar',
    سامي: 'Samai',
    ساهرة: 'Sahirah',
    ساهره: 'Sahirah',
    ساهي: 'Sahi',
    سايب: 'Saib',
    سباهي: 'Sbahi',
    سبتي: 'Sbti',
    سبع: 'Sba',
    ستار: 'Star',
    ستوري: 'Stoari',
    سجا: 'Saja',
    سجى: 'Saja',
    سجاد: 'Sajad',
    سجود: 'Sjod',
    سجودة: 'Sajodah',
    سجى: 'Saja',
    سحاب: 'Sahab',
    سحر: 'Sahar',
    سدخان: 'Sadkhan',
    سرحان: 'Srahan',
    سركوت: 'Srkot',
    سركيس: 'Srkis',
    سرمد: 'Srmd',
    سرهيد: 'Surhid',
    سرور: 'Sroar',
    سرى: 'Sura',
    سريح: 'Srih',
    سريسح: 'Srisah',
    سطام: 'Stam',
    سعاد: 'Saaad',
    سعد: 'Saad',
    سعدون: 'Saadon',
    سعدي: 'Saaudai',
    سعدية: 'Saaudaiah',
    سعود: 'Saaod',
    سعيد: 'Saaid',
    سعيدحسين: 'Saaidhusain',
    سفيان: 'Sufian',
    سفيح: 'Sufih',
    سكران: 'Skaran',
    سلام: 'Salam',
    سلطان: 'Sultan',
    سلمان: 'Salaman',
    سلوان: 'Salwan',
    سلومي: 'Salomai',
    سلوى: 'Saloa',
    سليم: 'Sulaiam',
    سليمان: 'Sulaiaman',
    سليمه: 'Sulaiamuah',
    سماح: 'Samah',
    سماطي: 'Samatai',
    سماعيل: 'Samaail',
    سمر: 'Samar',
    سمليذ: 'Samlaith',
    سمية: 'Samaiah',
    سمير: 'Sameir',
    سميرة: 'Sameirah',
    سميره: 'Sameirah',
    سميه: 'Samaiah',
    سمير: 'Sameir',
    سنا: 'Sana',
    سناء: 'Sanaa',
    سنان: 'Sanan',
    سنباط: 'Sanabat',
    سنحاريب: 'Sanharib',
    سند: 'Sanaad',
    سندال: 'Sanaadal',
    سندس: 'Sanaads',
    سها: 'Suha',
    سهاد: 'Suhad',
    سهام: 'Suham',
    سهد: 'Suhad',
    سهر: 'Suhr',
    سهول: 'Suhol',
    سهى: 'Suha',
    سهيل: 'Suhail',
    سهيله: 'Suhailah',
    سوادي: 'Sawadi',
    سوران: 'Saoaran',
    سوسن: 'Saosan',
    سيد: 'Said',
    سيرمد: 'Sairmd',
    سيروان: 'Sairawan',
    سيف: 'Saif',
    سيوان: 'Saiwan',
    شاتي: 'Shati',
    شادمان: 'Shadaman',
    شاذل: 'Shathl',
    شارق: 'Sharq',
    شاطي: 'Shatai',
    شاكر: 'Shakar',
    شامل: 'Shaml',
    شاهر: 'Shahir',
    شاهود: 'Shahod',
    شاهين: 'Shahin',
    شايع: 'Shaia',
    شبرم: 'Shbrm',
    شبل: 'Shbl',
    شبلاوي: 'Shbilaoi',
    شبيب: 'Shbib',
    شتين: 'Shtin',
    شجاي: 'Shjai',
    شجر: 'Shajr',
    شجين: 'Shjin',
    شخاط: 'Shkhat',
    شخير: 'Shkhair',
    شخيمت: 'Shkhiamt',
    شدهان: 'Shdhan',
    شديد: 'Shdid',
    شذر: 'Shthr',
    شذى: 'Shtha',
    شراد: 'Shrad',
    شرشاب: 'Shrshab',
    شرهان: 'Shrhan',
    شري: 'Shri',
    شريده: 'Shridah',
    شريع: 'Shria',
    شريف: 'Shrif',
    شعبان: 'Shaban',
    شعشاع: 'Shashaa',
    شعلان: 'Shalaan',
    شغناب: 'Shghnab',
    شفيع: 'Shfia',
    شفيق: 'Shfiq',
    شكر: 'Shkar',
    شكران: 'Shkaran',
    شكري: 'Shkari',
    شكون: 'Shkon',
    شلاش: 'ShlIsh',
    شلال: 'Shlal',
    شلش: 'Shlsh',
    شمال: 'Shmal',
    شمخي: 'Shmukhi',
    شمران: 'Shmaran',
    شمس: 'Shams',
    شنان: 'Shnan',
    شناوه: 'Shnaoah',
    شناوي: 'Shnaoi',
    شنايه: 'Shnaiah',
    شنته: 'Shntah',
    شنشول: 'Shnashol',
    شنون: 'Shnon',
    شنيتار: 'Shnitar',
    شنيشل: 'Shnishl',
    شنين: 'Shnin',
    شهاب: 'Shahab',
    شهد: 'Shahad',
    شهلاء: 'Shahlaa',
    شهيب: 'Shahib',
    شهيد: 'Shahid',
    شوقي: 'Shoqai',
    شوكة: 'Shokah',
    شوكت: 'Shokt',
    شوكه: 'Shokah',
    شونة: 'Shonah',
    شياع: 'Shiaa',
    شيال: 'Shial',
    شيحان: 'Shihan',
    شيخه: 'Shikhah',
    شيرزاد: 'Shirzad',
    شيروان: 'Shirawan',
    شيرين: 'Shirin',
    شيلان: 'Shilan',
    شيلاوي: 'Shilaoi',
    شيماء: 'Shiamaa',
    شييت: 'Sheet',
    صائب: 'Saab',
    صابر: 'Sabr',
    صابرشيخة: 'Sabrshikhah',
    صابرين: 'Sabrin',
    صاحب: 'Sahaib',
    صاحي: 'Sahai',
    صادق: 'Sadq',
    صادقعبود: 'Sadqabod',
    صافي: 'Safii',
    صالح: 'Salih',
    صبا: 'Saba',
    صباج: 'Sabaj',
    صباح: 'Sabah',
    صبار: 'Sabar',
    صبحه: 'Sbhah',
    صبحي: 'Sbhai',
    صبر: 'Sabr',
    صبري: 'Sabri',
    صبيح: 'Sbih',
    صبيحة: 'Sbihah',
    صحن: 'Shan',
    صدام: 'Sadam',
    صديق: 'Sadiq',
    صريع: 'Saria',
    صفا: 'Safa',
    صفاء: 'Safaa',
    صفوت: 'Safot',
    صفوك: 'Safok',
    صكار: 'Sakar',
    صكب: 'Sakb',
    صكبان: 'Sakban',
    صكر: 'Sakar',
    صلاح: 'Salah',
    صلال: 'Slal',
    صنكور: 'Sankoar',
    صنيدح: 'Sanidh',
    صهباء: 'Suhbaa',
    صهيب: 'Suhib',
    صياح: 'Siah',
    صيوان: 'Siwan',
    ضاحي: 'Dhahai',
    ضاري: 'Dhari',
    ضامد: 'Dhamd',
    ضاهر: 'Dhahir',
    ضايف: 'Dhaif',
    ضحى: 'Dhuha',
    ضرعام: 'Dhuraam',
    ضرغا: 'Dhurgha',
    ضرغام: 'Dhurgham',
    ضعيف: 'Dhaif',
    ضفاف: 'Dhfaf',
    ضمير: 'Dhmeir',
    ضياء: 'Dhiaa',
    ضيدان: 'Dhidan',
    ضيول: 'Dhiol',
    طارش: 'Tarsh',
    طارق: 'Tarq',
    طال: 'Tal',
    طالب: 'Talb',
    طاهر: 'Tahir',
    طة: 'Tah',
    طراد: 'Trad',
    طركي: 'Trki',
    طعمة: 'Tamah',
    طعمه: 'Tamuah',
    طعيمه: 'Taiamuah',
    طلال: 'Talal',
    طلعت: 'Tlat',
    طليع: 'Tlaia',
    طنيش: 'Tanish',
    طه: 'Tah',
    طهماز: 'Thmaz',
    طوفان: 'Tofan',
    طوير: 'Toir',
    طيار: 'Taiar',
    طيب: 'Taib',
    طيبة: 'Taibah',
    طيف: 'Taif',
    ظافر: 'Zafar',
    ظافرشاكر: 'Zafarshakar',
    ظاهر: 'Zahir',
    عا: 'Aa',
    عائد: 'Aaad',
    عائدة: 'Aaadah',
    عاتقه: 'Aatuqah',
    عاتي: 'Aati',
    عاجب: 'Aajab',
    عاد: 'Aad',
    عادل: 'Aadl',
    عادي: 'Aadi',
    عارف: 'Aaraf',
    عازل: 'Aazal',
    عاشور: 'AIshoar',
    عاصم: 'Aasm',
    عاصي: 'Aasi',
    عاطر: 'Aatr',
    عاطف: 'Aatf',
    عاكف: 'Aakf',
    عاكول: 'Aakol',
    عامر: 'Aamar',
    عايد: 'Aaid',
    عايز: 'Aaiaz',
    عايش: 'Aaish',
    عايف: 'Aaif',
    عب: 'Ab',
    عباده: 'Abadah',
    عبادي: 'Abadi',
    عباس: 'Abas',
    عباي: 'Abai',
    عبد: 'Abad',
    عبدال: 'Abadal',
    عبدالاله: 'Abadalalah',
    عبدالامام: 'Abadalamam',
    عبدالامير: 'Abadalameir',
    عبدالباري: 'Abadalbari',
    عبدالباسط: 'Abadalbast',
    عبدالجبار: 'Abadaljabar',
    عبدالجليل: 'Abadaljlail',
    عبدالحافظ: 'Abadalihafz',
    عبدالحر: 'Abadalihar',
    عبدالحسن: 'Abadalihasan',
    عبدالحسين: 'Abadalihusain',
    عبدالحليم: 'Abadalihalaiam',
    عبدالحمزة: 'Abadalihamzah',
    عبدالحميد: 'Abadalihamaid',
    عبدالحميدمضر: 'Abadalihamaidmdhur',
    عبدالخالق: 'Abadalkhaliq',
    عبدالرحمان: 'Abadalrahaman',
    عبدالرحمن: 'Abadalrahamun',
    عبدالرحيم: 'Abadalrahaiam',
    عبدالرزاق: 'Abadalrzaq',
    عبدالرسول: 'Abadalrsaol',
    عبدالرضا: 'Abadalradha',
    عبدالزهرة: 'Abadalzuhrah',
    عبدالزهره: 'Abadalzuhrah',
    عبدالسادة: 'Abadalsadah',
    عبدالساده: 'Abadalsadah',
    عبدالستار: 'Abadalstar',
    عبدالسلام: 'Abadalsalam',
    عبدالسيد: 'Abadalsaid',
    عبدالصاحب: 'Abadalsahaib',
    عبدالصمد: 'Abadalsmd',
    عبدالعالي: 'Abadalaalai',
    عبدالعباس: 'Abadalabas',
    عبدالعزيز: 'Abadalazaiaz',
    عبدالعظيم: 'Abadalaziam',
    عبدالغفار: 'Abadalghufar',
    عبدالغفور: 'Abadalghufoar',
    عبدالفتاح: 'Abadalftah',
    عبدالقادر: 'Abadaliqadr',
    عبدالقهار: 'Abadaliqhar',
    عبدالكاضم: 'Abadalkadhm',
    عبدالكاظم: 'Abadalkazm',
    عبدالكريم: 'Abadalkariam',
    عبداللطيف: 'Abadalltaif',
    عبدالله: 'Abadallah',
    عبدالمجيد: 'Abadalamajid',
    عبدالملك: 'Abadalamlk',
    عبدالمنعم: 'Abadalamunam',
    عبدالمهدي: 'Abadalamuhadi',
    عبدالنبي: 'Abadalnabi',
    عبدالهادي: 'Abadalhadi',
    عبدالواحد: 'Abadalwahd',
    عبدالودود: 'Abadalodod',
    عبدالوهاب: 'Abadalohab',
    عبداليمه: 'Abadalaiamuah',
    عبدزيد: 'Abadzaid',
    عبدعلي: 'Abadualai',
    عبدي: 'Abadi',
    عبطان: 'Abtan',
    عبظالاميراسماعيل: 'Abzalameirasamaail',
    عبود: 'Abod',
    عبودي: 'Abodi',
    عبيد: 'Abid',
    عبيده: 'Abidah',
    عبير: 'Abeer',
    عبيس: 'Abis',
    عبيسان: 'Abisan',
    عتاد: 'Atad',
    عتيوي: 'Atioi',
    عثمان: 'Athman',
    عجاج: 'Ajaj',
    عجب: 'Ajaab',
    عجر: 'Ajar',
    عجلان: 'Ajlan',
    عجمي: 'Ajmai',
    عجيل: 'Ajil',
    عجيمي: 'Ajiamai',
    عداي: 'Adai',
    عدنان: 'Adnan',
    عدوية: 'Adoiah',
    عدويه: 'Adoiah',
    عدي: 'Audai',
    عذاب: 'Athab',
    عذار: 'Athar',
    عذراء: 'Athraa',
    عراق: 'Araq',
    عراك: 'Arak',
    عرد: 'Ard',
    عرمان: 'Arman',
    عريبي: 'Aribi',
    عزالدين: 'Azaldin',
    عزام: 'Azam',
    عزت: 'Aizat',
    عزران: 'Azran',
    عزيز: 'Azaiaz',
    عسل: 'Asal',
    عسيب: 'Asaib',
    عصام: 'Asam',
    عطا: 'Aata',
    عطب: 'Aatb',
    عطشان: 'Aatshan',
    عطوان: 'Atwan',
    عطوش: 'Atosh',
    عطية: 'Aataiah',
    عطيه: 'Aataiah',
    عطيوي: 'Aataioi',
    عفيف: 'Afief',
    عقيل: 'Aqail',
    عكاب: 'Akab',
    عكار: 'Akar',
    عكله: 'Akalah',
    علا: 'Alaa',
    علاء: 'Alaa',
    علاءالدين: 'Alaaaaldin',
    علاءهادي: 'Alaaahadi',
    علاالله: 'Alaaallah',
    علاوي: 'Alaaoi',
    علك: 'Alak',
    علكم: 'Alakam',
    علوان: 'Alwan',
    علوش: 'Alosh',
    على: 'Ala',
    علي: 'Ali',
    علياء: 'Alaiaa',
    علية: 'Alaiah',
    عليل: 'Alail',
    عليه: 'Alaiah',
    عليوي: 'Alaioi',
    عماد: 'Ammad',
    عمال: 'Ammal',
    عمران: 'omaran',
    عمر: 'Omar',
    عمير: 'Omeir',
    عناد: 'inad',
    عنبر: 'Anabr',
    عنتر: 'Antr',
    عنزي: 'Anazai',
    عنيد: 'Anid',
    عهود: 'Ahod',
    عواد: 'Awad',
    عواز: 'Awaz',
    عوان: 'Awan',
    عوج: 'Awj',
    عودة: 'Aodah',
    عوده: 'Aodah',
    عوض: 'Aodh',
    عوف: 'Aof',
    عوفي: 'Aofi',
    عون: 'Aon',
    عوني: 'Aoni',
    عويد: 'Aoid',
    عويز: 'Aoiaz',
    عياد: 'Aiad',
    عيادة: 'Aiadah',
    عيد: 'Aid',
    عيدان: 'Aidan',
    عيدي: 'Aidi',
    عيسى: 'Aisa',
    غائب: 'Ghaab',
    غاده: 'Ghadah',
    غازي: 'Ghazai',
    غافل: 'Ghafl',
    غالب: 'Ghalb',
    غالي: 'Ghalai',
    غانم: 'Ghanm',
    غاوي: 'Ghaoi',
    غايب: 'Ghaib',
    غثيث: 'Ghthith',
    غدير: 'Ghdir',
    غربي: 'Ghrabi',
    غريب: 'Ghrib',
    غريبه: 'Ghribah',
    غريكان: 'Ghrikan',
    غزال: 'Ghzal',
    غزوان: 'Ghzwan',
    غسان: 'Ghsan',
    غصون: 'Ghson',
    غضبان: 'Ghdhban',
    غضيب: 'Ghdhib',
    غفران: 'Ghufaran',
    غفوري: 'Ghufoari',
    غلام: 'Ghlam',
    غلوم: 'Ghlom',
    غناوي: 'Ghnaoi',
    غني: 'Ghni',
    غياث: 'Ghiath',
    غيث: 'Ghith',
    غيد: 'Ghid',
    غيداء: 'Ghidaa',
    غيلان: 'Ghilan',
    فؤاد: 'Fuad',
    فائز: 'Faaz',
    فائزة: 'Faazah',
    فائق: 'Faaq',
    فائقه: 'Faaqah',
    فاخر: 'Fakhr',
    فادي: 'Fadi',
    فاديه: 'Fadiah',
    فارس: 'Fars',
    فارض: 'Faradh',
    فاروق: 'Faroq',
    فاضل: 'Fadhl',
    فاطمة: 'Fatmah',
    فيض: 'Faidh',
    فاطمه: 'Fatmuah',
    فاظل: 'Fazl',
    فاظمه: 'Fazmuah',
    فاقد: 'Faqad',
    فالح: 'Falih',
    فاهم: 'Fahm',
    فايز: 'Faiaz',
    فتاح: 'Ftah',
    فتحي: 'Fthai',
    فتلاوي: 'Ftlaoi',
    فتين: 'Ftin',
    فجر: 'Fajr',
    فخر: 'Fakhr',
    فخرالدين: 'Fakhraldin',
    فخري: 'Fakhri',
    فخريه: 'Fakhriah',
    فداء: 'Fdaa',
    فدعم: 'Fduam',
    فذ: 'Fath',
    فرات: 'Farat',
    فراس: 'Faras',
    فرج: 'Farj',
    فرح: 'Farah',
    فرحان: 'Farahan',
    فرزدق: 'Farzdq',
    فرعون: 'Faraon',
    فرقان: 'Farqan',
    فرقد: 'Farqad',
    فرمان: 'Farman',
    فرهاد: 'Farhad',
    فرهود: 'Farhod',
    فريال: 'Faraial',
    فريح: 'Farih',
    فريد: 'Farid',
    فريدون: 'Faridon',
    فريق: 'Fariq',
    فزع: 'Faza',
    فضاء: 'Fadhaa',
    فضل: 'Fadhl',
    فقاد: 'Faqad',
    فقي: 'Faqai',
    فلاح: 'Flah',
    فلحي: 'Flhai',
    فليح: 'Flaih',
    فنجان: 'Fanajan',
    فندي: 'Fanadi',
    فنيخ: 'Fanikh',
    فهد: 'Fhad',
    فهمي: 'Fhmai',
    فهود: 'Fhod',
    فواز: 'Fwaz',
    فوزي: 'Fozai',
    فوزية: 'Fozaiah',
    فوي: 'Foi',
    فياض: 'Fiadh',
    فيان: 'Fian',
    فيحان: 'Fihan',
    فيصل: 'Fisl',
    قائد: 'Qaad',
    قابل: 'Qabl',
    قادر: 'Qadr',
    قاسم: 'Qasam',
    قبال: 'Qbal',
    قتيبة: 'Qutibah',
    قتيبه: 'Qutibah',
    قحطان: 'Qahtan',
    قدرية: 'Qadriah',
    قدوري: 'Qadoari',
    قدير: 'Qadir',
    قرني: 'Qurni',
    قصي: 'Qusi',
    قيدار: 'Qaidar',
    قيس: 'Qais',
    قيصر: 'Qaisar',
    قيطاس: 'Qaitas',
    كار: 'Kar',
    كارمن: 'Karmun',
    كاصد: 'Kasad',
    كاضم: 'Kadhm',
    كاطع: 'Kata',
    كاظم: 'Kazm',
    كافي: 'Kafii',
    كاكا: 'Kaka',
    كامران: 'Kamaran',
    كامل: 'Kaml',
    كاوة: 'Kaoah',
    كباشي: 'KbIshi',
    كحيص: 'Khais',
    كرار: 'Karar',
    كرجي: 'Karji',
    كردي: 'Kardi',
    كركيز: 'Karkiaz',
    كرم: 'Karm',
    كرومي: 'Karomai',
    كريدي: 'Karidi',
    كريم: 'Kariam',
    كريمة: 'Kariamah',
    كريمه: 'Kariamuah',
    كزار: 'Kzar',
    كزاز: 'Kzaz',
    كسار: 'Ksar',
    كشاش: 'KshIsh',
    كشكول: 'Kshkol',
    كشوش: 'Kshosh',
    كشيش: 'Kshish',
    كصاب: 'Ksab',
    كصاي: 'Ksai',
    كطان: 'Ktan',
    كطاني: 'Ktanei',
    كطران: 'Ktran',
    كطن: 'Ktan',
    كطوف: 'Ktof',
    كعيد: 'Kaid',
    كعيم: 'Kaiam',
    كفاح: 'Kfah',
    كلف: 'Kalf',
    كمال: 'Kamal',
    كمر: 'Kamar',
    كميش: 'Kamaish',
    كميله: 'Kamailah',
    كنار: 'Knar',
    كناص: 'Knas',
    كنبار: 'Knabar',
    كنزوع: 'Knazoa',
    كنعان: 'Knaan',
    كنيوت: 'Kniot',
    كنيوه: 'Knioah',
    كوت: 'Kot',
    كودان: 'Kodan',
    كوير: 'Koir',
    كياشي: 'KiIshi',
    كيطان: 'Kitan',
    كيلان: 'Kilan',
    لؤي: 'Luai',
    لابد: 'Labad',
    لاتي: 'Lati',
    لارا: 'Lara',
    لازم: 'Lazm',
    لازه: 'Lazuah',
    لاله: 'Lalah',
    لاوي: 'Laoi',
    لاوين: 'Laoin',
    لبنى: 'Lbna',
    لبيد: 'Lbid',
    لجين: 'Ljin',
    لطيف: 'Ltaif',
    لعيبي: 'Laibi',
    لفتة: 'Lftah',
    لفته: 'Lftah',
    لقاء: 'Liqaa',
    لقمان: 'Liqman',
    لكلوك: 'Lkalok',
    لكن: 'Lkn',
    لمى: 'Luma',
    لمياء: 'Lamaiaa',
    لميعة: 'Lamaiaah',
    لهمود: 'Lhmod',
    لهيبة: 'Lhibah',
    لواء: 'Lwaa',
    لوتي: 'Loti',
    لوكان: 'Lokan',
    لوي: 'Loi',
    لي: 'Lai',
    ليث: 'Laith',
    ليلو: 'Lailo',
    ليلى: 'Laila',
    لينا: 'Laina',
    مؤمن: 'Mumun',
    مؤيد: 'Muaid',
    ماج: 'Maj',
    ماجد: 'Majad',
    مارد: 'Mard',
    مازن: 'Mazin',
    ماشاف: 'MIshaf',
    ماشي: 'MIshi',
    ماضي: 'Madhi',
    مال: 'Mal',
    مالح: 'Malih',
    مالك: 'Malk',
    ماهر: 'Mahir',
    ماهود: 'Mahod',
    مايع: 'Maia',
    مايل: 'Mail',
    مبارك: 'Mbark',
    مبدر: 'Mbadr',
    متعب: 'Mtab',
    متي: 'Mti',
    مثال: 'Muthal',
    مثقال: 'Muthqal',
    مثنئ: 'Muthna',
    مثنى: 'Muthna',
    مثني: 'Muthni',
    مثيل: 'Muthil',
    مجاهد: 'Mjahad',
    مجباس: 'Mjabas',
    مجبل: 'Mjabl',
    مجهول: 'Mjahol',
    مجول: 'Mjol',
    مجيد: 'Majid',
    محارب: 'Maharab',
    محان: 'Mahan',
    محب: 'Mahab',
    محرم: 'Muharm',
    محسن: 'Muhasan',
    محل: 'Muhl',
    محلول: 'Muhilol',
    محمد: 'Muhammad',
    محمود: 'Mahmood',
    محموظ: 'Muhamoz',
    محود: 'Muhod',
    محوش: 'Muhosh',
    محي: 'Muhai',
    محيسن: 'Muhaisan',
    محيل: 'Muhail',
    محيميد: 'Muhaiamaid',
    مختار: 'Mukhitar',
    مختاض: 'Mukhitadh',
    مخرب: 'Mukhrab',
    مخلد: 'Mukhald',
    مخلص: 'Mukhals',
    مخلف: 'Mukhalf',
    مخيف: 'Mukhif',
    مد: 'Md',
    مدب: 'Mdb',
    مدحت: 'Mdht',
    مدرك: 'Mdrk',
    مدفون: 'Mdfon',
    مدلول: 'Mdlol',
    مدن: 'Mdn',
    مديح: 'Mdih',
    مذري: 'Mthri',
    مذكور: 'Mthkoar',
    مراح: 'Marah',
    مراد: 'Marad',
    مرتجى: 'Martja',
    مرتضى: 'Martdha',
    مردان: 'Mardan',
    مرزة: 'Marzah',
    مرزه: 'Marzuah',
    مرزوج: 'Marzwj',
    مرزوق: 'Marzoq',
    مرزوك: 'Marzok',
    مرسول: 'Marsaol',
    مرشد: 'Marshd',
    مرعي: 'Marai',
    مرفت: 'Maraft',
    مرهون: 'Marhon',
    مروان: 'Marawan',
    مروة: 'Maroah',
    مرود: 'Marod',
    مروه: 'Maroah',
    مريح: 'Marih',
    مريص: 'Maris',
    مريم: 'Mariam',
    مريوش: 'Mariosh',
    مزعل: 'Mzal',
    مزهر: 'Mzuhr',
    مسافر: 'Msafar',
    مسرهد: 'Msrhad',
    مسعد: 'Msaad',
    مسعود: 'Msaaod',
    مسلم: 'Msalam',
    مسير: 'Msair',
    مشتاق: 'Mshtaq',
    مشحوت: 'Mshhot',
    مشرف: 'Mshraf',
    مشرق: 'Mshrq',
    مششول: 'Mshshol',
    مشعان: 'Mshaan',
    مشعل: 'Mshal',
    مشكل: 'Mshkal',
    مشكور: 'Mshkoar',
    مشهد: 'Mshahad',
    مشوح: 'Mshoh',
    مصدف: 'Musadf',
    مصدق: 'Musadq',
    مصصفى: 'Mussafa',
    مصطاف: 'Mustaf',
    مصطفئ: 'Mustfa',
    مصطفى: 'Mustfa',
    مصطفي: 'Mustfi',
    مصعب: 'Musab',
    مصلح: 'Muslh',
    مضر: 'Mudhir',
    مطر: 'Motar',
    مطرود: 'Mtrod',
    مطشر: 'Mtshr',
    مطلب: 'Mtlb',
    مطلق: 'Mtliq',
    مطلك: 'Mtlk',
    مطوي: 'Mtoi',
    مطير: 'Mtair',
    مظاهر: 'Mzahir',
    مظفر: 'Mzfar',
    مظلوم: 'Mzlom',
    مظهر: 'Mzhr',
    معتز: 'Muatz',
    معتزز: 'Muatzz',
    معتصم: 'Muatsm',
    معتمد: 'Muatmd',
    معتوق: 'Muatoq',
    معتوك: 'Muatok',
    معد: 'Muad',
    معروف: 'Muarof',
    معله: 'Mualah',
    معلى: 'Muala',
    معن: 'Muan',
    معيجل: 'Muaijl',
    معيلو: 'Muailo',
    معين: 'Muain',
    معيوف: 'Muaiof',
    مغامس: 'Mghams',
    مغيطي: 'Mghitai',
    مفتاح: 'Muftah',
    مفتن: 'Muftin',
    مفتول: 'Muftol',
    مفضل: 'Mufadhl',
    مفيد: 'Mufid',
    مفيده: 'Mufidah',
    مفير: 'Mufir',
    مقبولة: 'Muqbolah',
    مقداد: 'Muqadad',
    مقدام: 'Muqadam',
    مكطوف: 'Maktof',
    مكلف: 'Makalf',
    مكي: 'Maki',
    مكيه: 'Makiah',
    ملا: 'Mla',
    ملاذ: 'Mlath',
    ملاك: 'Mlak',
    ملكي: 'Mlki',
    مله: 'Mlah',
    ملهود: 'Mlhod',
    ملهوف: 'Mlhof',
    ملوح: 'Mloh',
    ملوكي: 'Mloki',
    مليحه: 'Mlaihah',
    ممتاز: 'Mmtaz',
    ممدوح: 'Mmdoh',
    ممصبح: 'Mmusbh',
    ممند: 'Mmunad',
    مناتي: 'Munati',
    مناحي: 'Munahai',
    منار: 'Munar',
    مناضل: 'Munadhl',
    مناف: 'Munaf',
    منال: 'Munal',
    منتصر: 'Muntsar',
    منتضر: 'Muntdhur',
    منتظر: 'Muntzr',
    منجد: 'Munajad',
    منذر: 'Munthr',
    منسي: 'Munisai',
    منشد: 'Munashd',
    منصور: 'Munasoar',
    منعثر: 'Munathr',
    منعم: 'Munam',
    منفي: 'Munfi',
    منهل: 'Munuhl',
    منيب: 'Manib',
    منير: 'Manir',
    منيشد: 'Manishd',
    منيع: 'Mania',
    مها: 'Maha',
    مهاد: 'Muhad',
    مهاوش: 'Muhaosh',
    مهاوي: 'Muhaoi',
    مهتدي: 'Muhtdi',
    مهدي: 'Muhadi',
    مهنا: 'Muhna',
    مهند: 'Muhnad',
    مهندحاجي: 'Muhnadhaji',
    مهنه: 'Muhnuah',
    مهودر: 'Muhodr',
    مهوس: 'Muhos',
    مهيب: 'Muhib',
    مهيدي: 'Muhidi',
    مهيمن: 'Muhaiamun',
    موات: 'Mwat',
    موجد: 'Mwjad',
    موحان: 'Mohan',
    موزان: 'Mozan',
    موسى: 'Mosa',
    موسىى: 'Mosaa',
    موعود: 'Moaod',
    موفق: 'Mofaq',
    مولود: 'Molod',
    مولى: 'Mola',
    مويح: 'Moih',
    مويد: 'Moid',
    مويني: 'Moini',
    مياسه: 'Maiasuah',
    ميثاق: 'Meithaq',
    ميثم: 'Meithm',
    ميرخان: 'Meirkhan',
    ميرزا: 'Meirza',
    ميزر: 'Maiazr',
    ميس: 'Mais',
    ميساء: 'Maisaa',
    ميسر: 'Maisr',
    ميسرة: 'Maisrah',
    ميسم: 'Maisam',
    ميعاد: 'Maiaad',
    ميلاد: 'Mailad',
    نائل: 'Nael',
    ناجح: 'Najh',
    ناجي: 'Naji',
    نادر: 'Nadr',
    نادين: 'Nadin',
    ناديه: 'Nadiah',
    نازك: 'Nazk',
    ناصح: 'Nash',
    ناصر: 'Nasar',
    ناصرنجم: 'Nasarnajm',
    ناضم: 'Nadhm',
    ناطق: 'Natq',
    ناظم: 'Nazm',
    نافع: 'Nafa',
    نافل: 'Nafl',
    ناقع: 'Naqa',
    ناهض: 'Nahdh',
    ناهي: 'Nahi',
    نايف: 'Naif',
    نايل: 'Nail',
    نايم: 'Naiam',
    نبأ: 'Naba',
    نباء: 'Nabaa',
    نبراس: 'Nabaras',
    نبهان: 'Nabhan',
    نبيل: 'Nabil',
    نجاح: 'Najah',
    نجلاء: 'Najlaa',
    نجله: 'Najlah',
    نجم: 'Najm',
    نجوان: 'Najwan',
    نجوى: 'Najoa',
    نجيب: 'Najib',
    ند: 'Nad',
    نداء: 'Nadaa',
    ندى: 'Nada',
    نديم: 'Nadiam',
    نذار: 'Nthar',
    نذير: 'Nthir',
    نزار: 'Nazar',
    نستعين: 'Nistain',
    نسرين: 'Nisrin',
    نسمه: 'Nisamuah',
    نسيم: 'Nisaiam',
    نشات: 'Nashat',
    نشوان: 'Nashwan',
    نصار: 'Nasar',
    نصر: 'Nasar',
    نصير: 'Nasir',
    نصيف: 'Nasif',
    نضال: 'Ndhal',
    نظام: 'Nizam',
    نظمي: 'Nizmai',
    نظيف: 'Nizif',
    نظيم: 'Niziam',
    نعاس: 'Naas',
    نعمان: 'Namman',
    نعمة: 'Namah',
    نعمت: 'Namt',
    نعمه: 'Namuah',
    نعيم: 'Naiam',
    نعيمة: 'Naiamah',
    نغم: 'Ngham',
    نغمش: 'Nghamsh',
    نفاوة: 'Nfaoah',
    نمل: 'Nml',
    نمير: 'Nmeir',
    نهاد: 'Nuhad',
    نهار: 'Nuhar',
    نهله: 'Nuhlah',
    نهى: 'Nuha',
    نهير: 'Nuhir',
    نوار: 'Nwar',
    نواف: 'Nwaf',
    نوال: 'Nwal',
    نور: 'Noar',
    نورا: 'Noara',
    نوران: 'Noaran',
    نورة: 'Noarah',
    نورس: 'Noars',
    نورقيس: 'Noaruqais',
    نوره: 'Noarah',
    نوري: 'Noari',
    نوريه: 'Noariah',
    نوفل: 'Nofl',
    نويران: 'Noiran',
    نياز: 'Niaz',
    نينا: 'Nina',
    هاتف: 'Hatf',
    هادي: 'Hadi',
    هارف: 'Haraf',
    هارون: 'Haron',
    هاشم: 'HIshm',
    هالة: 'Halah',
    هاله: 'Halah',
    هاني: 'Hanei',
    هاورى: 'Haoara',
    هبة: 'Hbah',
    هبه: 'Hbah',
    هتيمي: 'Htiamai',
    هجار: 'Hjar',
    هجام: 'Hjam',
    هداب: 'Hadab',
    هدى: 'Hada',
    هدي: 'Hadi',
    هدير: 'Hadir',
    هديل: 'Hadil',
    هذال: 'Hthal',
    هراير: 'Hrair',
    هشام: 'Hsham',
    هلال: 'Hlal',
    هلهول: 'Hlhol',
    هلول: 'Hlol',
    هليل: 'Hlail',
    همام: 'Hmam',
    همسه: 'Hmsuah',
    هناء: 'Hnaa',
    هند: 'Hnad',
    هندال: 'Hnadal',
    هنود: 'Hnod',
    هه: 'Hah',
    هوار: 'Hwar',
    هوازن: 'Hwazin',
    هوبي: 'Hobi',
    هوكر: 'Hokar',
    هويدي: 'Hoidi',
    هوير: 'Hoir',
    هياس: 'Hias',
    هيال: 'Hial',
    هيام: 'Hiam',
    هيثم: 'Hithm',
    هيثيم: 'Hithiam',
    هيف: 'Haif',
    هيفاء: 'Haifaa',
    هيلان: 'Hailan',
    هيلي: 'Hailai',
    هيمن: 'Haiamun',
    هيوا: 'Hiwa',
    وئام: 'Waam',
    وائل: 'Wael',
    واثق: 'Wathq',
    وادي: 'Wadi',
    واسع: 'Wasaa',
    واعي: 'Waai',
    وافد: 'Wafd',
    والي: 'Walai',
    وثيق: 'Wthiq',
    وجدان: 'Wjadan',
    وجدي: 'Wjadi',
    وحيد: 'Whaid',
    وحيده: 'Whaidah',
    وداد: 'Wdad',
    وداعه: 'Wdaaah',
    وديان: 'Wdian',
    وديع: 'Wdia',
    ورد: 'Ward',
    ورده: 'Wardah',
    وزي: 'Wzai',
    وزير: 'Wzair',
    وسام: 'Wsam',
    وسمي: 'Wsamai',
    وسن: 'Wsan',
    وسيله: 'Wsailah',
    وسيم: 'Wsaiam',
    وصخ: 'Wskh',
    وصفي: 'Wsafi',
    وضاح: 'Wdhah',
    وطبان: 'Wtban',
    وعد: 'Wad',
    وفر: 'Wfar',
    وفي: 'Wfi',
    وقاص: 'Wqas',
    وقيد: 'Wqaid',
    ولاء: 'Wlaa',
    ولي: 'Wlai',
    وليد: 'Wlaid',
    وليدعبدالملك: 'Wlaiduabadalamlk',
    وناس: 'Wnas',
    وه: 'Wah',
    وهاب: 'Whab',
    وهام: 'Wham',
    وهد: 'Whad',
    وهيب: 'Whib',
    ىذلا: 'Athla',
    ياس: 'Yas',
    ياسر: 'Yasr',
    ياسرحسام: 'Yasrahusam',
    ياسمين: 'Yasamain',
    ياسين: 'Yasain',
    يحى: 'Yha',
    يحي: 'Yhai',
    يحيى: 'Yhaia',
    يزن: 'Yazin',
    يسار: 'Ysar',
    يسرى: 'Ysura',
    يعرب: 'Yarab',
    يعسوب: 'Yasaob',
    يعقوب: 'Yaqob',
    يلدا: 'Ylda',
    يوار: 'Ywar',
    يوسف: 'Yousf',
    يوسفةسعيد: 'Yousfhsaaid',
    يونس: 'Yonis',
    يونو: 'Yono',
    پةريز: 'پhriaz',
    تميمي: 'Tmaiamai',
    التميمي: 'Altmaiamai',
    أسد: 'Asad',
    عشور: 'Ashoar',
    'عبد الفتاح Abad': 'Alftah',
    أنطوني: 'Antoni',
    'عبد المنعم Abad': 'Alamunam',
    علاء: 'Alaaa',
    أسلم: 'Asalam',
    'عبد الله Abad': 'Allah',
    'أولي جاما Aolai': 'Jama',
    أنس: 'Anias',
    عبود: 'Abod',
    أحمد: 'Ahamd',
    أكرم: 'Akarm',
    امين: 'Amain',
    أمجد: 'Amjad',
    'عَ مر Aَ': 'mar',
    انيس: 'Aneis',
    عطه: 'Atah',
    'عبد العزيز Abad': 'Alazaiaz',
    ايمن: 'Aiamun',
    عفيفة: 'Afiefah',
    عيني: 'Aini',
    أمل: 'Aml',
    أمير: 'Ameir',
    أفرا: 'Afara',
    ألماس: 'Alamas',
    أمينة: 'Amainah',
    آية: 'Aiah',
    أبدقاني: 'Abadqanei',
    افنان: 'Afanan',
    'أمت السلام Amt': 'Alsalam',
    أنابيا: 'Anabia',
    عايشة: 'Aaishah',
    بشار: 'Bshar',
    برهان: 'Brhan',
    باسل: 'Basal',
    بسيم: 'Bsaiam',
    بشر: 'Bshr',
    بهى: 'Bha',
    بهير: 'Bhir',
    باسم: 'Basam',
    بلال: 'Bilal',
    بدر: 'Badr',
    'بدر الدين Badr': 'Aldin',
    بركة: 'Brkah',
    بسيل: 'Bsail',
    بطرس: 'Btrs',
    بشرى: 'Bshra',
    كارمين: 'Karmain',
    قنطرة: 'Qntrah',
    كايتلين: 'Kaitlain',
    شمس: 'Shams',
    دوانا: 'Dwana',
    دابر: 'Dabr',
    'ضياء الدين Dhiaa': 'Aldin',
    ضحى: 'Dhuha',
    دلال: 'Dlal',
    دانيا: 'Daneia',
    ضياء: 'Dhiaa',
    آمير: 'Ameir',
    عصام: 'Asam',
    إيهاب: 'Iihab',
    إميلي: 'Imailai',
    فرحان: 'Farahan',
    'فَ لاح Fَ': 'lah',
    فادي: 'Fadi',
    فريدفس: 'Faridfs',
    فوزية: 'Fozaiah',
    غريس: 'Ghris',
    غبريالا: 'Ghbraiala',
    حسام: 'Husam',
    هيثم: 'Hithm',
    حيدر: 'Haidr',
    هشام: 'Hsham',
    حمزة: 'Hamzah',
    هيفاء: 'Haifaa',
    هجرى: 'Hjra',
    حفصى: 'Hfsa',
    هبة: 'Hbah',
    هاجر: 'Hajr',
    هداية: 'Hadaiah',
    حنان: 'Hanan',
    عصام: 'Asam',
    إسهامي: 'Isuhamai',
    عزتي: 'Aizati',
    اسراء: 'Asraa',
    عزة: 'Azah',
    يشم: 'Yshm',
    جوهرة: 'Johrah',
    جفريا: 'Jfaraia',
    جميلة: 'Jmailah',
    كريم: 'Kariam',
    خليل: 'Khalail',
    خالد: 'Khald',
    خضر: 'Khidhuir',
    خديجة: 'Khdijah',
    'لقمان الحكيم Liqman': 'Alihakiam',
    لينا: 'Laina',
    ليلى: 'Laila',
    ليلى: 'Laila',
    مصطفى: 'Mustfa',
    مشتاق: 'Mshtaq',
    ماهر: 'Mahir',
    مبارك: 'Mbark',
    مهند: 'Muhnad',
    منتهى: 'Muntha',
    مينى: 'Maina',
    محسنة: 'Muhasanah',
    نديم: 'Nadiam',
    نينوس: 'Ninos',
    نسيم: 'Nisaiam',
    نور: 'Noar',
    نظيف: 'Nizif',
    ناصر: 'Nasar',
    نبيل: 'Nabil',
    نصر: 'Nasar',
    نغمة: 'Nghamah',
    نور: 'Noar',
    نفين: 'Nfin',
    نايف: 'Naif',
    نسمة: 'Nisamah',
    نجاة: 'Najaah',
    نجية: 'Najiah',
    نجمة: 'Najmah',
    نادين: 'Nadin',
    أسامة: 'Asamah',
    أسامة: 'Asamah',
    بيتر: 'Bitr',
    قاسم: 'Qasam',
    رشيد: 'Rshid',
    ريم: 'Riam',
    راما: 'Rama',
    روان: 'Rawan',
    روبي: 'Robi',
    سعيد: 'Saaid',
    صلاح: 'Slah',
    سامر: 'Samar',
    صبري: 'Sabri',
    صالح: 'Salih',
    جميل: 'Jmail',
    شمامة: 'Shmamah',
    شهد: 'Shahad',
    سناء: 'Sanaa',
    شوكيرا: 'Shokira',
    صفاء: 'Safaa',
    شذى: 'Shtha',
    سهام: 'Suham',
    سالي: 'Salai',
    سوزان: 'Saozan',
    سمر: 'Samar',
    سامية: 'Samaiah',
    تامر: 'Tamar',
    تميم: 'Tmaiam',
    طلعت: 'Tlat',
    تمار: 'Tmar',
    توشانا: 'Toshana',
    'أمل الخير Aml': 'Alkhair',
    وليد: 'Wlaid',
    وسام: 'Wsam',
    وجه: 'Wjaah',
    وجدي: 'Wjadi',
    وادي: 'Wadi',
    وحيد: 'Whaid',
    وجيه: 'Wjiah',
    وكيل: 'Wkil',
    وليد: 'Wlaid',
    'ولي الله Wlai': 'Allah',
    وسيم: 'Wsaiam',
    وزير: 'Wzair',
    وقار: 'Wqar',
    وفى: 'Wfa',
    يحيى: 'Yhaia',
    يعقوب: 'Yaqob',
    ياسمين: 'Yasamain',
    يسرى: 'Ysura',
    يمنى: 'Yamuna',
    زفير: 'Zfir',
    زاهذ: 'Zahth',
    ظهير: 'Zhir',
    زيد: 'Zaid',
    زيم: 'Zaiam',
    زكريا: 'Zkaraia',
    زكي: 'Zki',
    زكية: 'Zkiah',
    زياد: 'Zaiad',
    زهير: 'Zuhir',
    'ذو الفقار': 'Tho Alfaqar',
    زهيرة: 'Zuhirah',
    زارا: 'Zara',
    عالية: 'Aalaiah',
    آمنة: 'Amunah',
    آمر: 'Amar',
    عباس: 'Abas',
    أبو: 'Abo',
    'أبو بكر Abo': 'Bkar',
    آدم: 'Adam',
    عادل: 'Aadl',
    عدنان: 'Adnan',
    عفيف: 'Afief',
    أفضل: 'Afadhl',
    آحمد: 'Ahamd',
    عائشة: 'Aaashah',
    عائشة: 'Aaashah',
    أكرم: 'Akarm',
    علياء: 'Alaiaa',
    علية: 'Alaiah',
    علية: 'Alaiah',
    علياء: 'Alaiaa',
    أماني: 'Amanei',
    آمنة: 'Amunah',
    أمينة: 'Amainah',
    أميرة: 'Ameirah',
    أمجد: 'Amjad',
    عمار: 'Ammar',
    انس: 'Anias',
    أنيس: 'Aneis',
    أنور: 'Anoar',
    عاقل: 'Aaql',
    أريج: 'Arij',
    أسعد: 'Asaad',
    أشفاق: 'Ashfaq',
    أصف: 'Asaf',
    أسمى: 'Asama',
    أسرى: 'Asura',
    'عطاء الله Aataa': 'Allah',
    عاطف: 'Aatf',
    عطية: 'Aataiah',
    عائدة: 'Aaadah',
    عائشة: 'Aaashah',
    أيوب: 'Aiob',
    عزيز: 'Azaiaz',
    عذراء: 'Athraa',
    بهيجة: 'Bhijah',
    بهيجة: 'Bhijah',
    باقي: 'Baqai',
    باقي: 'Baqai',
    باراك: 'Barak',
    بركات: 'Brkat',
    باسم: 'Basam',
    بصير: 'Bsir',
    باسط: 'Bast',
    بسام: 'Bsam',
    بتول: 'Btol',
    بنيامين: 'Bniamain',
    بولس: 'Bols',
    بدور: 'Badoar',
    برهان: 'Brhan',
    دلال: 'Dlal',
    دانية: 'Daneiah',
    دانيال: 'Daneial',
    داود: 'Daod',
    ديمة: 'Diamah',
    ضياء: 'Dhiaa',
    إبراهيم: 'Ibarahaiam',
    عيسى: 'Aisa',
    فادية: 'Fadiah',
    فاضلة: 'Fadhlah',
    فهد: 'Fhad',
    فهمة: 'Fhmah',
    فائز: 'Faaz',
    فيصل: 'Fisl',
    فخري: 'Fakhri',
    فرج: 'Farj',
    فرج: 'Farj',
    فريد: 'Farid',
    فرحة: 'Farahah',
    فاروق: 'Faroq',
    فرح: 'Farah',
    فاروق: 'Faroq',
    فتحية: 'Fthaiah',
    فاطمة: 'Fatmah',
    فطين: 'Ftain',
    فوزي: 'Fozai',
    فوزية: 'Fozaiah',
    فيروز: 'Firoz',
    فائزة: 'Faazah',
    فضة: 'Fadhah',
    فكري: 'Fikari',
    فردوس: 'Fardos',
    فضة: 'Fadhah',
    فؤاد: 'Fuad',
    جبر: 'Jabr',
    جلال: 'Jlal',
    جمال: 'Jmal',
    جميلة: 'Jmailah',
    جواهر: 'Jwahir',
    جودت: 'Jodt',
    غادة: 'Ghadah',
    غالب: 'Ghalb',
    غسان: 'Ghsan',
    غفران: 'Ghufaran',
    جودة: 'Jodah',
    حبيبة: 'Habibah',
    هادي: 'Hadi',
    هديل: 'Hadil',
    هدية: 'Hadiah',
    هادية: 'Hadiah',
    حفيظ: 'Hfiz',
    حفصة: 'Hfsah',
    هاجر: 'Hajr',
    حيدر: 'Haidr',
    هاجر: 'Hajr',
    حكيم: 'Hakiam',
    حليم: 'Halaiam',
    حميد: 'Hamaid',
    حميدة: 'Hamaidah',
    هناء: 'Hnaa',
    حنان: 'Hanan',
    هنية: 'Hniah',
    حنيفة: 'Hanifah',
    هنية: 'Hniah',
    حارث: 'Harth',
    هارون: 'Haron',
    هاشم: 'HIshm',
    حاسم: 'Hasam',
    حسان: 'Husan',
    حواء: 'Hwaa',
    هيفاء: 'Haifaa',
    هشام: 'Hsham',
    حكمة: 'Hkamah',
    هدى: 'Hada',
    حسام: 'Husam',
    حسام: 'Husam',
    حسني: 'Hasani',
    حيدر: 'Haidr',
    إبراهيم: 'Ibarahaiam',
    إبتسام: 'Ibtsam',
    ايهاب: 'Aihab',
    إكرام: 'Ikaram',
    إلهام: 'Ilham',
    عماد: 'Ammad',
    عمرام: 'Amaram',
    إمتياز: 'Imtiaz',
    إيناس: 'Iinas',
    عرفان: 'Arafan',
    عصام: 'Asam',
    إسماعيل: 'Isamaail',
    عصمة: 'Asmah',
    إسراء: 'Israa',
    اعتدال: 'Aatdal',
    إزدهار: 'Izdhar',
    'عز الدين Az': 'Aldin',
    جابر: 'Jabr',
    جبريل: 'Jabril',
    جعفر: 'Jafar',
    جلال: 'Jlal',
    جليلة: 'Jlailah',
    جمال: 'Jmal',
    جميلة: 'Jmailah',
    جميلة: 'Jmailah',
    جنان: 'Jnan',
    جاذبية: 'Jathbiah',
    جودت: 'Jodt',
    جنان: 'Jnan',
    جمانة: 'Jmanah',
    جنيد: 'Jnid',
    قدير: 'Qadir',
    كامل: 'Kaml',
    كريم: 'Kariam',
    كريمة: 'Kariamah',
    خديجة: 'Khdijah',
    خليل: 'Khalail',
    خالدة: 'Khaldah',
    خليل: 'Khalail',
    خليق: 'Khalaiq',
    خيرات: 'Khairat',
    خيرية: 'Khairiah',
    ليلى: 'Laila',
    لميس: 'Lamais',
    لطيفة: 'Ltaifah',
    لطيفة: 'Ltaifah',
    ليلى: 'Laila',
    لينا: 'Laina',
    لجين: 'Ljin',
    لطفي: 'Ltfi',
    مهاة: 'Muhaah',
    محفوظ: 'Mahfoz',
    مكرم: 'Makarm',
    ملك: 'Mlk',
    ملكة: 'Mlkah',
    منصور: 'Munasoar',
    مريم: 'Mariam',
    مريم: 'Mariam',
    مسعود: 'Msaaod',
    معصومة: 'Muasomah',
    مدحة: 'Mdhah',

    معراج: 'Muaraj',
    مسعد: 'Msaad',

    منى: 'Muna',
    منى: 'Muna',
    مبينة: 'Mbinah',

    محسن: 'Muhasan',
    مختار: 'Mukhitar',
    مؤمنة: 'Mumunah',
    منى: 'Muna',
    منيرة: 'Manirah',
    مراد: 'Marad',
    مرتضى: 'Martdha',
    مسعد: 'Msaad',
    مصطفى: 'Mustfa',
    معتصم: 'Muatsm',
    نبيلة: 'Nabilah',
    نادر: 'Nadr',
    نديم: 'Nadiam',
    نادرة: 'Nadrah',
    نضرة: 'Ndhurah',
    نجي: 'Naji',
    نائل: 'Nael',
    نعيم: 'Naiam',
    نجي: 'Naji',
    نجوى: 'Najoa',
    نصير: 'Nasir',
    نسيب: 'Nisaib',
    ناصر: 'Nasar',
    ناصر: 'Nasar',
    نويد: 'Noid',
    نوال: 'Nwal',
    نزيه: 'Nazaiah',
    نظلي: 'Nizlai',
    نعمة: 'Namah',
    نزار: 'Nazar',
    نور: 'Noar',
    نهى: 'Nuha',
    نورة: 'Noarah',
    قدير: 'Qadir',
    قاسم: 'Qasam',
    قصي: 'Qusi',
    رباب: 'Rabab',
    رفيق: 'Rafiq',
    راحة: 'Rahah',
    رحيم: 'Rahaiam',
    رئيس: 'Rais',
    رجاء: 'Rjaa',
    رجية: 'Rjiah',
    رمضان: 'Rmdhan',
    رملة: 'Rmlah',
    رندة: 'Rnadah',
    رانية: 'Raneiah',
    رشاد: 'Rshad',
    رشيد: 'Rshid',
    رشيدة: 'Rshidah',
    راسمة: 'Rasamah',
    رؤوف: 'Ruof',
    راوية: 'Raoiah',
    ريحانة: 'Rihanah',
    رضاء: 'Radhaa',
    رياض: 'Raiadh',
    رضاء: 'Radhaa',
    رضوان: 'Radhwan',
    رفعة: 'Rafaah',
    ريم: 'Riam',
    رضوانة: 'Radhwanah',
    روح: 'Roh',
    رقية: 'Ruqaiah',
    رواء: 'Rawaa',
    سعادة: 'Saaadah',
    'عز الدين Az': 'Aldin',
    صبري: 'Sabri',
    صدام: 'Sadam',
    سعدية: 'Saaudaiah',
    صفية: 'Safiah',
    سحر: 'Sahaar',
    سعيدة: 'Saaidah',
    صخر: 'Skhr',
    صلاح: 'Slah',
    سليم: 'Sulaiam',
    صالحة: 'Salihah',
    صالحة: 'Salihah',
    سليم: 'Sulaiam',
    سلمى: 'Saluma',
    سلوة: 'Saloah',
    سمر: 'Samar',
    سميرة: 'Sameirah',
    سامية: 'Samaiah',
    سمير: 'Sameir',
    سامية: 'Samaiah',
    سني: 'Sani',
    سنية: 'Saniah',
    سارة: 'Sarah',
    سيد: 'Said',
    سليمة: 'Sulaiamah',
    شادي: 'Shadi',
    شادية: 'Shadiah',
    شفيق: 'Shfiq',
    شهد: 'Shahad',
    شهيرة: 'Shahirah',
    شهرزاد: 'Shuhrzad',
    شهزاد: 'Shahzad',
    شكيل: 'Shkil',
    شكيلة: 'Shkilah',
    شاكرة: 'Shakarah',
    شكورة: 'Shkoarah',
    'شمس الدين Shams': 'Aldin',
    شريف: 'Shrif',
    شريف: 'Shrif',
    شيماء: 'Shiamaa',
    شريف: 'Shrif',
    شكري: 'Shkari',
    شكرية: 'Shkariah',
    سها: 'Suha',
    سهيلة: 'Suhailah',
    سليمان: 'Sulaiaman',
    سلطان: 'Saltan',
    سمية: 'Samaiah',
    ثرية: 'Thriah',
    طه: 'Tah',
    طاهرة: 'Tahirah',
    تاج: 'Taj',
    طلعت: 'Tlat',
    طالبة: 'Talbah',
    طارق: 'Tarq',
    طارق: 'Tarq',
    توفيق: 'Tofiq',
    طيب: 'Taib',
    ثامر: 'Thamar',
    ثريا: 'Thraia',
    طوبى: 'Toba',
    عثمان: 'Athman',
    عظمى: 'Azma',
    وفائي: 'Wfaai',
    وفية: 'Wfiah',
    وحيدة: 'Whaidah',
    وحيدة: 'Whaidah',
    وقار: 'Wqar',
    وسيم: 'Wsaiam',
    وداد: 'Wdad',
    يعقوب: 'Yaqob',
    يعقوب: 'Yaqob',
    ياسين: 'Yasain',
    يسمين: 'Ysamain',
    يسمين: 'Ysamain',
    يوسف: 'Yousf',
    يوحنا: 'Yohana',
    يوسف: 'Yousf',
    يسرى: 'Ysura',
    زاهر: 'Zahir',
    زاهرة: 'Zahirah',
    زاهية: 'Zahiah',
    زاهدة: 'Zahadah',
    ظهيرة: 'Zhirah',
    زهرة: 'Zuhrah',
    زينة: 'Zainah',
    زكريا: 'Zkaraia',
    زكريا: 'Zkaraia',
    ذكية: 'Thkiah',
    ذكية: 'Thkiah',
    زين: 'Zain',
    زينب: 'Zainab',
    زياد: 'Zaiad',
    زينة: 'Zainah',
    'ضياء الدين Dhiaa': 'Aldin',
    زبيدة: 'Zbidah',
    زليخا: 'Zlaikha',
    حصة: 'Hsah',
    ثابت: 'Thabt',
    زياد: 'Zaiad',
    زهراء: 'Zuhraa',
    خالد: 'Khald',
    البندري: 'Albnadri',
    عثمان: 'Athman',
    شيخ: 'Shikh',
    باسل: 'Basal',
    مزمل: 'Mzml',
    معتصم: 'Muatsm',
    وسام: 'Wsam',
    جميلة: 'Jmailah',
    عائشة: 'Aaashah',
    أمل: 'Aml',
    جواهر: 'Jwahir',
    رغد: 'Rghd',
    ليث: 'Laith',
    ضحى: 'Dhuha',
    فادي: 'Fadi',
    باسمة: 'Basamah',
    اكرم: 'Akarm',
    خديجة: 'Khdijah',
    فاطمة: 'Fatmah',
    عجلان: 'Ajlan',
    حرم: 'Harm',
    ريم: 'Riam',
    مروة: 'Maroah',
    ميادة: 'Maiadah',
    إيمان: 'Iiaman',
    لولوة: 'Loloah',
    نورة: 'Noarah',
    جاسم: 'Jasam',
    هيا: 'Hia',
    سلطان: 'Saltan',
    منتظر: 'Muntzr',
    لطفي: 'Ltfi',
    نجاة: 'Najaah',
    لطيفة: 'Ltaifah',
    حمد: 'Hamd',
    ماجدة: 'Majadah',
    موزة: 'Mozah',
    سعدون: 'Saadon',
    عزيزة: 'Azaiazah',
    أمينة: 'Amainah',
    سالم: 'Salam',

    زيغر: 'Zaighr',
    زيواك: 'Zaiwak',
    زيغ: 'Zaigh',
    زيراك: 'Zairak',
    خبلواك: 'Khblwak',
    خوزشيت: 'Khozsheit',
    خيال: 'Khial',
    خاورين: 'Khaoarin',
    خيبر: 'Khibr',
    بانغة: 'Banghah',
    برخة: 'Brkhah',
    بتاسة: 'Btasah',
    بيروزة: 'Birozah',
    توربيكي: 'Toarabiki',
    توتكي: 'Totki',
    تنكي: 'Tinki',
    زانغة: 'Zanghah',
    زلبلة: 'Zlblah',
    زولي: 'Zolai',
    خزانة: 'Khzanah',
    يوغرتن: 'Yoghrtin',
    توفايور: 'Tofaioar',
    تنسن: 'Tinisan',
    تليتماس: 'Tlaitmas',
    تازرزت: 'Tazrzt',
    ميسر: 'Maisr',
    سادن: 'Sadn',
    تحيات: 'Thaiat',
    سينيمان: 'Sainiaman',
    بكوس: 'Bkos',
    إيرات: 'Iirat',
    إتو: 'Ito',
    مسيفسن: 'Msaifsan',
    تامانارت: 'Tamanart',
    تاسافوت: 'Tasafot',
    إزمرسن: 'Izmarsan',
    ماغر: 'Maghr',
    إغدير: 'Ighdir',
    عزة: 'Azah',
    لونجا: 'Lonaja',
    تسفاي: 'Tsfai',
    غرماي: 'Ghrmai',
    أسفا: 'Asfa',
    دقا: 'Dqa',
    برهان: 'Brhan',
    توجو: 'Twjo',
    يوهانس: 'Yohanias',
    منليك: 'Munlaik',
    كيداني: 'Kidanei',
    غيتاتشو: 'Ghitatsho',
    برهي: 'Brhi',
    ستوتو: 'Stoto',
    ألولا: 'Alola',
    ترحاص: 'Trahas',
    هيمانوت: 'Haiamanot',
    تزازو: 'Tzazo',
    مالس: 'Mals',
    هايلي: 'Hailai',
    يوردانوس: 'Yoardanos',
    برخت: 'Brkhit',
    راجا: 'Raja',
    إيشانا: 'Iishana',
    موهان: 'Mohan',
    ديفاكي: 'Difaki',
    راشانا: 'RIshana',
    روهان: 'Rohan',
    غيتا: 'Ghita',
    سانجاي: 'Sanajai',
    رافي: 'Rafii',
    ساتيا: 'Satia',
    فاسانتا: 'Fasanta',
    كافيندرا: 'Kafiinadra',
    لكشمي: 'Lkshmai',
    فيجاي: 'Fijai',
    شاكتي: 'Shakti',
    دارشان: 'Darshan',
    ديفا: 'Difa',
    شاندرا: 'Shanadra',
    أجاي: 'Ajai',
    أنيلا: 'Aneila',
    سارة: 'Sarah',
    راشيل: 'RIshil',
    إستر: 'Istr',
    هانا: 'Hana',
    ميريام: 'Meiraiam',
    ريبيكا: 'Ribika',
    شوشانا: 'Shoshana',
    مالكا: 'Malka',
    ماريا: 'Maraia',
    كلارا: 'Kalara',
    ديبورا: 'Diboara',
    يهوديت: 'Yhodit',
    آنا: 'Ana',
    فريدا: 'Farida',
    بيلا: 'Bila',
    دينا: 'Dina',
    باتيا: 'Batia',
    ريجينا: 'Rijina',
    تحية: 'Thaiah',
    سونيا: 'Saonia',
    نعومي: 'Naomai',
    تمارا: 'Tmara',
    ليزا: 'Laiaza',
    جينيا: 'Jinia',
    كوهن: 'Kohn',
    ليفي: 'Laifi',
    مزراحي: 'Mzrahai',
    كاتز: 'Katz',
    فريدمان: 'Faridman',
    سيغال: 'Saighal',
    أبراهام: 'Abaraham',
    غرينبرغ: 'Ghrinabrgh',
    أشكنازي: 'Ashknazai',
    روزنبرغ: 'Rozinabrgh',
    بيتون: 'Biton',
    ديفيد: 'Difid',
    غولدشتاين: 'Gholdshtain',
    فيلدمان: 'Fildman',
    يوسف: 'Yousf',
    مئير: 'Mair',
    غولدبرغ: 'Gholdbrgh',
    موسى: 'Mosa',
    سليمان: 'Sulaiaman',
    يعقوب: 'Yaqob',
    إسحاق: 'Isahaq',
    ساسون: 'Sasaon',
    جولدمان: 'Joldman',
    روبن: 'Robn',
    ليبرمان: 'Laibrman',
    ناحوم: 'Nahom',
    أدلر: 'Adlr',
    شتاين: 'Shtain',
    هيرش: 'Hirsh',
    عوفاديا: 'Aofadia',
    إسرائيل: 'Israail',
    الباز: 'Albaz',
    إيليا: 'Iilaia',
    عزرا: 'Azra',
    أبراموف: 'Abaramof',
    شموئيل: 'Shmoail',
    هوشنغ: 'Hoshngh',
    فرساد: 'Farsad',
    'جهان ناز Jahan': 'Naz',
    مهرانغيز: 'Muhranghiaz',
    فرشید: 'Farshad',
    شاهزادة: 'Shahzadah',
    زرافشان: 'Zrafshan',
    بريجهرة: 'Brijahrah',
    فاروج: 'Farwj',
    سبنتا: 'Sbnta',
    شیرویه: 'Sharoaah',
    أرفانة: 'Arafanah',
    فرجفند: 'Farjfanad',
    دلیلة: 'Dlalah',
    تبسم: 'Tbsam',
    يزدغرد: 'Yazdghrd',
    جالة: 'Jalah',
    صدري: 'Sadri',
    تشنغيز: 'Tshnghiaz',
    طوسك: 'Tosk',
    مروان: 'Marawan',
    ميرهم: 'Meirhm',
    قاسملو: 'Qasamlo',
    بخشين: 'Bkhshin',
    كرزان: 'Karzan',
    كرسو: 'Karsao',
    بيشنغ: 'Bishngh',
    فينو: 'Fino',
    خانه: 'Khanuah',
    فراشين: 'FarIshin',
    إيويش: 'Iioish',
    إكرام: 'Ikaram',
    فيروز: 'Firoz',
    هادي: 'Hadi',
    حكيم: 'Hakiam',
    غشيلان: 'Ghshilan',
    جورين: 'Joarin',
    كليجان: 'Kalaijan',
    لاليخان: 'Lalaikhan',
    غفو: 'Ghufo',
    جوهر: 'Johr',
    لرزانه: 'Lrzanuah',
    ليفكن: 'Laifikn',
    مارين: 'Marin',
    فردوس: 'Fardos',
    فراش: 'FarIsh',
    ميناس: 'Mainas',
    بيزير: 'Biazair',
    باكان: 'Bakan',
    هيلان: 'Hailan',
    بوريس: 'Boaris',
    بافيل: 'Bafiil',
    فلاديمير: 'Fladiameir',
    نيكولاي: 'Nikolai',
    أليكساندر: 'Alaiksanadr',
    ديميتري: 'Diamaitri',
    أولغا: 'Aolgha',
    بيتر: 'Bitr',
    ناتاليا: 'Natalaia',
    سيرجي: 'Sairji',
    أليكسي: 'Alaiksai',
    كسينيا: 'Ksainia',
    يلينا: 'Ylaina',
    إيكاترينا: 'Iikatrina',
    أندريا: 'Anadraia',
    أليكساندرا: 'Alaiksanadra',
    أدينا: 'Adina',
    ماريا: 'Maraia',
    فاديم: 'Fadiam',
    أوكسانا: 'Aoksana',
    برفيز: 'Brafiaz',
    بينيظير: 'Binizir',
    ميرزا: 'Meirza',
    نسيمة: 'Nisaiamah',
    سردار: 'Srdar',
    'رفيق خان Rafiq': 'Khan',
    افتخار: 'Aftkhar',
    سرتاج: 'Srtaj',
    عفراء: 'Afaraa',
    بهروز: 'Bhroz',
    بانو: 'Bano',
    شريف: 'Shrif',
    'جهانغير خان Jahanghir': 'Khan',
    جميلة: 'Jmailah',
    تشودري: 'Tshodri',
    مشتاق: 'Mshtaq',
    قدير: 'Qadir',
    فرح: 'Farah',
    برفين: 'Brafin',
    نديمة: 'Nadiamah',
    بولنت: 'Bolnt',
    أهميت: 'Ahmait',
    هميرا: 'Hmeira',
    هسين: 'Hsain',
    فكرية: 'Fikariah',
    أيسل: 'Aisal',
    آغا: 'Agha',
    'بنّ ور Bnّ': 'oar',
    هاليل: 'Halail',
    بهلول: 'Bhlol',
    إسمت: 'Isamt',
    'إيفّ ت Iifّ': 't',
    أكشاي: 'Akshai',
    إيزت: 'Iiazt',
    براك: 'Barak',
    أردهان: 'Ardhan',
    إلهان: 'Ilhan',
    إلكر: 'Ilkar',
    بهار: 'Bhar',
    أيبوكه: 'Aibokah',
    المساعفه: 'Alamsaafah',
    الشمري: 'Alshmari',
    'فضل الله Fadhl': 'Allah',
    حفناوي: 'Hfanaoi',
    حسنين: 'Hasanin',
    قائد: 'Qaad',
    الدوسري: 'Aldosri',
    خلايلة: 'Khalailah',
    باعبود: 'Baabod',
    كاظم: 'Kazm',
    الرملاوي: 'Alrmlaoi',
    بوزيان: 'Bozaian',
    الخوري: 'Alkhoari',
    بنسعيد: 'Bnisaaid',
    البشير: 'Albshir',
    العلوي: 'Alaloi',
    الإدريسي: 'Alidrisai',
    مهدي: 'Muhadi',
    فرغلي: 'Farghlai',
    شنب: 'Shnab',
    الشيباني: 'Alshibanei',
    الحربي: 'Aliharabi',
    البلاونة: 'Albilaonah',
    بودربالة: 'Bodrabalah',
    الهيتي: 'Alhiti',
    الهنائي: 'Alhnaai',
    الحمصي: 'Alihamusi',
    البلوشي: 'Albloshi',
    القليوبي: 'Aliqlaiobi',
    الضوء: 'Aldhoa',
    ملاسي: 'Mlasai',
    العسكري: 'Alaskari',
    الفزاني: 'Alfazanei',
    الذيابات: 'Althiabat',
    الكباشي: 'AlkbIshi',
    العجمي: 'Alajmai',
    الشراح: 'Alshrah',
    القلاف: 'Aliqlaf',
    الجيلاني: 'Aljilanei',
    الأخرس: 'Alakhrs',
    البلوشي: 'Albloshi',
    المحروقي: 'Alamuharoqai',
    النجار: 'Alnajar',
    المجيد: 'Alamajid',
    عواملة: 'Awamlah',
    حجو: 'Hajo',
    اللبابيدي: 'Allbabidi',
    يحيى: 'Yhaia',
    تجاني: 'Tjanei',
    التريكي: 'Altriki',
    أبانوب: 'Abanob',
    أبرار: 'Abarar',
    آدم: 'Adam',
    أديب: 'Adib',
    أديبة: 'Adibah',
    أدهم: 'Adhm',
    أفاق: 'Afaq',
    أفنان: 'Afanan',
    أفراح: 'Afarah',
    أحلام: 'Ahlam',
    أحمد: 'Ahamd',
    أجنادين: 'Ajnadin',
    أجود: 'Ajod',
    أكرم: 'Akarm',
    أكثم: 'Akthm',
    آلاء: 'Alaa',
    'البَ راء Albَ': 'raa',
    البشير: 'Albshir',
    ألبرت: 'Albrt',
    ألحان: 'Alhan',
    أليس: 'Alais',
    أليف: 'Alaif',
    ألما: 'Alama',
    'المَ نصور Alamَ': 'nasoar',
    ألمظ: 'Alamz',
    'المُ عتز بالله Alamُ atz': 'Ballah',
    الشيماء: 'Alshiamaa',
    'الوَ ليد Aloَ': 'laid',
    أمل: 'Aml',
    آمال: 'Amal',
    أمانة: 'Amanah',
    أماني: 'Amanei',
    آمنة: 'Amunah',
    أمين: 'Amain',
    أمينة: 'Amainah',
    أمير: 'Ameir',
    أميرة: 'Ameirah',
    أمجد: 'Amjad',
    إنعام: 'Inaam',
    أنس: 'Anias',
    أندرية: 'Anadriah',
    أندرو: 'Anadaro',
    أندراوس: 'Anadraos',
    أنفال: 'Anfal',
    أنجيلا: 'Anajila',
    أنغام: 'Angham',
    أنهار: 'Anuhar',
    أنيس: 'Aneis',
    أنيسة: 'Aneisah',
    أنطوني: 'Antoni',
    أنطوان: 'Antwan',
    أنوار: 'Anwar',
    أنور: 'Anoar',
    أريج: 'Arij',
    أرغد: 'Arghd',
    أرسلان: 'Arsalan',
    أروى: 'Aroa',
    أريام: 'Araiam',
    أصالة: 'Asalah',
    أسيف: 'Asaif',
    أسيل: 'Asail',
    آسر: 'Asr',
    أشجان: 'Ashjan',
    أشهب: 'Ashahb',
    أشرف: 'Ashraf',
    أشرقت: 'Ashrqut',
    آسية: 'Asaiah',
    'أيسِ ل Aisِ': 'l',
    أسينات: 'Asainat',
    أسماء: 'Asamaa',
    أسمهان: 'Asamuhan',
    أسمر: 'Asamar',
    أسرى: 'Asura',
    أسرار: 'Asrar',
    'أوّ اب Aoّ': 'ab',
    أوس: 'Aws',
    آية: 'Aiah',
    آيات: 'Aiat',
    أيمن: 'Aiamun',
    ايوب: 'Aiob',
    أيتن: 'Aitin',
    أزهار: 'Azuhar',
    أزميرالدا: 'Azmeiralda',
    باديس: 'Badis',
    'بَ در Bَ': 'dr',
    بهاء: 'Bhaa',
    باهر: 'Bahir',
    بهي: 'Bhi',
    بهية: 'Bhiah',
    بهيّ: 'Bhiّ',
    بهيجة: 'Bhijah',
    بهجة: 'Bhjah',
    براء: 'Baraa',
    بارين: 'Barin',
    باسل: 'Basal',
    'باسِ مة Basِ': 'mah',
    بشام: 'Bsham',
    باسيل: 'Basail',
    'باسِ م Basِ': 'm',
    'بَ تول Bَ': 'tol',
    'بَ تّ ال Bَ tّ': 'al',
    بدير: 'Badir',
    بجاد: 'Bjad',
    بروين: 'Broin',
    بيشوي: 'Bishoi',
    بيبرس: 'Bibrs',
    بهار: 'Bhar',
    بيلار: 'Bilar',
    بوح: 'Boh',
    بيداء: 'Bidaa',
    إدريس: 'Idris',
    إدوارد: 'Idward',
    إجلال: 'Ijlal',
    إحسان: 'Ihusan',
    إخلاص: 'Ikhalas',
    إيلين: 'Iilain',
    إلهام: 'Ilham',
    إلهامي: 'Ilhamai',
    إليسا: 'Ilaisa',
    إليزابيث: 'Ilaiazabith',
    إلياس: 'Ilaias',
    إمام: 'Imam',
    إيميلي: 'Iiamailai',
    إمتنان: 'Imtinan',
    إنجي: 'Inaji',
    إنصاف: 'Inasaf',
    انتصار: 'Antsar',
    إقبال: 'Iqbal',
    إيريني: 'Iirini',
    اسحاق: 'Asahaq',
    إسراء: 'Israa',
    استفانوس: 'Astfanos',
    إيف: 'Iif',
    ايفلين: 'Aiflain',
    إيفون: 'Iifon',
    إفرونيا: 'Ifaronia',
    'هُ ديّ ن Hُ diّ': 'n',
    إبراهيم: 'Ibarahaiam',
    ابرام: 'Abaram',
    ابتسام: 'Abtsam',
    إبتهال: 'Ibthal',
    إيهاب: 'Iihab',
    إكرام: 'Ikaram',
    إيلاف: 'Iilaf',
    ايمان: 'Aiaman',
    إيناس: 'Iinas',
    إيريس: 'Iiris',
    ارتواء: 'Artwaa',
    إيزابيل: 'Iiazabil',
    اسكندر: 'Asknadr',
    إسلام: 'Isalam',
    إسماعيل: 'Isamaail',
    اعتماد: 'Aatmad',
    إيثار: 'Iithar',
    إياد: 'Iiad',
    إياس: 'Iias',
    نيللي: 'Nillai',
    نيكولا: 'Nikola',
    نورسين: 'Noarsain',
    أوكتافيا: 'Aoktafiia',
    ألفة: 'Alfah',
    أولجا: 'Aolja',
    أوليفيا: 'Aolaifia',
    أمامة: 'Amamah',
    أمنية: 'Amaniah',
    أورهان: 'Aoarhan',
    أسامة: 'Asamah',
    برديس: 'Brdis',
    باريس: 'Baris',
    باتريك: 'Batrik',
    بيتر: 'Bitr',
    بولا: 'Bola',
    بولس: 'Bols',
    'وضّ اء Wdhّ': 'aa',
    وسيم: 'Wsaiam',
    يوكابد: 'Yokabad',
    يونان: 'Yonan',
    عمرو: 'Amaro',
    شيت: 'Sheit',
    اكثم: 'Akthm',
    اننعام: 'Annaam'
};

let tools = {};

tools.firstLetter = index => !index;
tools.convertTheFirstLetters = letter => (firstObj[letter] ? firstObj[letter] : tools.convertLetter(letter));
tools.convertLetter = (letter, index) => (caseObj[letter] ? caseObj[letter] : letter);
tools.checkNextLetter = (letter, nextLetter) =>
    nextObj[letter] != undefined ? (nextObj[letter][nextLetter] ? nextObj[letter][nextLetter] : '') : '';
tools.checkSpecialLetter = (enName, letter, nextLetter) =>
    specialObj[letter] != undefined
        ? specialObj[letter][nextLetter]
            ? specialObj[letter]['action'] == 'slice'
                ? enName.slice(0, -1) + specialObj[letter][nextLetter]
                : enName
            : enName
        : enName;
tools.checkLastLetter = (enName, letter) =>
    lastObj[letter]
        ? lastObj[letter]['action'] == 'slice'
            ? enName.slice(0, -1) + lastObj[letter][letter]
            : enName
        : enName;
tools.isItLastLetter = (index, arName) => index == arName.length - 1;
tools.checkThreeLetters = (firstLetter, secondLetter, thirdLetter) =>
    threeObj[firstLetter]
        ? threeObj[firstLetter][secondLetter]
            ? threeObj[firstLetter][secondLetter][thirdLetter]
                ? threeObj[firstLetter][secondLetter][thirdLetter]
                : ''
            : ''
        : '';
tools.checkMiddleLetters = (middle, beforeLetter, afterLetter) =>
    middleObj[middle]
        ? middleObj[middle][beforeLetter]
            ? middleObj[middle][beforeLetter][afterLetter]
                ? middleObj[middle][beforeLetter][afterLetter]
                : ''
            : ''
        : '';
tools.capitalize = string => string.charAt(0).toUpperCase() + string.slice(1);

const trans = name => {
    if (!name) return '';
    // find name in our db and return it if any
    if (namesdb[name]) return tools.capitalize(namesdb[name]);
    // else
    let enName = '';
    for (let index in name) {
        let i = parseInt(index);
        if (tools.firstLetter(i)) enName = tools.convertTheFirstLetters(name[i]);
        else enName += tools.convertLetter(name[i], i);
        if (!tools.isItLastLetter(i, name)) {
            if (tools.checkThreeLetters(name[i], name[i + 1], name[i + 2]))
                enName += tools.checkThreeLetters(name[i], name[i + 1], name[i + 2]);
            else enName += tools.checkNextLetter(name[i], name[i + 1]);
            enName += tools.checkMiddleLetters(name[i], name[i - 1], name[i + 1]);
            enName = tools.checkSpecialLetter(enName, name[i], name[i + 1]);
        }
    }
    enName = tools.checkLastLetter(enName, name[name.length - 1]);
    return tools.capitalize(enName);
};

export default function toEnName(str) {
    try {
        let finalName = '';
        let array = str.split(' ');

        for (let index in array) {
            let enName = trans(array[index]);

            finalName += enName + ' ';
        }

        return finalName
            .split(' ')
            .map(t => t.trim())
            .filter(t => !!t)
            .join(' ');
    } catch (error) {}

    return str;
}
