import _ from 'lodash';
import {RRule, RRuleSet, rrulestr} from 'rrule';
import {DateTime} from 'framework/date';

export function rrule(input, timezone = 'utc') {
    const rule = parse(input, timezone);

    return {
        all(iterator = null) {
            if (_.isFunction(iterator)) {
                return rule.all(iterator);
            } else {
                return rule.all();
            }
        },
        between(start, end, include = true, iterator = null) {
            start = DateTime.fromJSDate(start).toUTC().toJSDate();
            end = DateTime.fromJSDate(end).toUTC().toJSDate();

            if (_.isFunction(iterator)) {
                return rule.between(start, end, include, iterator).map(fixDate);
            } else {
                return rule.between(start, end, include).map(fixDate);
            }
        },
        before(date, include = true) {
            date = DateTime.fromJSDate(date).toUTC().toJSDate();

            return rule.before(date, include);
        },
        after(date, include = true) {
            date = DateTime.fromJSDate(date).toUTC().toJSDate();

            return rule.after(date, include);
        },
        toString() {
            return rule.toString();
        },
        toText() {
            return rule.toText();
        }
    };
}

export function getRepeatStr(data) {
    const rule = {};

    rule.start = data.start;
    rule.frequency = data.frequency;
    rule.interval = data.interval;

    if (data.frequency === 'weekly' && Array.isArray(data.days)) {
        rule.byWeekDays = data.days;
    } else if (data.frequency === 'monthly') {
        if (data.repeatOnType === 'day') {
            rule.byMonthDay = data.repeatOnDay;
        } else if (data.repeatOnType === 'custom') {
            if (data.repeatOnWeek === 'first') rule.byPosition = 1;
            else if (data.repeatOnWeek === 'second') rule.byPosition = 2;
            else if (data.repeatOnWeek === 'third') rule.byPosition = 3;
            else if (data.repeatOnWeek === 'fourth') rule.byPosition = 4;
            else if (data.repeatOnWeek === 'last') rule.byPosition = -1;

            rule.byWeekDay = data.repeatOnWeekDay;
        }
    } else if (data.frequency === 'yearly') {
        if (data.repeatOnType === 'day') {
            rule.byMonth = data.repeatOnMonth;
            rule.byMonthDay = data.repeatOnDay;
        } else if (data.repeatOnType === 'custom') {
            rule.byMonth = data.repeatOnMonth;

            if (data.repeatOnWeek === 'first') rule.byPosition = 1;
            else if (data.repeatOnWeek === 'second') rule.byPosition = 2;
            else if (data.repeatOnWeek === 'third') rule.byPosition = 3;
            else if (data.repeatOnWeek === 'fourth') rule.byPosition = 4;
            else if (data.repeatOnWeek === 'last') rule.byPosition = -1;

            rule.byWeekDay = data.repeatOnWeekDay;
        }
    }

    if (data.endType === 'at-date') {
        rule.until = data.end;
    } else if (data.endType === 'occurrences') {
        rule.occurrences = data.occurrences;
    }

    return rrule(rule).toString();
}

function parse(input, timezone) {
    if (_.isString(input)) {
        return rrulestr(input);
    } else if (_.isPlainObject(input)) {
        const rule = {};

        // Set timezone.
        if (timezone !== 'utc') {
            rule.tzid = timezone;
        }

        // Frequency.
        if (_.isString(input.frequency)) {
            rule.freq = RRule[input.frequency.toUpperCase()];
        }

        // Start date.
        if (_.isDate(input.start)) {
            rule.dtstart = toUTC(input.start);
        } else {
            rule.dtstart = toUTC(DateTime.local().toJSDate());
        }

        // Interval.
        if (_.isInteger(input.interval)) {
            rule.interval = input.interval;
        }

        // Week first day.
        rule.wkst = RRule.MO;

        // Occurrence.
        if (_.isInteger(input.occurrences)) {
            rule.count = input.occurrences;
        }

        // Until.
        if (_.isDate(input.until)) {
            rule.until = toUTC(input.until);
        }

        // By occurrences position.
        if (!_.isUndefined(input.byPosition)) {
            rule.bysetpos = input.byPosition;
        }

        // By month.
        if (!_.isUndefined(input.byMonth)) {
            rule.bymonth = input.byMonth;
        }

        // By month day.
        if (!_.isUndefined(input.byMonthDay)) {
            rule.bymonthday = input.byMonthDay;
        }

        // By year day.
        if (!_.isUndefined(input.byYearDay)) {
            rule.byyearday = input.byYearDay;
        }

        // By week no.
        if (!_.isUndefined(input.byWeekNo)) {
            rule.byweekno = input.byWeekNo;
        }

        // By week day.
        if (_.isString(input.byWeekDay)) {
            rule.byweekday = RRule[input.byWeekDay.toUpperCase().slice(0, 2)];
        }
        if (Array.isArray(input.byWeekDays)) {
            rule.byweekday = input.byWeekDays.map(day => RRule[day.toUpperCase().slice(0, 2)]);
        }

        // By hour.
        if (!_.isUndefined(input.byHour)) {
            rule.byhour = input.byHour;
        }

        // By minute.
        if (!_.isUndefined(input.byMinute)) {
            rule.byminute = input.byMinute;
        }

        // By second.
        if (!_.isUndefined(input.bySecond)) {
            rule.bysecond = input.bySecond;
        }

        return new RRule(rule);
    }
}

function fixDate(date) {
    const dt = DateTime.fromJSDate(date);

    return dt.minus({minutes: dt.offset}).toJSDate();
}

function toUTC(date) {
    const dt = DateTime.fromJSDate(date);

    return new Date(Date.UTC(dt.year, dt.month - 1, dt.day, dt.hour, dt.minute, dt.second));
}
