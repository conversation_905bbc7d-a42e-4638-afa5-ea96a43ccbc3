import axios from 'axios';

export default async function (xsl, xml, params = {}) {
    const response = await axios({
        method: 'POST',
        // url: 'https://api.entererp.com/v1/common/transform-xslt',
        url: 'http://139.59.138.121:2828',
        headers: {
            'Content-Type': 'application/json'
        },
        data: JSON.stringify({xml, xsl})
    });

    return response.data;
}
