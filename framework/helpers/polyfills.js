if (!String.prototype.startsWith) {
    String.prototype.startsWith = function (searchString, position = 0) {
        return this.indexOf(searchString, position) === position;
    };
}

if (!String.prototype.endsWith) {
    String.prototype.endsWith = function (search, length) {
        if (length === undefined || length > this.length) {
            length = this.length;
        }

        return this.substring(length - search.length, length) === search;
    };
}

if (!String.prototype.trim) {
    String.prototype.trim = function () {
        return this.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, '');
    };
}
