// https://github.com/chrisveness/latlng-geoHash

const base32 = '0123456789bcdefghjkmnpqrstuvwxyz'; // (geoHash-specific) Base32 map

/**
 * GeoHash: <PERSON>’s geocoding system.
 */
export class GeoHash {
    /**
     * Encodes latitude/longitude to geoHash, either to specified precision or to automatically
     * evaluated precision.
     *
     * @param   {number} lat - Latitude in degrees.
     * @param   {number} lng - Longitude in degrees.
     * @param   {number} [precision] - Number of characters in resulting geoHash.
     * @returns {string} GeoHash of supplied latitude/longitude.
     * @throws  Invalid geoHash.
     *
     * @example
     *     const geoHash = GeoHash.encode(52.205, 0.119, 7); // => 'u120fxw'
     */
    static encode(lat, lng, precision) {
        // infer precision?
        if (typeof precision === 'undefined') {
            // refine geoHash until it matches precision of supplied lat/lng
            for (let p = 1; p <= 12; p++) {
                const hash = GeoHash.encode(lat, lng, p);
                const posn = GeoHash.decode(hash);
                if (posn.lat === lat && posn.lng === lng) return hash;
            }
            precision = 12; // set to maximum
        }

        lat = Number(lat);
        lng = Number(lng);
        precision = Number(precision);

        if (isNaN(lat) || isNaN(lng) || isNaN(precision)) throw new Error('Invalid geoHash');

        let idx = 0; // index into base32 map
        let bit = 0; // each char holds 5 bits
        let evenBit = true;
        let geoHash = '';

        let latMin = -90,
            latMax = 90;
        let lngMin = -180,
            lngMax = 180;

        while (geoHash.length < precision) {
            if (evenBit) {
                // bisect E-W longitude
                const lngMid = (lngMin + lngMax) / 2;
                if (lng >= lngMid) {
                    idx = idx * 2 + 1;
                    lngMin = lngMid;
                } else {
                    idx = idx * 2;
                    lngMax = lngMid;
                }
            } else {
                // bisect N-S latitude
                const latMid = (latMin + latMax) / 2;
                if (lat >= latMid) {
                    idx = idx * 2 + 1;
                    latMin = latMid;
                } else {
                    idx = idx * 2;
                    latMax = latMid;
                }
            }
            evenBit = !evenBit;

            if (++bit === 5) {
                // 5 bits gives us a character: append it and start over
                geoHash += base32.charAt(idx);
                bit = 0;
                idx = 0;
            }
        }

        return geoHash;
    }

    /**
     * Decode geoHash to latitude/longitude (location is approximate centre of geoHash cell,
     *     to reasonable precision).
     *
     * @param   {string} geoHash - GeoHash string to be converted to latitude/longitude.
     * @returns {{lat:number, lng:number}} (Center of) geoHashed location.
     * @throws  Invalid geoHash.
     *
     * @example
     *     const latlng = GeoHash.decode('u120fxw'); // => { lat: 52.205, lng: 0.1188 }
     */
    static decode(geoHash) {
        const bounds = GeoHash.bounds(geoHash); // <-- the hard work
        // now just determine the centre of the cell...

        const latMin = bounds.sw.lat,
            lngMin = bounds.sw.lng;
        const latMax = bounds.ne.lat,
            lngMax = bounds.ne.lng;

        // cell centre
        let lat = (latMin + latMax) / 2;
        let lng = (lngMin + lngMax) / 2;

        // round to close to centre without excessive precision: ⌊2-log10(Δ°)⌋ decimal places
        lat = lat.toFixed(Math.floor(2 - Math.log(latMax - latMin) / Math.LN10));
        lng = lng.toFixed(Math.floor(2 - Math.log(lngMax - lngMin) / Math.LN10));

        return {lat: Number(lat), lng: Number(lng)};
    }

    /**
     * Returns SW/NE latitude/longitude bounds of specified geoHash.
     *
     * @param   {string} geoHash - Cell that bounds are required of.
     * @returns {{sw: {lat: number, lng: number}, ne: {lat: number, lng: number}}}
     * @throws  Invalid geoHash.
     */
    static bounds(geoHash) {
        if (geoHash.length === 0) throw new Error('Invalid geoHash');

        geoHash = geoHash.toLowerCase();

        let evenBit = true;
        let latMin = -90,
            latMax = 90;
        let lngMin = -180,
            lngMax = 180;

        for (let i = 0; i < geoHash.length; i++) {
            const chr = geoHash.charAt(i);
            const idx = base32.indexOf(chr);
            if (idx === -1) throw new Error('Invalid geoHash');

            for (let n = 4; n >= 0; n--) {
                const bitN = (idx >> n) & 1;
                if (evenBit) {
                    // longitude
                    const lngMid = (lngMin + lngMax) / 2;
                    if (bitN === 1) {
                        lngMin = lngMid;
                    } else {
                        lngMax = lngMid;
                    }
                } else {
                    // latitude
                    const latMid = (latMin + latMax) / 2;
                    if (bitN === 1) {
                        latMin = latMid;
                    } else {
                        latMax = latMid;
                    }
                }
                evenBit = !evenBit;
            }
        }

        return {
            sw: {lat: latMin, lng: lngMin},
            ne: {lat: latMax, lng: lngMax}
        };
    }

    /**
     * Determines adjacent cell in given direction.
     *
     * @param   {string} geoHash - Cell to which adjacent cell is required.
     * @param   {string} direction - Direction from geoHash (N/S/E/W).
     * @returns {string} Geocode of adjacent cell.
     * @throws  Invalid geoHash.
     */
    static adjacent(geoHash, direction) {
        // based on github.com/davetroy/geoHash-js

        geoHash = geoHash.toLowerCase();
        direction = direction.toLowerCase();

        if (geoHash.length === 0) throw new Error('Invalid geoHash');
        if ('nsew'.indexOf(direction) === -1) throw new Error('Invalid direction');

        const neighbour = {
            n: ['p0r21436x8zb9dcf5h7kjnmqesgutwvy', 'bc01fg45238967deuvhjyznpkmstqrwx'],
            s: ['14365h7k9dcfesgujnmqp0r2twvyx8zb', '238967debc01fg45kmstqrwxuvhjyznp'],
            e: ['bc01fg45238967deuvhjyznpkmstqrwx', 'p0r21436x8zb9dcf5h7kjnmqesgutwvy'],
            w: ['238967debc01fg45kmstqrwxuvhjyznp', '14365h7k9dcfesgujnmqp0r2twvyx8zb']
        };
        const border = {
            n: ['prxz', 'bcfguvyz'],
            s: ['028b', '0145hjnp'],
            e: ['bcfguvyz', 'prxz'],
            w: ['0145hjnp', '028b']
        };

        const lastCh = geoHash.slice(-1); // last character of hash
        let parent = geoHash.slice(0, -1); // hash without last character

        const type = geoHash.length % 2;

        // check for edge-cases which don't share common prefix
        if (border[direction][type].indexOf(lastCh) !== -1 && parent !== '') {
            parent = GeoHash.adjacent(parent, direction);
        }

        // append letter for direction to parent
        return parent + base32.charAt(neighbour[direction][type].indexOf(lastCh));
    }

    /**
     * Returns all 8 adjacent cells to specified geoHash.
     *
     * @param   {string} geoHash - GeoHash neighbours are required of.
     * @returns {{n,ne,e,se,s,sw,w,nw: string}}
     * @throws  Invalid geoHash.
     */
    static neighbours(geoHash) {
        return {
            n: GeoHash.adjacent(geoHash, 'n'),
            ne: GeoHash.adjacent(GeoHash.adjacent(geoHash, 'n'), 'e'),
            e: GeoHash.adjacent(geoHash, 'e'),
            se: GeoHash.adjacent(GeoHash.adjacent(geoHash, 's'), 'e'),
            s: GeoHash.adjacent(geoHash, 's'),
            sw: GeoHash.adjacent(GeoHash.adjacent(geoHash, 's'), 'w'),
            w: GeoHash.adjacent(geoHash, 'w'),
            nw: GeoHash.adjacent(GeoHash.adjacent(geoHash, 'n'), 'w')
        };
    }
}
