import _ from 'lodash';
import {firstUpper, toLower, toUpper} from './str';

export function isPromise(obj) {
    return !!obj && (typeof obj === 'object' || typeof obj === 'function') && typeof obj.then === 'function';
}

export function isGenerator(obj) {
    return obj && typeof obj.next === 'function' && typeof obj.throw === 'function';
}

export function isGeneratorFunction(fn) {
    return typeof fn === 'function' && fn.constructor && fn.constructor.name === 'GeneratorFunction';
}

export function serialPromise(functions) {
    if (functions.some(f => isPromise(f))) {
        functions = functions.map(promise => () => promise);
    }

    return functions.reduce(
        (promise, f) => promise.then(result => f().then(Array.prototype.concat.bind(result))),
        Promise.resolve([])
    );
}

export function template(str, data) {
    // Use custom template delimiters.
    _.templateSettings.escape = /{{([\s\S]+?)}}/g;
    _.templateSettings.interpolate = /{_([\s\S]+?)}}/g;
    _.templateSettings.evaluate = /{#([\s\S]+?)}}/g;

    let compiled = _.template(str);

    return compiled(data);
}

/**
 * Escapes characters in the string that are not safe to use in a RegExp.
 *
 * @param {*} s The string to escape. If not a string, it will be casted to one.
 * @return {string} A RegExp safe, escaped copy of {@code s}.
 * from https://github.com/google/closure-library/blob/master/closure/goog/string/string.js#L1148
 */
export function escapeRegExp(s) {
    return String(s)
        .replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g, '\\$1')
        .replace(/\x08/g, '\\x08');
}

export function orderByAnother(array, orderArray) {
    const key = Object.keys(orderArray[0])[0];
    const order = orderArray.map(i => i[key]);

    array.sort(function (a, b) {
        let A = a[key],
            B = b[key];

        if (order.indexOf(A) > order.indexOf(B)) {
            return 1;
        } else {
            return -1;
        }
    });

    return array;
}

export function rawMongoQuery(query) {
    const omitFields = [
        '$client',
        '$limit',
        '$skip',
        '$search',
        '$select',
        '$sort',
        '$paginate',
        '$disableActiveCheck',
        '$disableInUseCheck',
        '$disableSoftDelete',
        '$disableTranslation',
        '$disableOrdering',
        '$disableBranchCheck',
        '$disablePermissionCheck',
        '$disableCache',
        '$disableLocalCache',
        '$disableWholeCount'
    ];

    if (Array.isArray(query.$and)) {
        query.$and = query.$and.map(q => _.omit(q, omitFields));
    }
    if (Array.isArray(query.$or)) {
        query.$or = query.$or.map(q => _.omit(q, omitFields));
    }

    return _.omit(query, omitFields);
}

export function getMongoSearchQuery(field, search) {
    const query = {$or: []};

    query.$or.push(
        ...[
            {
                [field]: {
                    $regex: toUpper(escapeRegExp(search)),
                    $options: 'i'
                }
            },
            {
                [field]: {
                    $regex: toLower(escapeRegExp(search)),
                    $options: 'i'
                }
            }
        ]
    );

    return query;
}

export function cartesianProduct(elements) {
    let data = new Array(elements.length);

    const result = (function* recursive(position) {
        if (position === elements.length) {
            yield data.join('');
        } else {
            for (let i = 0; i < elements[position].length; ++i) {
                data[position] = elements[position][i];

                yield* recursive(position + 1);
            }
        }
    })(0);

    return [...result];
}

export function cartesian(a) {
    // a = array of array
    let i,
        j,
        l,
        m,
        a1,
        o = [];
    if (!a || a.length === 0) return a;

    a1 = a.splice(0, 1)[0]; // the first array of a
    a = cartesian(a);
    for (i = 0, l = a1.length; i < l; i++) {
        if (a && a.length) for (j = 0, m = a.length; j < m; j++) o.push([a1[i]].concat(a[j]));
        else o.push([a1[i]]);
    }
    return o;
}

export function convertMS(milliseconds) {
    let days, hours, minutes, seconds;

    seconds = Math.floor(milliseconds / 1000);
    minutes = Math.floor(seconds / 60);
    seconds = seconds % 60;
    hours = Math.floor(minutes / 60);
    minutes = minutes % 60;
    days = Math.floor(hours / 24);
    hours = hours % 24;

    return {
        days,
        hours,
        minutes,
        seconds
    };
}

export function isURL(str) {
    const pattern = new RegExp(
        '^(https?:\\/\\/)?' + // protocol
            '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' + // domain name
            '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
            '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*' + // port and path
            '(\\?[;&a-z\\d%_.~+=-]*)?' + // query string
            '(\\#[-a-z\\d_]*)?$',
        'i'
    ); // fragment locator

    return !!pattern.test(str);
}

export function copyToClipboard(str) {
    const el = document.createElement('textarea');
    el.value = str;
    document.body.appendChild(el);
    el.select();
    document.execCommand('copy');
    document.body.removeChild(el);
}

export function stripHtml(html) {
    const tmp = document.createElement('div');

    tmp.innerHTML = html;

    return tmp.textContent || tmp.innerText || '';
}

export function flatten(data) {
    const result = {};

    function recurse(cur, prop) {
        if (Object(cur) !== cur) {
            result[prop] = cur;
        } else if (Array.isArray(cur)) {
            for (var i = 0, l = cur.length; i < l; i++) recurse(cur[i], prop + '[' + i + ']');
            if (l === 0) result[prop] = [];
        } else {
            var isEmpty = true;
            for (var p in cur) {
                isEmpty = false;
                recurse(cur[p], prop ? prop + '.' + p : p);
            }
            if (isEmpty && prop) result[prop] = {};
        }
    }

    recurse(data, '');

    return result;
}

export function getFormattedAddress(country, address) {
    let formatted = '';

    formatted = template(country.addressFormat, {
        subDistrict: address.subDistrict,
        street: address.street,
        apartmentNumber: address.apartmentNumber,
        doorNumber: address.doorNumber,
        postalCode: address.postalCode,
        district: address.district,
        city: address.city,
        country: country.name
    });

    formatted = formatted.trim();
    formatted = formatted.replace(' /', ' ');
    formatted = formatted.replace('/ ', ' ');
    formatted = formatted.replace('/,', ',');
    formatted = formatted.replace(',/', ',');
    formatted = formatted.replace('No: ,', ',');
    formatted = formatted
        .split(' ')
        .filter(part => !_.isEmpty(part.trim()) && part.trim() !== '/')
        .join(' ');
    formatted = formatted
        .split(',')
        .filter(part => !_.isEmpty(part.trim()) && part.trim() !== '/')
        .join(',');
    formatted = formatted.trim();
    if (formatted[0] === '/') {
        formatted = formatted.slice(1);
    }

    return formatted;
}

export const simpleHash = str => {
    let hash = 5381;
    let i = str.length;

    while (i) {
        hash = (hash * 33) ^ str.charCodeAt(--i);
    }

    /* JavaScript does bitwise operations (like XOR, above) on 32-bit signed
     * integers. Since we want the results to be always positive, convert the
     * signed int to an unsigned by doing an unsigned bitshift. */
    return hash >>> 0;
};

export function generateRandomBarcode(barcodeLength = 13) {
    const requiredLength = barcodeLength - 1;
    let gtin = new Date().getTime().toString().slice(-requiredLength);

    if (requiredLength > gtin.length) {
        const diff = barcodeLength - gtin.length;
        const start = parseInt('1' + '0'.repeat(diff - 1));
        const end = parseInt('9'.repeat(diff));

        gtin += _.random(start, end);
    }

    return gtin + calculateCheckDigit(gtin);
}

function calculateCheckDigit(gtin) {
    let data,
        sum = 0,
        checkDigit;

    gtin = Number(gtin);

    if (isNaN(gtin)) {
        return new Error('Argument' + gtin + 'is not valid type number');
    }

    data = ('' + gtin).split('').reverse();

    for (let i = 0; i < data.length; i++) {
        let num = parseInt(data[i]);

        if (i % 2) {
            sum += num;
        } else {
            sum += num * 3;
        }
    }

    checkDigit = Math.ceil(sum / 10) * 10 - sum;

    return checkDigit;
}
