import DeepDiff from 'deep-diff';
import Big from 'big.js';
import './polyfills';

export * from './arabic';
export * from './regexp';
export * from './clean-html';
export * from './str';
export * from './schemas';
export * from './load-google-api';
export {default as hash} from 'object-hash';
export * from './geohash';
export * from './calculate-distance';
export * from './normalize-url';
export * from './rrule';
export * from './general';
export {default as traverse} from 'traverse';
export const deepDiff = DeepDiff.diff;
export const big = Big;

export const sleep = duration => new Promise(resolve => setTimeout(resolve, duration));
