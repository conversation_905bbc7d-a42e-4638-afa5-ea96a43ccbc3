import _ from 'lodash';

let loader = null;

export function loadGoogleAPI(options = {}) {
    if (loader) {
        return loader;
    }

    const callbackName = '__googleMapsApiOnLoadCallback';

    options = _.defaults(options, {
        // libraries: ['places', 'drawing', 'geometry', 'visualization'],
        libraries: ['places', 'geometry'],
        version: '3',
        timeout: 10000
    });

    loader = new Promise((resolve, reject) => {
        // Exit if not running inside a browser.
        if (typeof window === 'undefined') {
            return reject(new Error('Can only load the Google Maps API in the browser'));
        }

        // Check whether API is already loaded.
        if (window.google && window.google.maps) {
            resolve(window.google.maps);
        } else {
            // Prepare the `script` tag to be inserted into the page.
            let scriptElement = document.createElement('script');
            let params = ['callback=' + callbackName];
            if (options.client) params.push('client=' + options.client);
            if (options.key) params.push('key=' + options.key);
            if (options.language) params.push('language=' + options.language);
            if (options.libraries.length) params.push('libraries=' + options.libraries.join(','));
            if (options.version) params.push('v=' + options.version);
            scriptElement.src = 'https://maps.googleapis.com/maps/api/js?' + params.join('&');
            scriptElement.setAttribute('async', '');
            scriptElement.setAttribute('defer', '');

            // Timeout if necessary.
            let timeoutId = null;
            if (options.timeout) {
                timeoutId = setTimeout(function () {
                    // Set the on load callback to a no-op.
                    window[callbackName] = _.noop;

                    reject(new Error('Could not load the Google Maps API'));
                }, options.timeout);
            }

            // Hook up the on load callback.
            window[callbackName] = function () {
                if (timeoutId !== null) {
                    window.clearTimeout(timeoutId);
                }

                resolve(window.google.maps);

                delete window[callbackName];
            };

            // Insert the `script` tag.
            document.body.appendChild(scriptElement);
        }
    });

    return loader;
}
