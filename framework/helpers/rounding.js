import Big from 'big.js';

export function roundToFull(number, params) {
    const DOWN = 0;
    const HALF_UP = 1;
    const HALF_EVEN = 2;
    const UP = 3;

    if (params.method === 'to-full-decimal-amount') {
        if (params.rule === 'to-closest') {
            return Number(Big(number).round(1, HALF_UP));
        } else if (params.rule === 'up') {
            return Number(Big(number).round(1, UP));
        } else if (params.rule === 'down') {
            return Number(Big(number).round(1, DOWN));
        }
    } else if (params.method === 'to-full-amount') {
        if (params.rule === 'to-closest') {
            return Number(Big(number).round(0, HALF_UP));
        } else if (params.rule === 'up') {
            return Number(Big(number).round(0, UP));
        } else if (params.rule === 'down') {
            return Number(Big(number).round(0, DOWN));
        }
    } else if (params.method === 'to-full-tens-amount') {
        if (params.rule === 'to-closest') {
            return Number(Big(number).round(-1, HALF_UP));
        } else if (params.rule === 'up') {
            return Number(Big(number).round(-1, UP));
        } else if (params.rule === 'down') {
            return Number(Big(number).round(-1, DOWN));
        }
    } else if (params.method === 'fixed-ending') {
        return roundToFixedEnding(number, params.mask, params.rule);
    }
}

export function roundToFixedEnding(number, mask = 'xxx.xx', rule = 'to-closest') {
    const maskAsNumber = parseFloat(
        mask
            .split('')
            .filter(s => s !== 'x')
            .join('')
    );

    if (isNaN(maskAsNumber)) {
        throw new Error('Invalid rounding definition!');
    }

    if (maskAsNumber > number) {
        if (rule === 'to-closest') {
            const lowerDiff = number;
            const upperDiff = maskAsNumber - number;

            if (upperDiff >= lowerDiff) {
                return 0;
            } else {
                return maskAsNumber;
            }
        } else if (rule === 'up') {
            return maskAsNumber;
        } else if (rule === 'down') {
            return 0;
        }
    } else if (maskAsNumber === number) {
        return number;
    } else {
        let factor = null;

        if (mask[0] !== 'x') {
            factor = 1000;
        } else if (mask[1] !== 'x') {
            factor = 100;
        } else if (mask[2] !== 'x') {
            factor = 10;
        } else if (mask[4] !== 'x') {
            factor = 1;
        } else {
            factor = 0.1;
        }

        const numberAsStringArray = number.toFixed(2).split('');
        let firstWrite = false;
        if (mask[0] !== 'x') {
            numberAsStringArray[numberAsStringArray.length - 6] = mask[0];

            firstWrite = true;
        }
        if (mask[1] !== 'x' || firstWrite) {
            numberAsStringArray[numberAsStringArray.length - 5] = mask[1];

            firstWrite = true;
        }
        if (mask[2] !== 'x' || firstWrite) {
            numberAsStringArray[numberAsStringArray.length - 4] = mask[2];

            firstWrite = true;
        }
        if (mask[4] !== 'x' || firstWrite) {
            numberAsStringArray[numberAsStringArray.length - 2] = mask[4];
        }
        numberAsStringArray[numberAsStringArray.length - 1] = mask[5];

        const newNumber = parseFloat(numberAsStringArray.join(''));

        if (newNumber > number) {
            const lowerNumber = newNumber - factor;

            if (rule === 'to-closest') {
                const lowerDiff = Math.abs(number - lowerNumber);
                const upperDiff = Math.abs(newNumber - number);

                if (upperDiff >= lowerDiff) {
                    return lowerNumber;
                } else {
                    return newNumber;
                }
            } else if (rule === 'up') {
                return newNumber;
            } else if (rule === 'down') {
                return lowerNumber;
            }
        } else if (newNumber === number) {
            return newNumber;
        } else {
            const upperNumber = newNumber + factor;

            if (rule === 'to-closest') {
                const lowerDiff = Math.abs(number - newNumber);
                const upperDiff = Math.abs(upperNumber - number);

                if (upperDiff > lowerDiff) {
                    return newNumber;
                } else {
                    return upperNumber;
                }
            } else if (rule === 'up') {
                return upperNumber;
            } else if (rule === 'down') {
                return newNumber;
            }
        }
    }
}
