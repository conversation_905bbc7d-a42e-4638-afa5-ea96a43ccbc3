import Handlebars from 'handlebars';
import asyncHelpers from './handlebars-async-helpers';
import {DOMImplementation, XMLSerializer} from '@xmldom/xmldom';
import JsBarcode from 'jsbarcode';
import QRCode from 'qrcode';
import sharp from 'sharp';
import {firstUpper, firstUpperAll, toLower, toUpper} from './str';

export default async function compile(app, content, data = {}) {
    const hb = asyncHelpers(Handlebars);
    const locale = data.locale || app.config('app.locale');

    hb.registerHelper('trans', function (str, options) {
        return app.translate(str, locale, options.hash || {});
    });
    hb.registerHelper('setting', function (key, options) {
        return app.setting(key);
    });
    hb.registerHelper('round', function (number, precision = 2) {
        return app.roundNumber(number, precision);
    });
    hb.registerHelper('format', function (number, format) {
        return app.format(number, format);
    });
    hb.registerHelper('lineNumber', function (index, options) {
        return parseInt(index) + 1;
    });
    hb.registerHelper('eq', function (a, b, options) {
        return a === b;
    });
    hb.registerHelper('lt', function (a, b, options) {
        return a < b;
    });
    hb.registerHelper('lte', function (a, b, options) {
        return a <= b;
    });
    hb.registerHelper('gt', function (a, b, options) {
        return a > b;
    });
    hb.registerHelper('gte', function (a, b, options) {
        return a >= b;
    });
    hb.registerHelper('notEQ', function (a, b, options) {
        return a !== b;
    });
    hb.registerHelper('ne', function (a, b, options) {
        return a !== b;
    });
    hb.registerHelper('sum', function (a, b, options) {
        return a + b;
    });
    hb.registerHelper('subtract', function (a, b, options) {
        return a - b;
    });
    hb.registerHelper('multiply', function (a, b, options) {
        return a * b;
    });
    hb.registerHelper('divide', function (a, b, options) {
        return a / b;
    });
    hb.registerHelper('toUpper', function (str, options) {
        return toUpper(str);
    });
    hb.registerHelper('toLower', function (str, options) {
        return toLower(str);
    });
    hb.registerHelper('firstUpper', function (str, options) {
        return firstUpper(str);
    });
    hb.registerHelper('firstUpperAll', function (str, options) {
        return firstUpperAll(str);
    });
    hb.registerHelper('slice', function (value, start, end, options) {
        if (typeof value === 'string' || Array.isArray(value)) {
            return value.slice(start, (end = typeof end === 'object' ? undefined : end));
        }
        return value;
    });
    hb.registerHelper('split', function (value, delimiter, index, options) {
        if (typeof value === 'string' && typeof delimiter === 'string') {
            const parts = value.split(delimiter);
            if (typeof index === 'number') {
                return parts[index] || '';
            }
            return parts;
        }
        return value;
    });
    hb.registerHelper('join', function (array, delimiter, options) {
        if (Array.isArray(array) && typeof delimiter === 'string') {
            return array.join(delimiter);
        }
        return array;
    });
    hb.registerHelper('ifCond', function (v1, operator, v2, options) {
        const operators = {
            '==': (a, b) => a == b,
            '===': (a, b) => a === b,
            '!=': (a, b) => a != b,
            '!==': (a, b) => a !== b,
            '<': (a, b) => a < b,
            '<=': (a, b) => a <= b,
            '>': (a, b) => a > b,
            '>=': (a, b) => a >= b,
            '&&': (a, b) => a && b,
            '||': (a, b) => a || b
        };

        if (operators[operator]) {
            return operators[operator](v1, v2) ? options.fn(this) : options.inverse(this);
        } else {
            return options.inverse(this);
        }
    });
    hb.registerHelper('generateBarcode', async function (str, options) {
        const xmlSerializer = new XMLSerializer();
        const document = new DOMImplementation().createDocument('http://www.w3.org/1999/xhtml', 'html', null);
        const svgNode = document.createElementNS('http://www.w3.org/2000/svg', 'svg');

        JsBarcode(svgNode, str, {
            xmlDocument: document,
            height: options.height ?? 120,
            margin: 0,
            background: '',
            displayValue: options.hash.displayValue === true
        });

        const base64 = Buffer.from(
            await sharp(Buffer.from(xmlSerializer.serializeToString(svgNode)))
                .png()
                .toBuffer()
        ).toString('base64');

        return `data:image/png;base64,${base64}`;
    });
    hb.registerHelper('generateQR', async function (str, options) {
        return QRCode.toDataURL(str, options ?? {});
    });

    hb.registerHelper('cleanString', function (input, options) {
        function cleanString(input) {
            if (typeof input !== 'string') return input;
            return input
                .replace(/\s*\(.*?\)\s*/g, ' ')
                .replace(/%\s*\d+/g, '')
                .replace(/\s+/g, ' ')
                .trim();
        }

        return cleanString(input);
    });

    //await

    const template = hb.compile(content || '');

    return await template(data);
}
