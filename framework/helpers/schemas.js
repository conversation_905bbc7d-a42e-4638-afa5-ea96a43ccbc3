export const addressSchema = {
    address: {
        type: 'string',
        label: 'Address',
        required: false
    },
    directions: {
        type: 'string',
        label: 'Directions',
        required: false
    },
    countryId: {
        type: 'string',
        label: 'Country'
    },
    state: {
        type: 'string',
        label: 'State',
        required: false
    },
    city: {
        type: 'string',
        label: 'City',
        required: false
    },
    district: {
        type: 'string',
        label: 'District',
        required: false
    },
    subDistrict: {
        type: 'string',
        label: 'Sub-District',
        required: false
    },
    street: {
        type: 'string',
        label: 'Street',
        required: false
    },
    doorNumber: {
        type: 'string',
        label: 'Door number',
        required: false
    },
    apartmentNumber: {
        type: 'string',
        label: 'Apartment number',
        required: false
    },
    postalCode: {
        type: 'string',
        label: 'Postal code',
        required: false
    }
};
