import os from 'os';
import _ from 'lodash';
import whois from 'whois-ux';

export default async function lookupIp(ip) {
    let data = null;

    if (os.platform() === 'linux') {
        try {
            data = await new Promise((resolve, reject) => {
                whois.whois(ip, (e, d) => {
                    if (!!e) {
                        return reject(e);
                    }

                    resolve(d);
                });
            });
        } catch (error) {
            data = null;
        }
    }

    if (_.isPlainObject(data)) {
        const normalized = {};
        for (const key of Object.keys(data)) {
            normalized[_.camelCase(key)] = data[key];
        }

        const result = {
            ip,
            isp: '',
            countryCode: '',
            city: '',
            address: ''
        };

        if (typeof normalized['orgName'] === 'string') {
            result.isp = normalized['orgName'].trim();
        } else if (typeof normalized['orgname'] === 'string') {
            result.isp = normalized['orgname'].trim();
        } else if (Array.isArray(normalized['descr']) && normalized['descr'].length > 0) {
            result.isp = normalized['descr'][0];
        }

        if (typeof normalized['country'] === 'string') {
            result.countryCode = normalized['country'];
        }

        if (typeof normalized['city'] === 'string') {
            result.city = normalized['city'];
        } else if (Array.isArray(normalized['address']) && normalized['address'].length >= 3) {
            result.city = normalized['address'][2];
        }

        if (typeof normalized['address'] === 'string') {
            result.address = normalized['address'];
        } else if (Array.isArray(normalized['address']) && normalized['address'].length > 0) {
            result.address = _.uniq(normalized['address']).join(' ');
        }

        return result;
    }

    return null;
}
