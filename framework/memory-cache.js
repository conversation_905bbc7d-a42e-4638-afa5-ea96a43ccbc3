import {LRUCache} from 'lru-cache';
import fastCopy from 'fast-copy';

export default class MemoryCache {
    constructor() {
        this.cache = new LRUCache({
            max: 10000
        });
    }

    get(key, defaultValue = null) {
        return fastCopy(this.cache.get(key)) ?? fastCopy(defaultValue);
    }

    has(key) {
        return this.cache.has(key);
    }

    set(key, value, ttl = 0) {
        if (!ttl) {
            this.cache.set(key, fastCopy(value));
        } else {
            this.cache.set(key, fastCopy(value), {
                ttl: ttl * 1000
            });
        }
    }

    delete(key) {
        this.cache.delete(key);
    }

    clear(pattern = '') {
        if (!pattern) {
            this.cache.clear();
        } else {
            for (const key of this.cache.keys()) {
                if (typeof key === 'string' && key.startsWith(pattern)) {
                    this.cache.delete(key);
                }
            }
        }
    }
}
