import _ from 'lodash';
import {NotAuthenticated} from '@feathersjs/errors';
import Redis from 'ioredis';

export default function () {
    const tokensMap = {};

    const redis = new Redis();
    const logoutEventKey = 'user-logout';
    redis.on('message', (e, accessToken) => {
        if (e.toString() === logoutEventKey) {
            delete tokensMap[accessToken.toString()];
        }
    });
    redis.subscribe(logoutEventKey);

    return async (context, _next) => {
        const next = typeof _next === 'function' ? _next : async () => context;
        const {app, params, type, path, service} = context;
        const {provider, authentication} = params;
        const authService = app.defaultAuthentication();

        if (type && type !== 'before') {
            throw new NotAuthenticated('The authenticate hook must be used as a before hook');
        }

        if (!authService || typeof authService.authenticate !== 'function') {
            throw new NotAuthenticated('Could not find a valid authentication service');
        }

        if (service === authService) {
            throw new NotAuthenticated('The authenticate hook does not need to be used on the authentication service');
        }

        if (params.authenticated === true) {
            return next();
        }

        if (authentication) {
            const {accessToken} = authentication;
            const cached = tokensMap[accessToken];
            let authResult = null;

            if (typeof cached !== 'undefined') {
                const {
                    authentication: {
                        payload: {exp}
                    }
                } = cached;
                const duration = exp * 1000 - Date.now();

                if (duration > 0) {
                    authResult = cached;
                } else {
                    delete tokensMap[accessToken];
                }
            }

            if (authResult === null) {
                const authParams = _.omit(params, 'provider', 'authentication');

                authResult = await authService.authenticate(authentication, authParams, 'jwt');
                tokensMap[accessToken] = authResult;
            }

            context.params = Object.assign({}, params, _.omit(authResult, 'accessToken'), {authenticated: true});
        } else if (provider) {
            throw new NotAuthenticated('Not authenticated');
        }

        return next();
    };
}
