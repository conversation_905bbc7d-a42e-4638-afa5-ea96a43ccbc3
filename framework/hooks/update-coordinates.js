import _ from 'lodash';
import GoogleMaps from '@google/maps';

export default function updateCoordinates(collection) {
    return context => {
        if (context.params.disableUpdateCoordinates) {
            delete context.params.disableUpdateCoordinates;

            return context;
        }

        if (context.app.get('isSeeding') === true) {
            return context;
        }

        const app = context.app;
        const googleMapsApiKey = app.setting('system.googleMapsApiKey');

        if (_.isString(googleMapsApiKey) && !!googleMapsApiKey.trim()) {
            const googleMapsClient = GoogleMaps.createClient({
                key: googleMapsApiKey.trim(),
                Promise
            });

            (Array.isArray(context.result) ? context.result : [context.result]).forEach(async item => {
                if (_.isObject(item.address) && _.isString(item.address.address) && item.address.address.length > 0) {
                    const address = item.address.address;

                    try {
                        const response = (await googleMapsClient.geocode({address}).asPromise()).json;

                        if (response.status === 'OK' && response.results.length > 0) {
                            const result = response.results[0];
                            const latitude = result.geometry.location.lat;
                            const longitude = result.geometry.location.lng;

                            if (_.isNumber(latitude) && _.isNumber(longitude)) {
                                app.collection(collection).patch(
                                    {_id: item._id},
                                    {
                                        'coordinates.latitude': latitude,
                                        'coordinates.longitude': longitude
                                    },
                                    {disableUpdateCoordinates: true}
                                );
                            }
                        }
                    } catch (e) {
                        console.error(e);
                    }
                }
            });
        }

        return context;
    };
}
