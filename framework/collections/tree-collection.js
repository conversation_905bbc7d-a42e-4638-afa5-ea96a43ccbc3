import _ from 'lodash';
import fastCopy from 'fast-copy';
import {leadingZeros} from 'framework/helpers';
import Collection from './collection';

function tokenize(str) {
    return str
        .trim()
        .replace(/[^A-Za-z0-9- ]/g, '')
        .replace(/[ ]{1,}/g, '-')
        .toUpperCase();
}

export default class TreeCollection extends Collection {
    async createNode(payload, params = {}) {
        const hasDisplayPath = !!this.schema.path && !!this.schema.name;
        const nodes = [];
        let path = null,
            last = '';

        if (!!payload.path && payload.path !== '') {
            path = payload.path;
            delete payload.path;
        } else {
            if (!!payload.parentId || !!payload.parentPath) {
                let parent = null;

                if (!!payload.parentId) {
                    parent = await this.findOne({
                        _id: payload.parentId,
                        $select: ['tree']
                    });

                    delete payload.parentId;
                } else if (!!payload.parentPath) {
                    parent = await this.findOne({
                        'tree.path': payload.parentPath,
                        $select: ['tree']
                    });

                    delete payload.parentPath;
                }

                if (parent !== null) {
                    let children = await this.children(parent.tree.path, {
                        $select: ['tree']
                    });
                    let current = 1;

                    if (children.length > 0) {
                        children = _.sortBy(children, child => parseInt(child.tree.current, 10));

                        let lastIndex = parseInt(children[children.length - 1].tree.current);

                        current = lastIndex + 1;
                    }

                    path = `${parent.tree.path}/${leadingZeros(current, 8)}`;
                } else {
                    throw new Error('Parent not found!');
                }
            } else {
                let parents = await this.find({
                    'tree.parent': '',
                    $sort: {'tree.current': 1},
                    $select: ['tree']
                });
                let current = 1;

                if (parents.length > 0) {
                    parents = _.sortBy(parents, p => parseInt(p.tree.current, 10));

                    let lastIndex = parseInt(parents[parents.length - 1].tree.current);

                    current = lastIndex + 1;
                }

                path = leadingZeros(current, 8);
            }
        }

        path.split('/').forEach((subPath, depth) => {
            subPath = tokenize(subPath);

            nodes.push(
                _.assign(fastCopy(payload) || {}, {
                    tree: {
                        current: subPath,
                        parent: last.replace(/\/$/, ''),
                        path: last + subPath,
                        hasChild: false,
                        depth
                    }
                })
            );

            last = last + subPath + '/';
        });

        let result = null;
        for (let node of nodes) {
            if ((await this.count({'tree.path': node.tree.path})) < 1) {
                if (hasDisplayPath && !!node.name) {
                    if (!!node.tree.parent) {
                        const parent = await this.findOne({
                            'tree.path': node.tree.parent,
                            $select: ['path']
                        });

                        if (_.isPlainObject(parent)) {
                            node.path = `${parent.path}/${node.name}`;
                        } else {
                            node.path = node.name;
                        }
                    } else {
                        node.path = node.name;
                    }
                }

                result = await this.create(node, {skipEvents: true});
            }
        }

        await this.patch(
            {'tree.path': {$in: nodes.slice(0, -1).map(n => n.tree.path)}},
            {
                'tree.hasChild': true
            },
            {skipEvents: true}
        );

        if (!params.skipEvents) {
            this.emit('created', {
                _id: result._id,
                collectionName: this.name,
                type: 'create',
                isLocalCached: this.isLocalCached
            });
        }

        return result;
    }

    async updateNode(id, payload) {
        const node = await this.findOne(
            {
                _id: id,
                $select: ['name', 'path', 'tree']
            },
            {disableSoftDelete: true, disableActiveCheck: true}
        );
        const hasDisplayPath = !!this.schema.path && !!this.schema.name;
        const self = this;
        let parentPath = '';

        if (payload.parentId) {
            const parent = await this.findOne({
                _id: payload.parentId,
                $select: ['tree']
            });

            parentPath = parent.tree.path;

            delete payload.parentId;
        } else if (!_.isUndefined(payload.parentPath)) {
            parentPath = payload.parentPath !== null ? payload.parentPath : '';

            delete payload.parentPath;
        }

        if (parentPath !== '' && !!parentPath) {
            const operations = [];
            let path = node.tree.path;

            if (hasDisplayPath && (!!node.name || !!payload.name)) {
                if (!!node.tree.parent) {
                    const parent = await this.findOne({
                        'tree.path': parentPath !== node.tree.parent ? parentPath : node.tree.parent,
                        $select: ['path']
                    });

                    if (_.isPlainObject(parent)) {
                        payload.path = !!payload.name
                            ? `${parent.path}/${payload.name}`
                            : `${parent.path}/${node.name}`;
                    } else {
                        payload.path = !!payload.name ? payload.name : node.name;
                    }
                } else {
                    payload.path = !!payload.name ? payload.name : node.name;
                }
            }

            if (parentPath !== node.tree.parent) {
                let children = await this.children(parentPath, {
                    $select: ['tree']
                });
                let lastIndex = 1;
                if (children.length > 0) {
                    children = _.sortBy(children, child => parseInt(child.tree.current, 10));

                    lastIndex = parseInt(children[children.length - 1].tree.current) + 1;
                }

                path = leadingZeros(lastIndex, 8);
                if (parentPath) path = `${parentPath}/${path}`;

                const parts = path.split('/');
                const tree = {};
                tree.current = parts[parts.length - 1];
                tree.parent = parentPath || '';
                tree.path = path;
                tree.depth = parts.length - 1;
                tree.hasChild = (await this.count({'tree.parent': node.tree.path})) > 0;

                operations.push({
                    updateOne: {
                        filter: {_id: node._id},
                        update: {$set: {...payload, tree}}
                    }
                });
            } else {
                operations.push({
                    updateOne: {
                        filter: {_id: node._id},
                        update: {$set: _.omit(payload, 'tree')}
                    }
                });
            }

            // Get display path.
            const displayPath = payload.path;

            // Update children tree structure.
            await (async function recurse(oldParentPath, newParentPath, parentDisplayPath) {
                const children = _.sortBy(
                    await self.children(oldParentPath, {
                        $select: ['name', 'path', 'tree']
                    }),
                    child => parseInt(child.tree.current, 10)
                );

                let lastIndex = 1;
                for (const child of children) {
                    const path = `${newParentPath}/${leadingZeros(lastIndex, 8)}`;
                    const parts = path.split('/');
                    const $set = {
                        'tree.current': parts[parts.length - 1],
                        'tree.parent': newParentPath,
                        'tree.path': path,
                        'tree.depth': parts.length - 1
                    };

                    if (hasDisplayPath && !!parentDisplayPath && !!child.name) {
                        $set.path = `${parentDisplayPath}/${child.name}`;
                    }

                    operations.push({
                        updateOne: {
                            filter: {_id: child._id},
                            update: {$set}
                        }
                    });

                    lastIndex++;

                    if (child.tree.hasChild) {
                        if (hasDisplayPath && !!parentDisplayPath && !!child.name) {
                            await recurse(child.tree.path, path, `${parentDisplayPath}/${child.name}`);
                        } else {
                            await recurse(child.tree.path, path);
                        }
                    }
                }
            })(node.tree.path, path, displayPath);

            // Persist.
            if (operations.length > 0) {
                await this.bulkWrite(operations, {
                    ordered: true,
                    skipEvents: true
                });
            }

            // Update new parent hasChild field.
            if (parentPath !== node.tree.parent) {
                const result = await this.patch({'tree.path': parentPath}, {'tree.hasChild': true}, {skipEvents: true});

                this.emit('patched', {
                    _id: result._id,
                    collectionName: this.name,
                    type: 'patch',
                    isLocalCached: this.isLocalCached
                });
            }

            // Update old parent hasChild field.
            if (!!node.tree.parent && parentPath !== node.tree.parent) {
                const childrenCount = await this.count({
                    'tree.parent': node.tree.parent
                });

                if (childrenCount < 1) {
                    const result = await this.patch(
                        {'tree.path': node.tree.parent},
                        {'tree.hasChild': false},
                        {skipEvents: true}
                    );

                    this.emit('patched', {
                        _id: result._id,
                        collectionName: this.name,
                        type: 'patch',
                        isLocalCached: this.isLocalCached
                    });
                }
            }

            // Get result.
            const result = await this.get(node._id);

            // Send patched event.
            this.emit('patched', {
                _id: result._id,
                collectionName: this.name,
                type: 'patch',
                isLocalCached: this.isLocalCached
            });

            return result;
        } else {
            payload.path = !!payload.name ? payload.name : node.name;

            if (_.isPlainObject(payload.tree) && _.isNull(payload.tree.parent)) {
                payload.tree.parent = '';
            }

            const result = await this.patch({_id: id}, payload, {
                skipEvents: true
            });

            this.emit('patched', {
                _id: result._id,
                collectionName: this.name,
                type: 'patch',
                isLocalCached: this.isLocalCached
            });

            return result;
        }
    }

    async ancestors(path, query = {}) {
        query = this.processQuery(query);

        const nodes = [];

        const recurse = async node => {
            if (typeof node.tree.parent === 'string' && node.tree.parent.trim() !== '') {
                const parent = await this.findOne(_.assign(query, {'tree.path': node.tree.parent}));

                if (parent) {
                    nodes.push(parent);

                    await recurse(parent);
                }
            }
        };

        const result = await this.findOne(_.assign(query, {'tree.path': path}));

        if (result) {
            await recurse(result);
        }

        return nodes.reverse();
    }

    async descendants(path = '', query = {}) {
        query = this.processQuery(query);

        const nodes = [];

        const recurse = async node => {
            if (node.hasChild) {
                const children = await this.children(node.tree.path, query);

                for (const child of children) {
                    nodes.push(child);

                    await recurse(child);
                }
            }
        };

        const result = await this.findOne(_.assign(query, {'tree.path': path}));

        if (result) {
            await recurse(result);
        }

        return nodes;
    }

    async parent(path = '', query = {}) {
        query = this.processQuery(query);

        const node = await this.findOne({'tree.path': path, $select: ['tree']});

        return await this.findOne(_.assign(query, {'tree.path': node.tree.parent}));
    }

    async children(path = '', query = {}) {
        query = this.processQuery(query);

        return await this.find(_.assign(query, {'tree.parent': path, $sort: {'tree.current': 1}}));
    }

    async siblings(path, query = {}) {
        query = this.processQuery(query);

        const node = await this.findOne({'tree.path': path, $select: ['tree']});

        return await this.find(
            _.assign(query, {
                'tree.parent': node.tree.parent,
                'tree.path': {$ne: node.tree.path}
            })
        );
    }

    async remove(query = {}, params = {}) {
        const result = await super.remove(query, params);

        for (const item of Array.isArray(result) ? result : [result]) {
            const childrenCount = await this.count({
                'tree.parent': item.tree.parent
            });

            if (childrenCount < 1) {
                await this.patch(
                    {'tree.path': item.tree.parent},
                    {
                        'tree.hasChild': false
                    }
                );
            }
        }

        return result;
    }

    processQuery(query) {
        if (Array.isArray(query.$select)) {
            query.$select = query.$select.filter(s => s.indexOf('tree') === -1);

            if (query.$select.indexOf('tree') === -1) {
                query.$select.push('tree');
            }
        }

        return query;
    }
}
