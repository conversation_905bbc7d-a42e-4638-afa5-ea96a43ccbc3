import _ from 'lodash';

export default class Collection {
    constructor(app, service) {
        this.app = app;
        this.service = service;
    }

    get name() {
        return this.service.collectionName;
    }

    get path() {
        return this.service.path;
    }

    get schema() {
        return this.service.schema;
    }

    get attributes() {
        return this.service.attributes;
    }

    get softDelete() {
        return this.service.softDelete;
    }

    get isAssignable() {
        return this.service.assignable;
    }

    get isLocalCached() {
        return this.service.localCache;
    }

    find(query = {}, params = {}) {
        params.query = query;

        return this.service.find(params);
    }

    async findOne(query = {}, params = {}) {
        query.$limit = 1;

        (params || {}).paginate = false;

        let record = await this.find(query, params);

        if (record.length > 0) {
            return record[0];
        } else {
            return null;
        }
    }

    get(id, params = {}) {
        return this.service.get(id, params);
    }

    create(data, params = {}) {
        return this.service.create(data, params);
    }

    update(id, data, params = {}) {
        return this.service.update(id, data, params);
    }

    patch(query, data, params = {}) {
        let id = null;

        query = query || {};

        if (_.isString(query)) {
            id = query;
        } else if (_.isString(query._id)) {
            id = query._id;
        } else {
            params.query = query;
        }

        return this.service.patch(id, data, params);
    }

    remove(query = {}, params = {}) {
        if (_.isString(query) || _.isNumber(query)) {
            // Just id given.
            return this.service.remove(query, params);
        } else if (_.isObject(query) && _.isString(query._id)) {
            return this.service.remove(query._id, params);
        } else if (_.isObject(query)) {
            params.query = query;

            return this.service.remove(null, params);
        } else {
            throw new Error('Invalid query!');
        }
    }

    async aggregate(pipeline, cache = false) {
        const servicePath =
            this.app.get('arch') === 'backend'
                ? this.service.Model.collectionName.replace('_', '/')
                : this.service.path;

        return await this.app.rpc('kernel.database.aggregate', {
            servicePath,
            pipeline,
            cache
        });
    }

    async distinct(field, query = {}) {
        const servicePath =
            this.app.get('arch') === 'backend'
                ? this.service.Model.collectionName.replace('_', '/')
                : this.service.path;

        return await this.app.rpc('kernel.database.distinct', {
            servicePath,
            field,
            query
        });
    }

    async sum(field, query = {}) {
        const servicePath =
            this.app.get('arch') === 'backend'
                ? this.service.Model.collectionName.replace('_', '/')
                : this.service.path;

        return await this.app.rpc('kernel.database.sum', {
            servicePath,
            field,
            query
        });
    }

    async avg(field, query = {}) {
        const servicePath =
            this.app.get('arch') === 'backend'
                ? this.service.Model.collectionName.replace('_', '/')
                : this.service.path;

        return await this.app.rpc('kernel.database.avg', {
            servicePath,
            field,
            query
        });
    }

    async max(field, query = {}) {
        const servicePath =
            this.app.get('arch') === 'backend'
                ? this.service.Model.collectionName.replace('_', '/')
                : this.service.path;

        return await this.app.rpc('kernel.database.max', {
            servicePath,
            field,
            query
        });
    }

    async min(field, query = {}) {
        const servicePath =
            this.app.get('arch') === 'backend'
                ? this.service.Model.collectionName.replace('_', '/')
                : this.service.path;

        return await this.app.rpc('kernel.database.min', {
            servicePath,
            field,
            query
        });
    }

    async count(query = {}, params = {}) {
        params.query = _.assign(query, {deleted: {$ne: true}});

        if (this.app.get('arch') === 'backend') {
            return await this.service.count(params);
        }

        return await this.app.rpc('kernel.database.count', {
            servicePath: this.service.path,
            query
        });
    }

    async bulkWrite(operations, options = {}) {
        const servicePath =
            this.app.get('arch') === 'backend'
                ? this.service.Model.collectionName.replace('_', '/')
                : this.service.path;

        return await this.app.rpc('kernel.database.bulk-write', {
            servicePath,
            operations,
            options
        });
    }

    hooks(hooks = {}) {
        return this.service.hooks(hooks);
    }

    publish(event, publisher) {
        return this.service.publish(event, publisher);
    }

    on(eventName, listener) {
        if (eventName === 'all') {
            eventName = ['created', 'updated', 'patched', 'removed', 'bulkWrite'];
        }

        if (Array.isArray(eventName)) {
            return eventName.forEach(event => {
                this.on(event, listener);
            });
        }

        return this.service.on(eventName, listener);
    }

    emit(eventName, data) {
        if (this.app.get('arch') === 'frontend') {
            const collectionName = this.service.path.split('/').join('.');

            return this.app.rpc('kernel.common.trigger-collection-event', {
                collectionName,
                eventName,
                data
            });
        }

        return this.service.emit(eventName, data);
    }

    removeListener(eventName, listener) {
        if (eventName === 'all') {
            eventName = ['created', 'updated', 'patched', 'removed', 'bulkWrite'];
        }

        if (Array.isArray(eventName)) {
            return eventName.forEach(event => {
                this.removeListener(event, listener);
            });
        }

        return this.service.removeListener(eventName, listener);
    }

    mixin(mixin) {
        return this.service.mixin(mixin);
    }
}
