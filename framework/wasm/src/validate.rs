use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use wasm_bindgen::prelude::*;

#[derive(Deserialize, Serialize)]
enum DataType {
    #[serde(rename = "integer")]
    Integer,
    #[serde(rename = "decimal")]
    Decimal,
    #[serde(rename = "number")]
    Number,
    #[serde(rename = "boolean")]
    Boolean,
    #[serde(rename = "date")]
    Date,
    #[serde(rename = "datetime")]
    DateTime,
    #[serde(rename = "string")]
    String,
    #[serde(rename = "object")]
    Object,
}

#[derive(Deserialize, Serialize)]
#[serde(untagged)]
enum FieldType {
    ArrayOfDataType(Vec<DataType>),
    ArrayOfRuleMap(Vec<HashMap<String, Rule>>),
    ArrayOfRule(Vec<Rule>),
    Normal(DataType),
}

#[derive(Deserialize, Serialize)]
struct Rule {
    #[serde(rename = "type")]
    field_type: FieldType,
    label: Option<String>,
    required: Option<bool>,
    blackbox: Option<bool>,
    min: Option<f64>,
    max: Option<f64>,
}

#[derive(Deserialize, Serialize)]
#[serde(untagged)]
enum Value {
    Integer(i64),
    Decimal(f64),
    Boolean(bool),
    Date(DateTime<Utc>),
    String(String),
    Object(HashMap<String, Value>),
    Array(Vec<Value>),
}

#[derive(Deserialize, Serialize)]
enum Data {
    Object(HashMap<String, Value>),
    Array(Vec<HashMap<String, Value>>),
}

// #[wasm_bindgen]
pub fn validate(schema: JsValue, data: JsValue) -> Result<(), JsValue> {
    // Enable the console panic hook.
    console_error_panic_hook::set_once();

    // Parse the schema.
    let mut schema: HashMap<String, Rule> = serde_wasm_bindgen::from_value(schema)?;

    // Parse the data.
    let data: Data = serde_wasm_bindgen::from_value(data)?;
    let mut rows = Vec::new();
    match data {
        Data::Object(r) => {
            rows.push(r);
        }
        Data::Array(mut rs) => {
            rows.append(&mut rs);
        }
    }

    // Do the validation.
    do_validation(schema, rows)?;

    // Normalize the schema.
    // for (field_name, rule) in schema.iter_mut().enumerate() {
    //
    // }
    // let v: JsValue = serde_wasm_bindgen::to_value(&schema)?;

    Ok(())
}

fn do_validation(
    schema: HashMap<String, Rule>,
    rows: Vec<HashMap<String, Value>>,
) -> Result<(), JsValue> {
    // Iterate over the rows.
    for row in rows.iter() {
        // Iterate over the schema fields.
        for (field, rule) in schema.iter() {
            // Get the value.
            let value = row.get(field);

            // Check the type.
        }
    }

    Ok(())
}
