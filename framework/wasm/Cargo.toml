[package]
name = "wasm"
version = "0.0.1"
authors = ["Mahmut AKTAŞ <<EMAIL>>"]
edition = "2021"

[lib]
crate-type = ["cdylib", "rlib"]

[dependencies]
console_error_panic_hook = "0.1.7"
chrono = { version = "0.4", default-features = false, features = ["serde"] }
serde = { version = "1.0", features = ["derive"] }
serde-wasm-bindgen = "0.6"
wasm-bindgen = "0.2"
#web-sys = { version = "0.3", features = ["console"] }

[profile.release]
lto = true
panic = "abort"
strip = true
opt-level = "s"
codegen-units = 1
debug = false
