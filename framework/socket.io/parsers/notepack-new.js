import notepack from 'notepack.io';
import Emitter from 'component-emitter';

export class Encoder {
    encode(packet) {
        return [notepack.encode(packet)];
    }
}

export class Decoder extends Emitter {
    /**
     * Receive a chunk (string or buffer) and optionally emit a
     * "decoded" event with the reconstructed packet
     */
    add(chunk) {
        const packet = notepack.decode(chunk);

        if (this.isPacketValid(packet)) {
            this.emit('decoded', packet);
        } else {
            throw new Error('Invalid format');
        }
    }

    isPacketValid({type, data, nsp, id}) {
        const isNamespaceValid = typeof nsp === 'string';
        const isAckIdValid = id === undefined || Number.isInteger(id);

        if (!isNamespaceValid || !isAckIdValid) {
            return false;
        }

        switch (type) {
            case 0: // CONNECT
                return data === undefined || typeof data === 'object';
            case 1: // DISCONNECT
                return data === undefined;
            case 2: // EVENT
                return Array.isArray(data) && data.length > 0;
            case 3: // ACK
                return Array.isArray(data);
            case 4: // CONNECT_ERROR
                return typeof data === 'object';
            default:
                return false;
        }
    }

    destroy() {}
}
