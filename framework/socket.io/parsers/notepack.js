import notepack from 'notepack.io';
import Emitter from 'component-emitter';

export const CONNECT = 0;
export const DISCONNECT = 1;
export const EVENT = 2;
export const ACK = 3;
export const ERROR = 4;
export const BINARY_EVENT = 5;
export const BINARY_ACK = 6;

const errorPacket = {
    type: ERROR,
    data: 'parser error'
};

export class Encoder {
    encode(packet) {
        switch (packet.type) {
            case CONNECT:
            case DISCONNECT:
            case ERROR:
                return [JSON.stringify(packet)];
            default:
                return [notepack.encode(packet)];
        }
    }
}

export class Decoder extends Emitter {
    add(obj) {
        if (typeof obj === 'string') {
            this.parseJSON(obj);
        } else {
            this.parseBinary(obj);
        }
    }

    parseJSON(obj) {
        try {
            const decoded = JSON.parse(obj);

            this.emit('decoded', decoded);
        } catch (e) {
            this.emit('decoded', errorPacket);
        }
    }

    parseBinary(obj) {
        try {
            const decoded = notepack.decode(obj);

            this.emit('decoded', decoded);
        } catch (e) {
            this.emit('decoded', errorPacket);
        }
    }

    destroy() {}
}
