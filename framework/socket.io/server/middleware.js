import _ from 'lodash';

const disconnect = (app, getParams) => (socket, next) => {
    const disconnectedDebounced = _.debounce(() => app.emit('disconnect', getParams(socket)), 250, {
        leading: false,
        trailing: true
    });

    socket.once('disconnect', disconnectedDebounced);
    socket.once('ping timeout', disconnectedDebounced);
    socket.once('transport close', disconnectedDebounced);
    socket.once('transport error', disconnectedDebounced);

    next();
};

const params = (app, socketMap) => (socket, next) => {
    socket.feathers = {
        provider: 'socketio',
        headers: socket.handshake.headers
    };

    socketMap.set(socket.feathers, socket);

    next();
};

const authentication =
    (app, getParams, settings = {}) =>
    (socket, next) => {
        const service = app.defaultAuthentication ? app.defaultAuthentication(settings.service) : null;

        if (service === null) {
            return next();
        }

        const config = service.configuration;
        const authStrategies = config.parseStrategies || config.authStrategies || [];

        if (authStrategies.length === 0) {
            return next();
        }

        service
            .parse(socket.handshake, null, ...authStrategies)
            .then(async authentication => {
                if (authentication) {
                    socket.feathers.authentication = authentication;
                    await service.create(authentication, {
                        provider: 'socketio',
                        connection: getParams(socket)
                    });
                }

                next();
            })
            .catch(next);
    };

export default {
    disconnect,
    params,
    authentication
};
