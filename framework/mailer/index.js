import _ from 'lodash';
import mailer from 'nodemailer';

export default class Mailer {
    constructor() {
        this.isVerified = false;
    }

    async connect(options) {
        this.isVerified = false;

        this.transporter = mailer.createTransport({
            pool: true,
            host: options.host,
            port: options.port,
            secure: !!options.secure || false,
            auth: {
                user: options.user,
                pass: options.password
            },
            tls: options.tls || {},
            ..._.omit(options, ['host', 'port', 'secure', 'user', 'password', 'tls'])
        });

        try {
            await this.transporter.verify();

            this.isVerified = true;
        } catch (error) {
            this.isVerified = false;

            throw error;
        }
    }

    send(params) {
        if (this.isVerified) {
            return this.transporter.sendMail(params);
        } else {
            throw new Error('Mail server connection is not verified!');
        }
    }
}
