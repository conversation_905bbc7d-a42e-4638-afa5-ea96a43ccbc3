const webpack = require('webpack');
const shell = require('shelljs');
const {clean, log, timeDiff} = require('../utils');

module.exports = {
    name: 'init',
    description: 'Initialize application.',
    action() {
        process.env.NODE_ENV = 'development';
        process.env.IS_INITIALIZING = 'yes';

        log('info', 'Please wait! Building for initialization..');

        // Clean build results.
        clean(true);

        webpack(require('../compiler/config/backend'), (error, stats) => {
            log('success', 'Build process is completed successfully. Staring initialization..');

            shell.exec('node ./storage/build/backend/app.js');
        });
    }
};
