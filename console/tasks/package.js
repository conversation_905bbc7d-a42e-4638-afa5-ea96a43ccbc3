const path = require('path');
const fs = require('fs');
const v8 = require('v8');
const _ = require('lodash');
const shell = require('shelljs');
const archiver = require('archiver');
const del = require('del');
const {minify} = require('terser');
const webpack = require('webpack');
const settings = require('../settings');
const {clean, log, timeDiff} = require('../utils');

module.exports = {
    name: 'package',
    description: 'Package application.',
    async action() {}
};
