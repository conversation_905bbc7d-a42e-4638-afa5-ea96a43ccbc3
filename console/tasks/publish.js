const path = require('path');
const fs = require('fs');
const v8 = require('v8');
const _ = require('lodash');
const shell = require('shelljs');
const archiver = require('archiver');
const del = require('del');
const {minify} = require('terser');
const webpack = require('webpack');
const settings = require('../settings');
const {clean, log, timeDiff} = require('../utils');
const ssh = new (require('node-ssh').NodeSSH)();

module.exports = {
    name: 'publish',
    description: 'Publish a new version.',
    async action() {
        const tempAppPath = path.join(settings.paths.tempPath, 'app');
        const configJson = JSON.parse(fs.readFileSync(path.join(settings.paths.rootPath, 'config.json')));
        const pkgJson = JSON.parse(fs.readFileSync(path.join(settings.paths.rootPath, 'package.json')));
        const sshConfig = configJson.ssh;
        const deploymentConfig = configJson.deployment;
        let result = null;

        // Build
        process.env.NODE_ENV = 'production';
        process.env.IS_PACKAGED = 'yes';

        log('info', 'Please wait! Building for packaging..');

        // Clean build results.
        clean(true, true);

        await new Promise((resolve, reject) => {
            const startTime = new Date();
            webpack(
                [
                    require('../compiler/config/backend'),
                    require('../compiler/config/frontend'),
                    require('../compiler/config/vendors')
                ],
                async (error, stats) => {
                    if (stats.hasErrors()) {
                        const jsonStats = stats.toJson();

                        console.log(jsonStats);

                        return;
                    }

                    if (error) {
                        log('error', error.message);

                        reject();

                        return;
                    }

                    const endTime = new Date();

                    log('success', `Build process is completed in ${timeDiff(startTime, endTime)}`);

                    log('info', 'Please wait! Packaging...');

                    shell.exec(`mkdir ${path.join(tempAppPath)}`);
                    shell.exec(`mkdir ${path.join(tempAppPath, 'bin')}`);
                    shell.exec(`mkdir ${path.join(tempAppPath, 'backend')}`);
                    shell.exec(`mkdir ${path.join(tempAppPath, 'frontend')}`);
                    shell.exec(`mkdir ${path.join(tempAppPath, 'static')}`);
                    shell.exec(`mkdir ${path.join(tempAppPath, 'storage')}`);
                    shell.exec(`mkdir ${path.join(tempAppPath, 'storage', 'cache')}`);
                    shell.exec(`mkdir ${path.join(tempAppPath, 'storage', 'temp')}`);

                    shell.cp('-R', path.join(settings.paths.rootPath, '/bin/linux/*'), path.join(tempAppPath, 'bin/'));
                    shell.cp(
                        '-R',
                        path.join(settings.paths.backendBuildPath, '/*'),
                        path.join(tempAppPath, 'backend/')
                    );
                    shell.cp(
                        '-R',
                        path.join(settings.paths.frontendBuildPath, '/*'),
                        path.join(tempAppPath, 'frontend/')
                    );
                    shell.cp('-R', path.join(settings.paths.staticPath, '/*'), path.join(tempAppPath, 'static/'));
                    shell.cp('-R', path.join(settings.paths.rootPath, '/yarn.lock'), path.join(tempAppPath, '/'));

                    _.each(pkgJson.dependencies, (version, packageName) => {
                        if (
                            settings.bundledFrontendPackages.concat(settings.frontEndPackages).indexOf(packageName) !==
                            -1
                        ) {
                            delete pkgJson.dependencies[packageName];
                        }
                    });
                    delete pkgJson.devDependencies;
                    fs.writeFileSync(path.join(tempAppPath, 'package.json'), JSON.stringify(pkgJson, null, 4));

                    await new Promise((res, rej) => {
                        shell.exec(
                            'yarn install --production --ignore-engines',
                            {
                                cwd: tempAppPath
                            },
                            (code, stdout, stderr) => {
                                if (code === 0) {
                                    res(stdout);
                                } else {
                                    rej(stderr);
                                }
                            }
                        );
                    });

                    const output = fs.createWriteStream(path.join(settings.paths.tempPath, 'entererp.zip'));
                    const archive = archiver('zip', {
                        zlib: {level: 9} // Sets the compression level.
                    });
                    output.on('close', function () {
                        log('success', `Application is packaged successfully!`);

                        resolve();
                    });
                    archive.pipe(output);
                    archive.directory(tempAppPath, false);
                    archive.finalize();
                }
            );
        });

        // Connect to server.
        log('info', 'Please wait! Connecting to remote server..');
        try {
            await ssh.connect({
                host: 'main.entererp.com',
                port: '22',
                username: 'root',
                privateKeyPath: sshConfig.privateKey
            });
        } catch (e) {
            log('error', `Could not connect to the server! Reason: ${e.message}`);

            process.exit(1);
        }
        log('success', 'Successfully connected to the remote server.');

        // Put file to remote folder.
        log('info', 'Please wait! Putting the files to the remote server..');
        try {
            await ssh.putFile(
                path.join(settings.paths.tempPath, 'entererp.zip'),
                `/opt/entererp-versions/entererp.${pkgJson.version}.zip`
            );
        } catch (e) {
            log('error', `Could not put file to remote destination! Reason: ${e.message}`);

            process.exit(1);
        }
        log('success', 'Successfully put the files to the remote server.');
        log('success', `Successfully published the version ${pkgJson.version}`);

        del.sync([
            path.join(settings.paths.tempPath, '**'),
            '!' + settings.paths.tempPath,
            '!' + path.join(settings.paths.tempPath, '.gitignore')
        ]);

        process.exit(0);
    }
};
