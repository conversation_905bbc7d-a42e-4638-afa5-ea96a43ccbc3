const webpack = require('webpack');
const shell = require('shelljs');
const {clean, log, timeDiff} = require('../utils');

module.exports = {
    name: 'seed',
    description: 'Seeding application.',
    action() {
        process.env.NODE_ENV = 'development';
        process.env.IS_SEEDING = 'yes';

        log('info', 'Please wait! Building for seeding..');

        // Clean build results.
        clean(true);

        webpack(require('../compiler/config/backend'), (error, stats) => {
            log('success', 'Build process is completed successfully. Staring seeding..');

            shell.exec('node ./storage/build/backend/app.js');
        });
    }
};
