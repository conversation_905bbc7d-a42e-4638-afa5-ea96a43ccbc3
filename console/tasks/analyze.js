const webpack = require('webpack');
const {clean, log, timeDiff} = require('../utils');

module.exports = {
    name: 'analyze',
    description: 'Build application for analyzing.',
    action() {
        process.env.NODE_ENV = 'production';
        process.env.IS_ANALYZING = 'yes';

        log('info', 'Please wait! Building for analyzing..');

        // Clean build results.
        clean(true);

        const startTime = new Date();
        webpack(require('../compiler/config/frontend'), (error, stats) => {
            if (error) {
                log('error', error.message);

                return;
            }

            const endTime = new Date();

            log('success', `Build process is completed in ${timeDiff(startTime, endTime)}`);
        });
    }
};
