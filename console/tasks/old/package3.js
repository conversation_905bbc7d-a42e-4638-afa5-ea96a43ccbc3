const path = require('path');
const fs = require('fs');
const v8 = require('v8');
const _ = require('lodash');
const shell = require('shelljs');
const archiver = require('archiver');
const del = require('del');
const {minify} = require('terser');
const webpack = require('webpack');
const bytenode = require('./utils/bytenode');
const settings = require('../settings');
const {clean, log, timeDiff} = require('../utils');

v8.setFlagsFromString('--no-lazy');

module.exports = {
    name: 'package',
    description: 'Package application.',
    arguments: '[compile]',
    action(compile) {
        process.env.NODE_ENV = 'production';
        process.env.IS_PACKAGED = 'yes';
        process.env.IS_BUNDLED = 'yes';

        log('info', 'Please wait! Building for packaging..');

        // Clean build results.
        clean(true, true);

        const startTime = new Date();
        webpack([
            require('../compiler/config/backend'),
            require('../compiler/config/frontend'),
            require('../compiler/config/vendors')
        ], async (error, stats) => {
            if (error) {
                log('error', error.message);

                return;
            }

            const endTime = new Date();
            log('success', `Build process is completed in ${timeDiff(startTime, endTime)}`);
            log('info', 'Please wait! Packaging...');

            // Get temp app path.
            const tempAppPath = path.join(settings.paths.tempPath, 'app');

            // Create temp app path.
            shell.exec(`mkdir ${path.join(tempAppPath)}`);

            // Copy directories.
            shell.cp('-R', path.join(__dirname, '/templates/package/*'), tempAppPath);
            shell.cp('-R', path.join(settings.paths.backendBuildPath, '/*'), path.join(tempAppPath, 'storage/build/backend/'));
            shell.cp('-R', path.join(settings.paths.frontendBuildPath, '/*'), path.join(tempAppPath, 'storage/build/frontend/'));
            shell.cp('-R', path.join(settings.paths.staticPath, '/*'), path.join(tempAppPath, 'static/'));

            // Get app.js path.
            const appJsPath = path.join(tempAppPath, 'storage/build/backend/app.js');

            // Prepare app.js.
            const appJsContent = fs.readFileSync(appJsPath, {encoding: 'utf8'});
            fs.writeFileSync(
                appJsPath,
                `global.checkLicence = true;global.isPackaged = true;global.isBundled = true;${appJsContent}`
            );

            // Compile to bytecode.
            bytenode.compileFile({
                filename: path.join(tempAppPath, 'storage/build/backend/app.js'),
                compileAsModule: true
            });
            fs.unlinkSync(path.join(tempAppPath, 'storage/build/backend/app.js'));

            // Copy rummer.
            let runnerJsContent = fs.readFileSync(path.join(__dirname, './utils/bytenode.js'), {encoding: 'utf8'});
            runnerJsContent = `${runnerJsContent}\nconst init = require('./app.jsc');\nrequire('./main.js');\ninit.default();`;
            fs.writeFileSync(path.join(tempAppPath, 'storage/build/backend/runner.js'), (await minify(runnerJsContent, {sourceMap: false})).code);

            // Prepare package.json.
            const mainPkgJson = JSON.parse(fs.readFileSync(path.join(settings.paths.rootPath, 'package.json')));
            const templatePkgJson = JSON.parse(fs.readFileSync(path.join(tempAppPath, 'package.json')));
            templatePkgJson.version = mainPkgJson.version;
            templatePkgJson.dependencies = mainPkgJson.dependencies;
            _.each(templatePkgJson.dependencies, (version, packageName) => {
                if (settings.bundledFrontendPackages.concat(settings.frontEndPackages).indexOf(packageName) !== -1) {
                    delete templatePkgJson.dependencies[packageName];
                }
            });
            fs.writeFileSync(path.join(tempAppPath, 'package.json'), JSON.stringify(templatePkgJson, null, 4));

            // Prepare config.json.
            const configJson = JSON.parse(fs.readFileSync(path.join(tempAppPath, 'config.json')));
            configJson.app.version = mainPkgJson.version;
            fs.writeFileSync(path.join(tempAppPath, 'config.json'), JSON.stringify(configJson, null, 4));
        });
    }
};
