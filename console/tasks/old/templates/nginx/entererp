upstream backend {
    server 127.0.0.1:3000;
    keepalive 64;
}

server {
    # General.
    listen {{port}};
    server_name {{host}};

    # Increase max upload size.
    client_max_body_size 100M;

    # Try to load from nginx first.
    location / {
        try_files $uri @backend;
    }

    location @backend {
        # General.
        proxy_pass http://backend;
        proxy_redirect off;
    	proxy_read_timeout 240s;
        proxy_max_temp_file_size 0;
        proxy_cache_bypass $http_upgrade;

        # Headers.
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-NginX-Proxy true;

        # Following is necessary for Websocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
