{"presets": [["@babel/preset-env", {"modules": false, "targets": {"node": "current", "browsers": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version", "last 1 edge version"]}}]], "plugins": ["@babel/plugin-transform-runtime", "@babel/plugin-syntax-dynamic-import", ["@babel/plugin-proposal-decorators", {"legacy": true}], ["@babel/plugin-proposal-class-properties", {"loose": true}]]}