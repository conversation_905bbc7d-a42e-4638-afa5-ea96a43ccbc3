const path = require('path');
const appConfig = require('../config.json');

const rootPath = path.resolve(process.cwd());
const srcPath = path.join(rootPath, 'core');
const backendSrcPath = path.join(srcPath, 'backend');
const frontendSrcPath = path.join(srcPath, 'frontend');
const buildPath = path.join(rootPath, 'storage/build');
const backendBuildPath = path.join(buildPath, 'backend');
const frontendBuildPath = path.join(buildPath, 'frontend');
const staticPath = path.join(rootPath, 'static');
const tempPath = path.join(rootPath, 'storage/temp');
const cachePath = path.join(rootPath, 'storage/cache');
const userNodeModulesPath = path.join(rootPath, 'node_modules');

module.exports = {
    // App
    app: {
        title: appConfig.app.title,
        locale: appConfig.app.locale,
        version: appConfig.app.version
    },

    // Server
    server: {
        host: appConfig.server.host,
        port: appConfig.server.port
    },

    // Paths
    paths: {
        rootPath,
        srcPath,
        backendSrcPath,
        frontendSrcPath,
        buildPath,
        backendBuildPath,
        frontendBuildPath,
        staticPath,
        tempPath,
        cachePath,
        userNodeModulesPath
    },
};
