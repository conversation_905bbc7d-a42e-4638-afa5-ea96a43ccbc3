const path = require('path');
const fs = require('fs');

module.exports = function(source) {
    const addons = JSON.parse(source);
    let code = '';

    code += `import _ from 'lodash';`;
    code += `import Vue from 'vue';`;
    code += `import LoadingComponent from 'el-loading-component';`;

    code += `window.__bundledENV = '${process.env.NODE_ENV}'`;

    code += '(function(g) {';
    code += 'let addon, resolvedItems;';
    addons.forEach(addonName => {
        const addonPath = path.join(process.cwd(), 'addons', addonName);
        const viewsPath = path.join(addonPath, 'views');
        const componentsPath = path.join(addonPath, 'components');
        const widgetsPath = path.join(addonPath, 'widgets');
        const routesPath = path.join(addonPath, 'routes.js');
        const extendPath = path.join(addonPath, 'extend-frontend.js');
        const initPath = path.join(addonPath, 'frontend.js');

        // Load addon
        code += `addon = {name: require('@/addons/${addonName}/addon.json').name, routes: [], components: [], widgets: {}, extend: null, init: null};`;
        if (fs.existsSync(viewsPath)) {
            code += `resolvedItems = require.context('@/addons/${addonName}/views/', true, /([\\w-_]+)\\.vue/, 'lazy');`;
            code += `for (const key of resolvedItems.keys()) {
                    let viewPath = key.replace(/^\\.\\//, '').replace(/\\.vue/, '');
                    let viewName = '${addonName}-views-' + viewPath.replace(/\\//g, '-');
                    if (viewName.indexOf('-_') !== -1) continue;
                    addon.components.push(viewName);
                    Vue.component(viewName, () => ({
                        component: import('@/addons/${addonName}/views/' + viewPath + '.vue'),
                        loading: LoadingComponent,
                        delay: 0
                    }));
                }`;
        }
        if (fs.existsSync(componentsPath)) {
            code += `resolvedItems = require.context('@/addons/${addonName}/components/', false, /^\\.\\/([\\w-_]+)\\.vue/, 'lazy');`;
            code += `for (const key of resolvedItems.keys()) {
                    let componentPath = key.replace(/^\\.\\//, '').replace(/\\.vue/, '');
                    let componentName = '${addonName}-components-' + componentPath.replace(/\\//g, '-');
                    if (componentName.indexOf('-_') !== -1) continue;
                    addon.components.push(componentName);
                    Vue.component(componentName, () => import('@/addons/${addonName}/components/' + componentPath + '.vue'));
                }`;
        }
        if (fs.existsSync(widgetsPath)) {
            code += `resolvedItems = require.context('@/addons/${addonName}/widgets/', true, /^\\.\\/([\\w-_]+)\\/widget\\.json/, 'lazy');`;
            code += `for (const key of resolvedItems.keys()) {
                    let widgetName = key.replace(/^\\.\\//, '').replace(/\\/widget\\.json/, '');
                    let widgetComponentName = '${addonName}-widgets-' + widgetName;
                    Vue.component(widgetComponentName, () => import('@/addons/${addonName}/widgets/' + widgetName + '/main.vue'));
                }`;
        }
        // if (fs.existsSync(viewsPath)) {
        //     code += `resolvedItems = require.context('@/addons/${addonName}/views/', true, /([\\w-_]+)\\.vue/, 'lazy');`;
        //     code += `for (const key of resolvedItems.keys()) {
        //             let viewPath = key.replace(/^\\.\\//, '').replace(/\\.vue/, '');
        //             let viewName = '${addonName}-views-' + viewPath.replace(/\\//g, '-');
        //             if (viewName.indexOf('-_') !== -1) continue;
        //             addon.components.push(viewName);
        //             Vue.component(viewName, require('@/addons/${addonName}/views/' + viewPath + '.vue').default);
        //         }`;
        // }
        // if (fs.existsSync(componentsPath)) {
        //     code += `resolvedItems = require.context('@/addons/${addonName}/components/', false, /^\\.\\/([\\w-_]+)\\.vue/, 'lazy');`;
        //     code += `for (const key of resolvedItems.keys()) {
        //             let componentPath = key.replace(/^\\.\\//, '').replace(/\\.vue/, '');
        //             let componentName = '${addonName}-components-' + componentPath.replace(/\\//g, '-');
        //             if (componentName.indexOf('-_') !== -1) continue;
        //             addon.components.push(componentName);
        //             Vue.component(componentName, require('@/addons/${addonName}/components/' + componentPath + '.vue').default);
        //         }`;
        // }

        if (fs.existsSync(routesPath)) {
            code += `addon.routes = require('@/addons/${addonName}/routes.js').default;`;
        }
        if (fs.existsSync(extendPath)) {
            code += `addon.extend = require('@/addons/${addonName}/extend-frontend.js').default;`;
        }
        if (fs.existsSync(initPath)) {
            code += `addon.init = require('@/addons/${addonName}/frontend.js').default;`;
        }

        code += 'const addonIndex = g.entererp.programs.findIndex(p => p.name === addon.name);';
        code += 'g.entererp.programs[addonIndex] = {...g.entererp.programs[addonIndex], ...addon};';
    });
    code += '})(window);';

    return code;
};
