const path = require('path');
const fs = require('fs');

module.exports = function(source) {
    const addons = JSON.parse(source);

    let code = '(function(g) {';

    code += `global.__bundledENV = '${process.env.NODE_ENV}'`;

    code += `const _ = require('lodash');`;
    code += 'const addons = [];';
    code += 'let addon, resolvedItems;';
    addons.forEach(addonName => {
        const addonPath = path.join(process.cwd(), 'addons', addonName);
        const collectionsPath = path.join(addonPath, 'collections');
        const migrationsPath = path.join(addonPath, 'migrations');
        const schemasPath = path.join(addonPath, 'schemas');
        const methodsPath = path.join(addonPath, 'methods');
        const jobsPath = path.join(addonPath, 'jobs');
        const translationsPath = path.join(addonPath, 'translations');
        const widgetsPath = path.join(addonPath, 'widgets');
        const extendPath = path.join(addonPath, 'extend-backend.js');
        const initPath = path.join(addonPath, 'backend.js');

        // Load addon
        code += `addon = _.assign({}, require('@/addons/${addonName}/addon.json'), {collections: [], migrations: [], schemas: [], methods: [], jobs: [], permissions: [], translations: {}, widgets: [], extend: null, init: null});`;
        if (fs.existsSync(collectionsPath)) {
            code += `resolvedItems = require.context('@/addons/${addonName}/collections/', false, /^\\.\\/([\\w-_]+)\\.js/);`;
            code += `_.each(resolvedItems.keys(), key => addon.collections.push(resolvedItems(key).default));`;
        }
        if (fs.existsSync(migrationsPath)) {
            code += `resolvedItems = require.context('@/addons/${addonName}/migrations/', false, /^\\.\\/([\\w-_]+)\\.js/);`;
            code += `_.each(resolvedItems.keys(), key => addon.migrations.push(resolvedItems(key).default));`;
        }
        if (fs.existsSync(schemasPath)) {
            code += `resolvedItems = require.context('@/addons/${addonName}/schemas/', false, /^\\.\\/([\\w-_]+)\\.js/);`;
            code += `_.each(resolvedItems.keys(), key => addon.schemas.push(resolvedItems(key).default));`;
        }
        if (fs.existsSync(methodsPath)) {
            code += `resolvedItems = require.context('@/addons/${addonName}/methods/', false, /^\\.\\/([\\w-_]+)\\.js/);`;
            code += `_.each(resolvedItems.keys(), key => addon.methods.push(resolvedItems(key).default));`;
        }
        if (fs.existsSync(jobsPath)) {
            code += `resolvedItems = require.context('@/addons/${addonName}/jobs/', false, /^\\.\\/([\\w-_]+)\\.js/);`;
            code += `_.each(resolvedItems.keys(), key => addon.jobs.push(resolvedItems(key).default));`;
        }
        if (fs.existsSync(translationsPath)) {
            code += `resolvedItems = require.context('@/addons/${addonName}/translations/', false, /^\\.\\/([\\w-_]+)\\.json/);`;
            code += `_.each(resolvedItems.keys(), key => { let locale = key.replace(/^\\.\\//, '').replace(/\\.json/, ''); addon.translations[locale] = resolvedItems(key); });`;
        }
        if (fs.existsSync(widgetsPath)) {
            code += `resolvedItems = require.context('@/addons/${addonName}/widgets/', true, /^\\.\\/([\\w-_]+)\\/widget\\.json/);`;
            code += `_.each(resolvedItems.keys(), key => {
            let widgetName = key.replace(/^\\.\\//, '').replace(/\\/widget\\.json/, '');
            addon.widgets.push({
                name: widgetName,
                definition: resolvedItems(key),
                method: null
            });
            });`;
        }
        if (fs.existsSync(widgetsPath)) {
            code += `resolvedItems = require.context('@/addons/${addonName}/widgets/', true, /^\\.\\/([\\w-_]+)\\/method\\.js/);`;
            code += `_.each(resolvedItems.keys(), key => {
            let widgetName = key.replace(/^\\.\\//, '').replace(/\\/method\\.js/, '');
            let index = addon.widgets.findIndex(w => w.name === widgetName);
            addon.widgets[index].method = resolvedItems(key).default;
            });`;
        }
        if (fs.existsSync(extendPath)) {
            code += `addon.extend = require('@/addons/${addonName}/extend-backend.js').default;`;
        }
        if (fs.existsSync(initPath)) {
            code += `addon.init = require('@/addons/${addonName}/backend.js').default;`;
        }
        code += `addons.push(addon);`;
    });
    code += `g.__addons = addons;`;
    code += '})(global);';

    return code;
};
