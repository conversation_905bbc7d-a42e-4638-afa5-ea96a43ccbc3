const express = require('express');
const compress = require('compression');
const webpack = require('webpack');
const config = require('../config/frontend');

const server = express();
const compiler = webpack(config);

compiler.hooks.watchRun.tapAsync('EnterERPWatchRunPlugin', (compiler, callback) => {
    process.send('compiling');

    callback();
});
compiler.hooks.done.tap('EnterERPDonePlugin', stats => {
    if (!stats.hasErrors()) {
        process.send('compiled');
    } else {
        const jsonStats = stats.toJson();

        process.send({
            hasErrors: true,
            error: jsonStats.errors[0]
        });
    }
});

const devMiddleware = require('webpack-dev-middleware')(compiler, {
    publicPath: config.output.publicPath,
    historyApiFallback: true,
    headers: {
        'Access-Control-Allow-Origin': '*'
    },
    logLevel: 'silent'
});

const hotMiddleware = require('webpack-hot-middleware')(compiler, {
    log: false,
    heartbeat: 2000
});

// Enable compression.
server.use(compress());

// Serve webpack bundle output.
server.use(devMiddleware);

// Enable hot-reload and state-preserving compilation error display.
server.use(hotMiddleware);

server.listen(3001);
