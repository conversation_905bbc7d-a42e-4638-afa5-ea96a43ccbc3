const path = require('path');
const webpack = require('webpack');
const threadLoader = require('thread-loader');
const nodeExternals = require('webpack-node-externals');
const settings = require('../../settings');

const isProduction = process.env.NODE_ENV === 'production';

threadLoader.warmup({
    // pool options, like passed to loader options
    // must match loader options to boot the correct pool
}, [
    // modules to load
    // can be any module, i. e.
    'babel-loader'
]);

const config = {
    entry: {
        main: [
            path.join(settings.paths.backendSrcPath, 'index.js')
        ]
    },

    output: {
        path: settings.paths.backendBuildPath,
        filename: '[name].js',
        libraryTarget: 'commonjs2',
        sourceMapFilename: '[name].map'
    },

    resolve: {
        extensions: ['.js'],
        alias: {
            '@': settings.paths.rootPath
        }
    },

    module: {
        rules: [
            {
                test: /\.js$/,
                use: ['cache-loader', 'thread-loader', 'babel-loader'],
                exclude: [
                    /cli/,
                    /node_modules/,
                    /storage/
                ]
            },
            {
                test: /addons.json$/,
                type: 'javascript/auto',
                use: ['thread-loader', 'babel-loader', path.join(__dirname, '../loaders/backend-loader.js')],
                exclude: [
                    /cli/,
                    /node_modules/,
                    /storage/
                ]
            },
            {
                test: /\.html/,
                loader: 'html-loader',
                options: {
                    minimize: isProduction
                },
                exclude: [
                    /cli/,
                    /node_modules/,
                    /storage/
                ]
            },
            {
                test: /\.txt$/i,
                use: 'raw-loader',
                exclude: [
                    /cli/,
                    /node_modules/,
                    /storage/
                ]
            }
        ]
    },

    performance: {
        hints: false
    },

    externals: nodeExternals({
        allowlist: [
            /\.(eot|woff|woff2|ttf|otf)$/,
            /\.(svg|png|jpg|jpeg|gif|ico|webm)$/,
            /\.(mp4|mp3|ogg|swf|webp)$/,
            /\.(css|scss|sass|less|styl)$/,
        ]
    }),

    target: 'node',

    devtool: 'source-map',

    node: {
        __filename: true,
        __dirname: true
    },

    cache: true,

    plugins: [
        new webpack.NoEmitOnErrorsPlugin()
    ]
};

if (!isProduction) {
    config.mode = 'development';
    config.devtool = 'source-map';

    const env = {
        'process.env.NODE_ENV': JSON.stringify('development')
    };

    config.plugins.push(new webpack.BannerPlugin({
        raw: true,
        entryOnly: false,
        banner: `require('${
            // Is source-map-support installed as project dependency, or linked?
            (require.resolve('source-map-support').indexOf(process.cwd()) === 0)
                // If it's resolvable from the project root, it's a project dependency.
                ? 'source-map-support/register'
                // It's not under the project, it's linked via lerna.
                : require.resolve('source-map-support/register')
        }')`
    }));
    config.plugins.push(new webpack.DefinePlugin(env));
} else {
    config.mode = 'production';

    config.plugins.push(new webpack.DefinePlugin({
        'process.env.NODE_ENV': JSON.stringify('production')
    }));
}

module.exports = config;
