const path = require('path');
const webpack = require('webpack');
const threadLoader = require('thread-loader');
const {VueLoaderPlugin} = require('vue-loader');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const OptimizeCSSPlugin = require('optimize-css-assets-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const AssetsPlugin = require('assets-webpack-plugin');
const settings = require('../../settings');

const isProduction = process.env.NODE_ENV === 'production';

threadLoader.warmup(
    {
        // pool options, like passed to loader options
        // must match loader options to boot the correct pool
    },
    [
        // modules to load
        // can be any module, i. e.
        'babel-loader',
        'vue-loader'
    ]
);

const config = {
    output: {
        path: settings.paths.frontendBuildPath
    },

    externals: {
        lodash: 'vendorGlobals.lodash',
        'object-hash': 'vendorGlobals.objectHash',
        jquery: 'vendorGlobals.jQuery',
        'simpl-schema': 'vendorGlobals.SimpleSchema',
        'element-ui/lib/locale/lang/en': 'vendorGlobals.ElementUILangEN',
        'element-ui/lib/locale/lang/tr': 'vendorGlobals.ElementUILangTR',
        'element-ui/lib/locale': 'vendorGlobals.ElementUILocale',
        'jwt-decode': 'vendorGlobals.jwtDecode',
        '@feathersjs/client': 'vendorGlobals.feathers',
        '@feathersjs/socketio-client': 'vendorGlobals.socketio',
        'socket.io-client': 'vendorGlobals.io',
        'big.js': 'vendorGlobals.bigJS',
        traverse: 'vendorGlobals.traverse',
        'deep-diff': 'vendorGlobals.DeepDiff',
        uberproto: 'vendorGlobals.uberproto',
        sift: 'vendorGlobals.sift',
        luxon: 'vendorGlobals.luxon',
        rrule: 'vendorGlobals.rrule',
        'javascript-time-ago': 'vendorGlobals.TimeAgo',
        'perfect-scrollbar': 'vendorGlobals.PerfectScrollbar',
        'ag-grid-vue': 'vendorGlobals.AgGridVue',
        'resize-observer-polyfill': 'vendorGlobals.ResizeObserver',
        'masonry-layout': 'vendorGlobals.Masonry',
        interactjs: 'vendorGlobals.InteractJs',
        'element-resize-detector': 'vendorGlobals.elementResizeDetector',
        '@cubejs-client/core': 'vendorGlobals.cubejs',
        quill: 'vendorGlobals.Quill',
        'quill-delta': 'vendorGlobals.QuillDelta',
        fullcalendar: 'vendorGlobals.fullcalendar',
        mousetrap: 'vendorGlobals.Mousetrap',
        highcharts: 'vendorGlobals.Highcharts',

        vue: 'appGlobals.Vue',
        'vue-class-component': 'appGlobals.VueClassComponent',
        'ue-deepset': 'appGlobals.VueDeepSet',
        'vue-property-decorator': 'appGlobals.VuePropertyDecorator',
        'vue-router': 'appGlobals.VueRouter',
        vuex: 'appGlobals.Vuex',
        'component-emitter': 'appGlobals.componentEmitter',
        'element-ui': 'appGlobals.ElementUI',
        'el-loading-component': 'appGlobals.ElLoadingComponent'
    },

    resolve: {
        extensions: ['.js', '.vue'],
        alias: {
            '@': settings.paths.rootPath
        }
    },

    module: {
        rules: [
            {
                test: /\.worker\.js$/,
                use: {
                    loader: 'worker-loader',
                    options: isProduction ? {name: 'addons/js/[hash].worker.js'} : {inline: true}
                },
                exclude: [/cli/, /node_modules/, /storage/]
            },
            {
                test: /\.js$/,
                use: ['cache-loader', 'thread-loader', 'babel-loader'],
                exclude: [/cli/, /node_modules/, /storage/]
            },
            {
                test: /addons.json$/,
                type: 'javascript/auto',
                use: ['babel-loader', path.join(__dirname, '../loaders/frontend-loader.js')],
                exclude: [/cli/, /node_modules/, /storage/]
            },
            {
                test: /\.vue$/,
                use: [
                    'cache-loader',
                    'thread-loader',
                    {
                        loader: 'vue-loader'
                        // options: {
                        //     cssSourceMap: !isProduction,
                        //     compilerOptions: {
                        //         preserveWhitespace: !isProduction
                        //     }
                        //     // cacheBusting: true,
                        //     // cacheDirectory: path.join(settings.paths.cachePath, 'build', 'vue'),
                        //     // cacheIdentifier: [
                        //     //     process.env.NODE_ENV || 'development',
                        //     //     webpack.version,
                        //     //     VUE_VERSION,
                        //     //     VUE_LOADER_VERSION
                        //     // ].join('|')
                        // }
                    }
                ],
                exclude: [/cli/, /node_modules/, /storage/]
            },
            {
                test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
                use: [
                    {
                        loader: 'url-loader',
                        options: {
                            limit: 10000,
                            esModule: false,
                            fallback: {
                                loader: 'file-loader',
                                options: {
                                    name: 'addons/images/[name].[hash:7].[ext]'
                                }
                            }
                        }
                    }
                ],
                exclude: [/cli/, /node_modules/, /storage/]
            },
            {
                test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/,
                use: [
                    {
                        loader: 'url-loader',
                        options: {
                            limit: 10000,
                            esModule: false,
                            fallback: {
                                loader: 'file-loader',
                                options: {
                                    name: 'addons/media/[name].[hash:7].[ext]'
                                }
                            }
                        }
                    }
                ],
                exclude: [/cli/, /node_modules/, /storage/]
            },
            {
                test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
                use: [
                    {
                        loader: 'url-loader',
                        options: {
                            limit: 10000,
                            esModule: false,
                            fallback: {
                                loader: 'file-loader',
                                options: {
                                    name: 'addons/fonts/[name].[hash:7].[ext]'
                                }
                            }
                        }
                    }
                ],
                exclude: [/cli/, /node_modules/, /storage/]
            }
        ].concat(
            styleLoaders({
                sourceMap: !isProduction,
                usePostCSS: isProduction,
                extract: isProduction
            })
        )
    },

    cache: true,

    performance: {
        hints: false
    },

    node: {
        // Prevent webpack from injecting useless setImmediate polyfill because Vue
        // source contains it (although only uses it if it's native).
        setImmediate: false,
        // Prevent webpack from injecting mocks to Node native modules
        // that does not make sense for the client.
        dgram: 'empty',
        fs: 'empty',
        net: 'empty',
        tls: 'empty',
        child_process: 'empty'
    },

    plugins: [
        new VueLoaderPlugin(),
        new AssetsPlugin({
            filename: 'manifest.json',
            path: path.join(settings.paths.frontendBuildPath, 'addons'),
            includeAllFileTypes: false,
            fileTypes: ['css', 'js'],
            entrypoints: true,
            prettyPrint: true
        }),
        new webpack.NoEmitOnErrorsPlugin()
    ]
};

if (!isProduction) {
    config.mode = 'development';

    config.entry = {
        addons: [
            !!process.env.DEV_SERVER
                ? 'webpack-hot-middleware/client?path=http://**************:3001/__webpack_hmr&noInfo=true&reload=true'
                : 'webpack-hot-middleware/client?path=http://localhost:3001/__webpack_hmr&noInfo=true&reload=true',
            path.join(settings.paths.frontendSrcPath, 'index.js')
        ]
    };

    config.output.filename = path.posix.join('addons', 'js/[name].js');
    config.output.publicPath = 'http://localhost:3001/';
    config.output.globalObject = 'this';

    // config.devtool = 'source-map';

    config.plugins.push(
        new webpack.DefinePlugin({
            'process.env.NODE_ENV': JSON.stringify('development')
        })
    );
    config.plugins.push(new webpack.HotModuleReplacementPlugin());
} else {
    config.mode = 'production';

    config.entry = {
        addons: [path.join(settings.paths.frontendSrcPath, 'index.js')]
    };

    config.output.filename = path.posix.join('addons', 'js/[name].[contenthash].js');
    config.output.chunkFilename = path.posix.join('addons', 'js/[name].[contenthash].js');
    config.output.publicPath = '/';

    config.devtool = false;

    const env = {'process.env.NODE_ENV': JSON.stringify('production')};
    config.plugins.push(new webpack.DefinePlugin(env));
    config.plugins.push(
        new MiniCssExtractPlugin({
            filename: path.posix.join('addons', 'css/[name].[hash].css')
        })
    );
    config.plugins.push(new webpack.optimize.OccurrenceOrderPlugin());
    config.plugins.push(new webpack.HashedModuleIdsPlugin());

    // Optimization - Chunks and minimizer
    config.optimization = {
        minimizer: [
            new TerserPlugin({
                terserOptions: {
                    ecma: 6,
                    compress: {
                        warnings: false
                    },
                    output: {
                        comments: false
                    }
                },
                sourceMap: false,
                parallel: true
            }),
            new OptimizeCSSPlugin()
        ],
        runtimeChunk: false,
        splitChunks: {
            minSize: 0
        }
    };
}

function cssLoaders(options = {}) {
    const cssLoader = {
        loader: 'css-loader',
        options: {
            sourceMap: options.sourceMap
        }
    };

    const postcssLoader = {
        loader: 'postcss-loader',
        options: {
            sourceMap: options.sourceMap
        }
    };

    // Generate loader string to be used with extract text plugin.
    function generateLoaders(loader, loaderOptions) {
        const loaders = options.usePostCSS ? [cssLoader, postcssLoader] : [cssLoader];

        if (loader) {
            if (loader === 'fast-sass') {
                loaders.push({
                    loader: loader + '-loader',
                    options: Object.assign({}, loaderOptions, {
                        includePaths: [path.join(settings.paths.frontendSrcPath, 'assets/scss')],
                        sourceMap: options.sourceMap
                    })
                });
            } else {
                loaders.push({
                    loader: loader + '-loader',
                    options: Object.assign({}, loaderOptions, {
                        sourceMap: options.sourceMap
                    })
                });
            }
        }

        // Extract CSS when that option is specified.
        // (which is the case during production build)
        if (options.extract) {
            return [MiniCssExtractPlugin.loader].concat(loaders);
        } else {
            return ['vue-style-loader'].concat(loaders);
        }
    }

    // https://vue-loader.vuejs.org/en/configurations/extract-css.html
    return {
        css: generateLoaders(),
        postcss: generateLoaders(),
        // scss: generateLoaders('sass')
        scss: generateLoaders('fast-sass')
    };
}

function styleLoaders(options) {
    const output = [];
    const loaders = cssLoaders(options);

    for (const extension in loaders) {
        const loader = loaders[extension];

        output.push({
            test: new RegExp('\\.' + extension + '$'),
            use: loader,
            exclude: [/cli/, /node_modules/, /storage/]
        });
    }

    return output;
}

module.exports = config;
