const path = require('path');
const _ = require('lodash');
const {fork} = require('child_process');
const settings = require('../settings');
const {clean, log, timeDiff} = require('../utils');

process.env.NODE_ENV = 'development';

log('info', 'Please wait! Starting application..');

// Clean build results.
clean();

let frontendStartTime = null;
let frontendRunCount = 0;
const frontendScript = path.resolve(path.join(__dirname, '../compiler/scripts/frontend.js'));
const frontendProcess = fork(frontendScript, [], {
    stdio: 'inherit',
    env: {
        NODE_ENV: 'development'
    }
});
frontendProcess.on('message', message => {
    if (message === 'compiled') {
        const frontendEndTime = new Date();

        log('success', `Frontend compilation is completed in ${timeDiff(frontendStartTime, frontendEndTime)}`);

        if (frontendRunCount === 0) {
            let backendStartTime = new Date();
            const backendScript = path.resolve(path.join(__dirname, '../compiler/scripts/backend.js'));
            const backendProcess = fork(backendScript, [], {
                stdio: 'inherit',
                env: {
                    NODE_ENV: 'development'
                }
            });
            backendProcess.on('message', message => {
                if (message === 'started') {
                    log('info', `Application running at http://${settings.server.host}:${settings.server.port}`);
                } else if (message === 'restarted') {
                    log('info', 'Backend is restarted!');
                } else if (message === 'compiling') {
                    backendStartTime = new Date();

                    log('info', 'Compiling backend..');
                } else if (message === 'compiled') {
                    const backendEndTime = new Date();

                    log('success', `Backend compilation is completed in ${timeDiff(backendStartTime, backendEndTime)}`);
                } else if (_.isObject(message) && message.hasErrors) {
                    log('error', 'Backend compilation is completed with errors');

                    console.error(message.error);
                }
            });
            backendProcess.on('close', code => () => {
                frontendProcess.exit(code);
                process.exit(code);
            });
            backendProcess.on('error', error => {
                console.error(error);

                frontendProcess.exit(1);
                process.exit(1);
            });
        }

        frontendRunCount++;
    } else if (message === 'compiling') {
        frontendStartTime = new Date();

        log('info', 'Compiling frontend..');
    } else if (_.isObject(message) && message.hasErrors) {
        log('error', 'Frontend compilation is completed with errors');

        console.error(message.error);
    }
});
frontendProcess.on('close', code => process.exit(code));
frontendProcess.on('error', error => {
    console.error(error);

    process.exit(1);
});
