const webpack = require('webpack');
const {clean, log, timeDiff} = require('../utils');

process.env.NODE_ENV = 'production';

log('info', 'Please wait! Building for production..');

// Clean build results.
clean(true);

const startTime = new Date();
webpack([
    require('../compiler/config/backend'),
    require('../compiler/config/frontend')
], (error, stats) => {
    if (error) {
        log('error', error.message);

        return;
    }

    const endTime = new Date();

    log('success', `Build process is completed in ${timeDiff(startTime, endTime)}`);

    process.exit(0);
});
