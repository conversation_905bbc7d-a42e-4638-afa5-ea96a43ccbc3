@mixin clearfix() {
    &::after {
        display: block;
        clear: both;
        content: "";
    }
}

@mixin no-select() {
    -webkit-tap-highlight-color: transparent;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

@mixin size($width, $height: $width) {
    width: $width;
    height: $height;
}

// Color contrast
@mixin cy($color) {
    $r: red($color);
    $g: green($color);
    $b: blue($color);
    $yiq: (($r * 299) + ($g * 587) + ($b * 114)) / 1000;

    @if ($yiq >= 210) {
        color: $primary;
    } @else {
        color: #fff;
    }
}

@mixin font-icon($icon, $font-weight: $font-weight) {
    display: inline-block;
    font-family: 'Font Awesome 5 Pro';
    font-weight: $font-weight;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
    content: fa-content($icon);
}
