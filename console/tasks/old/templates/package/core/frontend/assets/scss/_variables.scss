@import "mixins/palate";

//---------------------------------
// MAIN COLORS
//---------------------------------
// Dark colors
$dark-base: #909399;
$dark-50: $dark-base;
$dark-100: mix(#000, $dark-base, 10%);
$dark-200: mix(#000, $dark-base, 20%);
$dark-300: mix(#000, $dark-base, 30%);
$dark-400: mix(#000, $dark-base, 40%);
$dark-500: mix(#000, $dark-base, 50%);
$dark-600: mix(#000, $dark-base, 60%);
$dark-700: mix(#000, $dark-base, 70%);
$dark-800: mix(#000, $dark-base, 80%);
$dark-900: mix(#000, $dark-base, 90%);
$dark-1000: mix(#000, $dark-base, 95%);
$dark: $dark-900;

// Light-colors
$light-base: $dark-base;
$light-25: #F4F4F6;
$light-50: #E9EBED;
$light-100: #DFE1E2;
$light-200: #CCC;
$light-300: mix(#fff, $light-base, 60%);
$light-400: mix(#fff, $light-base, 50%);
$light-500: mix(#fff, $light-base, 40%);
$light-600: mix(#fff, $light-base, 30%);
$light-700: mix(#fff, $light-base, 20%);
$light-800: mix(#fff, $light-base, 10%);
$light-900: $light-base;
$light: $light-50;

// Brand colors
$primary: #0078d4;
$secondary: #112B42;
$success: palate('light-green', '600');
$info: palate('light-blue', '500');
$warning: palate('amber', '600');
$danger: palate('red', '600');
$default: $dark-200;

// Light colors
$primary-light: mix(#fff, $primary, 80%);
$success-light: mix(#fff, $success, 80%);
$info-light: mix(#fff, $info, 80%);
$warning-light: mix(#fff, $warning, 80%);
$danger-light: mix(#fff, $danger, 80%);
$primary-lighter: mix(#fff, $primary, 90%);
$success-lighter: mix(#fff, $success, 90%);
$info-lighter: mix(#fff, $info, 90%);
$warning-lighter: mix(#fff, $warning, 90%);
$danger-lighter: mix(#fff, $danger, 90%);

//---------------------------------
// COMMON ELEMENT COLORS
//---------------------------------
// Common background colors
$bg-color: $light-50;
$body-bg: linear-gradient(to left top, #009bd2, #034e6f);

// Text colors
$text-color: #112B42;
$text-color-light: #415669;
$text-color-lighter: #7F878A;

// Border colors
$border-color: #dcdfe6;
$border-color-dark: #C0C4CC;
$border-color-light: #ebeef5;

// Box shadows
$box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);
$box-shadow-dark: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .12);
$box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

//---------------------------------
// FONT SETTINGS
//---------------------------------
$font-family: '72full', Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
//$font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
$font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
$font-size: 13px;
$font-weight: 400;
$font-weight-bold: 700;
$line-height: 1.428571;

//---------------------------------
// MIXED SETTINGS
//---------------------------------
$spacer: 10px;
$border-radius: 4px;
$border-radius-large: 6px;
$fonts-path: "../fonts";

//---------------------------------
// COMPONENT SETTINGS
//---------------------------------

// Sell
$shell-transparent-bg: rgba(#fff, 0.3);
$shell-taskbar-height: 45px;
$shell-sidebar-width: 360px;

// Program
$program-topbar-height: 50px;
$program-menu-width: 50px;
$program-enlarged-menu-width: 210px;
