@mixin reset-text {
    font-family: $font-family;
    // We deliberately do NOT reset font-size or word-wrap.
    font-style: normal;
    font-weight: $font-weight;
    line-height: $line-height;
    text-align: left; // Fallback for where `start` is not supported
    text-align: start;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    letter-spacing: normal;
    word-break: normal;
    word-spacing: normal;
    white-space: normal;
    line-break: auto;
}

// CSS image replacement
@mixin text-hide() {
    font: 0/0 a;
    color: transparent;
    text-shadow: none;
    background-color: transparent;
    border: 0;
}

// Text truncate
// Requires inline-block or block for proper styling
@mixin text-truncate() {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

// Truncate text and add an ellipsis to represent overflow.
@mixin text-ellipsis($width: 100%, $display: inline-block) {
    display: $display;
    max-width: $width;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-wrap: normal;
}
