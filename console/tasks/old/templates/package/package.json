{"name": "entererp-packaged", "version": "1.0.0", "private": true, "scripts": {"dev": "node ./cli/tasks/dev.js", "build": "node ./cli/tasks/build.js"}, "dependencies": {}, "devDependencies": {"@babel/core": "7.12.3", "@babel/plugin-proposal-class-properties": "7.12.1", "@babel/plugin-proposal-decorators": "7.12.1", "@babel/plugin-syntax-dynamic-import": "7.8.3", "@babel/plugin-transform-runtime": "7.12.1", "@babel/preset-env": "7.12.1", "assets-webpack-plugin": "5.0.1", "autoprefixer": "9.8.6", "babel-loader": "8.1.0", "cache-loader": "4.1.0", "chalk": "4.1.0", "commander": "6.2.0", "css-loader": "3.6.0", "del": "6.0.0", "express": "^4.17.1", "fast-sass-loader": "1.5.0", "file-loader": "6.2.0", "html-loader": "1.3.2", "mini-css-extract-plugin": "1.0.0", "node-sass": "5.0.0", "node-ssh": "11.1.1", "nodemon": "2.0.6", "optimize-css-assets-webpack-plugin": "5.0.4", "postcss-import": "12.0.1", "postcss-loader": "4.0.4", "postcss-url": "8.0.0", "raw-loader": "4.0.2", "source-map-support": "0.5.19", "terser-webpack-plugin": "4.2.3", "thread-loader": "3.0.0", "url-loader": "4.1.1", "vue-loader": "15.9.5", "vue-style-loader": "4.1.2", "vue-template-compiler": "2.6.12", "webpack": "4.41.1", "webpack-bundle-analyzer": "3.9.0", "webpack-dev-middleware": "3.7.2", "webpack-hot-middleware": "2.25.0", "webpack-node-externals": "2.5.2", "worker-loader": "3.0.5"}, "browserslist": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version", "last 1 edge version"]}