{"app": {"name": "entererp", "title": "EnterERP Elegance", "locale": "tr", "timezone": "Europe/Istanbul", "version": "1.0.0"}, "database": {"mongodb": {"host": "127.0.0.1", "port": 27017, "db": "entererp"}, "redis": {"host": "127.0.0.1", "port": 6379}}, "mail": {"host": "", "port": 587, "secure": false, "auth": {"user": "", "password": ""}}, "server": {"protocol": "http", "host": "localhost", "port": 3000}}