{"app": {"name": "entererp", "title": "EnterERP Elegance", "locale": "tr", "timezone": "Europe/Istanbul", "version": "1.0.0"}, "database": {"mongodb": {"host": "localhost", "port": 27017, "db": "entererp"}, "redis": {"host": "localhost", "port": 6379}}, "mail": {"host": "", "port": 587, "secure": false, "auth": {"user": "", "password": ""}}, "server": {"protocol": "http", "host": "localhost", "port": 3000}}