const path = require('path');
const fs = require('fs');
const v8 = require('v8');
const _ = require('lodash');
const shell = require('shelljs');
const archiver = require('archiver');
const del = require('del');
const {minify} = require('terser');
const webpack = require('webpack');
const bytenode = require('./utils/bytenode');
const settings = require('../settings');
const {clean, log, timeDiff} = require('../utils');

v8.setFlagsFromString('--no-lazy');

module.exports = {
    name: 'package',
    description: 'Package application.',
    arguments: '[compile]',
    action(compile) {
        process.env.NODE_ENV = 'production';
        process.env.IS_PACKAGED = 'yes';
        process.env.IS_BUNDLED = 'yes';

        log('info', 'Please wait! Building for packaging..');

        // Clean build results.
        clean(true, true);

        const startTime = new Date();
        webpack([
            require('../compiler/config/backend'),
            require('../compiler/config/frontend'),
            require('../compiler/config/vendors')
        ], (error, stats) => {
            if (error) {
                log('error', error.message);

                return;
            }

            const endTime = new Date();

            log('success', `Build process is completed in ${timeDiff(startTime, endTime)}`);

            log('info', 'Please wait! Packaging...');

            const tempAppPath = path.join(settings.paths.tempPath, 'app');
            shell.exec(`mkdir ${path.join(tempAppPath)}`);
            shell.exec(`mkdir ${path.join(tempAppPath, 'backend')}`);
            shell.exec(`mkdir ${path.join(tempAppPath, 'frontend')}`);
            shell.exec(`mkdir ${path.join(tempAppPath, 'static')}`);
            shell.exec(`mkdir ${path.join(tempAppPath, 'storage')}`);
            shell.exec(`mkdir ${path.join(tempAppPath, 'storage', 'cache')}`);
            shell.exec(`mkdir ${path.join(tempAppPath, 'storage', 'temp')}`);

            shell.cp('-R', path.join(settings.paths.backendBuildPath, '/*'), path.join(tempAppPath, 'backend/'));
            shell.cp('-R', path.join(settings.paths.frontendBuildPath, '/*'), path.join(tempAppPath, 'frontend/'));
            shell.cp('-R', path.join(settings.paths.staticPath, '/*'), path.join(tempAppPath, 'static/'));

            if (!!compile) {
                // Get app js path.
                const appJsPath = path.join(tempAppPath, 'backend/app.js');

                // Force licence check.
                const appJsContent = fs.readFileSync(appJsPath, {encoding: 'utf8'});
                fs.writeFileSync(appJsPath, `global.checkLicence = true;${appJsContent}`);

                // Compile to bytecode.
                bytenode.compileFile({
                    filename: path.join(tempAppPath, 'backend/app.js'),
                    // compileAsModule: false
                });
                fs.unlinkSync(path.join(tempAppPath, 'backend/app.js'));

                // Copy rummer.
                const runnerJsContent = fs.readFileSync(path.join(__dirname, './utils/bytenode.js'), {encoding: 'utf8'});
                fs.writeFileSync(path.join(tempAppPath, 'backend/runner.js'), minify(runnerJsContent).code);

                // Copy main.js
                const mainJsContent = fs.readFileSync(path.join(__dirname, './templates/entererp/main.js'), {encoding: 'utf8'});
                fs.writeFileSync(path.join(tempAppPath, 'main.js'), minify(mainJsContent).code);
            }

            const configJson = JSON.parse(fs.readFileSync(path.join(settings.paths.rootPath, 'config.json')));
            configJson.app.packaged = true;
            delete configJson.deployment;
            fs.writeFileSync(path.join(tempAppPath, 'config.json'), JSON.stringify(configJson, null, 4));

            const pkgJson = JSON.parse(fs.readFileSync(path.join(settings.paths.rootPath, 'package.json')));
            _.each(pkgJson.dependencies, (version, packageName) => {
                if (settings.bundledFrontendPackages.concat(settings.frontEndPackages).indexOf(packageName) !== -1) {
                    delete pkgJson.dependencies[packageName];
                }
            });
            pkgJson.main = 'main.js';
            delete pkgJson.devDependencies;
            fs.writeFileSync(path.join(tempAppPath, 'package.json'), JSON.stringify(pkgJson, null, 4));

            const output = fs.createWriteStream(path.join(settings.paths.rootPath, 'entererp.zip'));
            const archive = archiver('zip', {
                zlib: {level: 9} // Sets the compression level.
            });
            output.on('close', function() {
                del.sync([
                    path.join(settings.paths.tempPath, '**'),
                    '!' + settings.paths.tempPath,
                    '!' + path.join(settings.paths.tempPath, '.gitignore')
                ]);

                log('success', `Application is packaged successfully!`);
            });
            archive.pipe(output);
            archive.directory(tempAppPath, false);
            archive.finalize();
        });
    }
};
