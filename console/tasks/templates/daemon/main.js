const express = require('express');
const shell = require('shelljs');
// const {MongoClient} = require('mongodb');

const exec = (command, options = {}) => {
    return new Promise((resolve, reject) => {
        options = {
            ...(options || {}),
            async: true
        };

        shell.exec(command, options, (code, stdout, stderr) => {
            if (code === 0) {
                resolve(stdout);
            } else {
                reject(stderr);
            }
        });
    });
};

// (async () => {
//     const dbUrl = 'mongodb://127.0.0.1:27017/entererp';
//     const mongoClient = await MongoClient.connect(dbUrl);
//
//     let isResetting = false;
//     mongoClient.on('serverHeartbeatFailed', async () => {
//         if (isResetting) return;
//         isResetting = true;
//         console.log('Resetting the mongodb!!');
//
//         try {
//             await exec('systemctl stop mongod');
//         } catch (error) {}
//         try {
//             await exec(`cp /var/log/mongodb/mongod.log /root/mongod-${new Date().getTime()}.log`);
//         } catch (error) {}
//         try {
//             await exec('rm -rf /var/log/mongodb/*');
//         } catch (error) {}
//         try {
//             await exec('systemctl start mongod');
//         } catch (error) {}
//
//         isResetting = false;
//     });
// })();

const app = express();
app.get('/reload', (req, res) => {
    exec('pm2 reload entererp');

    res.send('ok');
});
app.get('/restart', (req, res) => {
    exec('pm2 restart entererp');

    res.send('ok');
});
app.get('/maintenance', (req, res) => {
    exec('sh /opt/entererp/bin/maintenance.sh');

    res.send('ok');
});
app.listen(2999);
