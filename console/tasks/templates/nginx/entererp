proxy_cache_path /var/cache/nginx-entererp levels=1:2 keys_zone=ENTERERP_STATIC:256m inactive=30d use_temp_path=off;

upstream backend {
    server localhost:3000;
    keepalive 64;
}

server {
    # General.
    listen {{port}};
    listen [::]:{{port}};
    server_name {{host}};
    server_tokens off;

    # Increase max upload size.
    client_max_body_size 100M;

    # Gzip
    gzip on;
    gzip_proxied any;
    gzip_comp_level 4;
    gzip_types text/css application/javascript application/json image/svg+xml font/woff font/woff2;

    # Cache images.
    location ~* ^/.*\\.(?:jpg|jpeg|gif|png|webp|avif|ico|svg|ttf)$ {
        proxy_cache ENTERERP_STATIC;
        proxy_hide_header Cache-Control;
        proxy_ignore_headers Set-Cookie;
        proxy_cache_valid 21600m;
        proxy_pass http://backend;
        access_log off;
        sendfile on;
        sendfile_max_chunk 2m;
        tcp_nopush on;
        tcp_nodelay on;

        add_header Cache-Control "public, max-age=31536000, immutable";

        # For testing cache - remove before deploying to production
        add_header X-Cache-Status $upstream_cache_status;
    }

    # Try to load from nginx first.
    location / {
        try_files $uri @backend;
        access_log off;
    }

    location @backend {
        # General.
        proxy_pass http://backend;
        proxy_redirect off;
    	proxy_read_timeout 240s;
        proxy_max_temp_file_size 0;
        proxy_cache_bypass $http_upgrade;
        access_log off;

        # Headers.
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Following is necessary for Websocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
