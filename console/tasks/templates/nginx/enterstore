proxy_cache_path /var/cache/nginx-enterstore levels=1:2 keys_zone=ENTERSTORE_STATIC:256m inactive=30d use_temp_path=off;

upstream enterstore {
    server localhost:3333;
    keepalive 64;
}

server {
    # General.
    server_name demostore.entererp.com;
    #server_tokens off;
    proxy_http_version 1.1;
    proxy_redirect off;
    proxy_read_timeout 60s;
    #proxy_max_temp_file_size 0;
    proxy_cache_bypass $http_upgrade;
    access_log off;
    server_tokens off;

    # Gzip
    gzip on;
    gzip_proxied any;
    gzip_comp_level 4;
    gzip_types text/css application/javascript application/json image/svg+xml font/woff font/woff2;

    # Increase max upload size.
    client_max_body_size 100M;
    client_body_buffer_size 100M;

    # Headers.
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header Host $host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    # Cache images.
    location ~* ^/.*\\.(?:jpg|jpeg|gif|png|webp|avif|ico|svg|ttf)$ {
        proxy_cache ENTERSTORE_STATIC;
        proxy_hide_header Cache-Control;
        proxy_ignore_headers Set-Cookie;
        proxy_cache_valid 21600m;
        proxy_pass http://enterstore;
        access_log off;
        sendfile on;
        sendfile_max_chunk 2m;
        tcp_nopush on;
        tcp_nodelay on;

        add_header Cache-Control "public, max-age=31536000, immutable";

        # For testing cache - remove before deploying to production
        add_header X-Cache-Status $upstream_cache_status;
    }

    location / {
        proxy_pass http://enterstore;
    }
}
