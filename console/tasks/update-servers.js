const path = require('path');
const fs = require('fs');
const _ = require('lodash');
const shell = require('shelljs');
const archiver = require('archiver');
const del = require('del');
const webpack = require('webpack');
const settings = require('../settings');
const {clean, log, timeDiff} = require('../utils');

module.exports = {
    name: 'update-servers',
    description: 'Update servers',
    async action() {
        const serversToUpdateText = fs.readFileSync(path.join(settings.paths.rootPath, 'servers-to-update.txt'), {
            encoding: 'utf-8'
        });
        const serversToUpdate = _.uniq(
            serversToUpdateText
            .split('\n')
            .map(stu => stu.trim())
            .filter(stu => !!stu)
        );
        if (serversToUpdate.length < 1) return;

        log('info', 'Check ssh connections..');
        for (const stu of serversToUpdate) {
            console.log(stu);
            await new (require('node-ssh').NodeSSH)().connect({
                host: stu,
                port: '22',
                username: 'root',
                privateKeyPath: '/home/<USER>/.ssh/id_rsa'
            });
        }

        const tempAppPath = path.join(settings.paths.tempPath, 'app');
        const configJson = JSON.parse(fs.readFileSync(path.join(settings.paths.rootPath, 'config.json')));
        configJson.app.lastUpdatedAt = new Date();
        configJson.app.packaged = true;
        configJson.server.protocol = 'https';
        configJson.server.host = '';
        configJson.server.port = 80;
        delete configJson.deployment;
        delete configJson.ssh;
        const pkgJson = JSON.parse(fs.readFileSync(path.join(settings.paths.rootPath, 'package.json')));
        _.each(pkgJson.dependencies, (version, packageName) => {
            if (settings.bundledFrontendPackages.concat(settings.frontEndPackages).indexOf(packageName) !== -1) {
                delete pkgJson.dependencies[packageName];
            }
        });
        delete pkgJson.devDependencies;

        // Build
        process.env.NODE_ENV = 'production';
        process.env.IS_PACKAGED = 'yes';

        log('info', 'Please wait! Building for packaging..');

        clean(true, true);

        await new Promise((resolve, reject) => {
            webpack(
                [
                    require('../compiler/config/backend'),
                    require('../compiler/config/frontend'),
                    require('../compiler/config/vendors')
                ],
                (error, stats) => {
                    if (stats.hasErrors()) {
                        const jsonStats = stats.toJson();

                        console.log(jsonStats);

                        return;
                    }

                    if (error) {
                        log('error', error.message);

                        reject();

                        return;
                    }

                    resolve();
                }
            );
        });

        log('success', `Build process is completed!`);

        log('info', 'Please wait! Packaging...');

        shell.exec(`mkdir ${path.join(tempAppPath)}`);

        let promises = [];
        for (const stu of serversToUpdate) {
            promises.push(
                new Promise(resolve => {
                    const tempStuAppPath = path.join(tempAppPath, stu);

                    shell.exec(`mkdir ${path.join(tempStuAppPath)}`);
                    shell.exec(`mkdir ${path.join(tempStuAppPath, 'bin')}`);
                    shell.exec(`mkdir ${path.join(tempStuAppPath, 'backend')}`);
                    shell.exec(`mkdir ${path.join(tempStuAppPath, 'daemon')}`);
                    shell.exec(`mkdir ${path.join(tempStuAppPath, 'frontend')}`);
                    shell.exec(`mkdir ${path.join(tempStuAppPath, 'static')}`);
                    shell.exec(`mkdir ${path.join(tempStuAppPath, 'storage')}`);
                    shell.exec(`mkdir ${path.join(tempStuAppPath, 'storage', 'cache')}`);
                    shell.exec(`mkdir ${path.join(tempStuAppPath, 'storage', 'temp')}`);

                    shell.cp(
                        '-R',
                        path.join(settings.paths.rootPath, '/bin/linux/*'),
                        path.join(tempStuAppPath, 'bin/')
                    );
                    shell.cp(
                        '-R',
                        path.join(settings.paths.backendBuildPath, '/*'),
                        path.join(tempStuAppPath, 'backend/')
                    );
                    shell.cp('-R', path.join(__dirname, 'templates/daemon/*'), path.join(tempStuAppPath, 'daemon/'));
                    shell.cp(
                        '-R',
                        path.join(settings.paths.frontendBuildPath, '/*'),
                        path.join(tempStuAppPath, 'frontend/')
                    );
                    shell.cp('-R', path.join(settings.paths.staticPath, '/*'), path.join(tempStuAppPath, 'static/'));
                    shell.cp('-R', path.join(settings.paths.rootPath, '/yarn.lock'), path.join(tempStuAppPath, '/'));

                    fs.writeFileSync(
                        path.join(tempStuAppPath, 'config.json'),
                        JSON.stringify(
                            {
                                ...configJson,
                                server: {
                                    ...configJson.server,
                                    host: stu
                                }
                            },
                            null,
                            4
                        )
                    );

                    fs.writeFileSync(path.join(tempStuAppPath, 'package.json'), JSON.stringify(pkgJson, null, 4));

                    const output = fs.createWriteStream(
                        path.join(settings.paths.tempPath, `${stu.replaceAll('.', '_')}.zip`)
                    );
                    const archive = archiver('zip', {
                        zlib: {level: 9} // Sets the compression level.
                    });
                    output.on('close', function () {
                        resolve();
                    });
                    archive.pipe(output);
                    archive.directory(tempStuAppPath, false);
                    archive.finalize();
                })
            );
        }

        await Promise.all(promises);

        log('success', `Packing is completed!`);

        log('info', 'Please wait! Deploying to remote servers..');

        promises = [];
        for (const stu of serversToUpdate) {
            promises.push(
                new Promise(async resolve => {
                    try {
                        const tempStuAppPath = path.join(tempAppPath, stu);

                        const ssh = await new (require('node-ssh').NodeSSH)().connect({
                            host: stu,
                            port: '22',
                            username: 'root',
                            privateKeyPath: '/home/<USER>/.ssh/id_rsa'
                        });
                        await ssh.execCommand('pm2 stop entererp');
                        await ssh.execCommand('rm -rf /opt/entererp');
                        await ssh.execCommand('mkdir /opt/entererp');
                        let nginxTemplate = template(
                            fs.readFileSync(path.join(__dirname, 'templates/nginx/entererp')),
                            {
                                host: stu,
                                port: 80
                            }
                        );
                        fs.writeFileSync(path.join(tempStuAppPath, 'nginx.conf'), nginxTemplate);
                        await ssh.execCommand('rm -rf /etc/nginx/sites-available/entererp');
                        await ssh.execCommand('rm -rf /etc/nginx/sites-enabled/entererp');
                        await ssh.putFile(
                            path.join(tempStuAppPath, 'nginx.conf'),
                            '/etc/nginx/sites-available/entererp'
                        );
                        await ssh.execCommand('ln -s /etc/nginx/sites-available/entererp /etc/nginx/sites-enabled/');
                        await ssh.putFile(
                            path.join(settings.paths.tempPath, `${stu.replaceAll('.', '_')}.zip`),
                            '/opt/entererp.zip'
                        );
                        await ssh.execCommand('unzip /opt/entererp.zip -d /opt/entererp');
                        await ssh.execCommand('rm /opt/entererp.zip');
                        await ssh.execCommand('yarn install --production --ignore-engines', {
                            cwd: '/opt/entererp'
                        });
                        await ssh.execCommand('yarn install --production --ignore-engines', {
                            cwd: '/opt/entererp/daemon'
                        });
                        await ssh.execCommand('pm2 flush');
                        await ssh.execCommand('systemctl stop mongod');
                        await ssh.execCommand('systemctl stop postgresql');
                        await ssh.execCommand('systemctl stop redis');
                        await ssh.execCommand('systemctl stop nginx');
                        await ssh.execCommand('rm -rf /var/log/mongodb/*');
                        await ssh.execCommand('rm -rf /var/log/journal/*');
                        await ssh.execCommand('rm -rf /var/log/nginx/*');
                        await ssh.execCommand('rm -rf /var/log/postgresql/*');
                        await ssh.execCommand('rm -rf /var/log/alternatives.log.*');
                        await ssh.execCommand('rm -rf /var/log/apport.log.*');
                        await ssh.execCommand('rm -rf /var/log/syslog.*');
                        await ssh.execCommand('rm -rf /tmp/entererp-*');
                        await ssh.execCommand('apt autoclean');
                        await ssh.execCommand('apt --purge autoremove -y');
                        await ssh.execCommand('systemctl start mongod');
                        await ssh.execCommand('systemctl start postgresql');
                        await ssh.execCommand('systemctl start redis');
                        await ssh.execCommand('systemctl start nginx');
                        await ssh.execCommand('redis-cli FLUSHALL');
                        await ssh.execCommand('pm2 restart entererp-daemon');
                        await ssh.execCommand('pm2 save');
                        await ssh.execCommand('pm2 restart entererp');
                        await ssh.execCommand('pm2 save');
                        await ssh.execCommand(
                            `certbot --nginx -d ${stu} -n --agree-tos --redirect -m <EMAIL>`
                        );
                        try {
                            await ssh.execCommand(
                                'apt update && apt upgrade -y && sh /opt/entererp/bin/maintenance.sh && reboot'
                            );
                        } catch (error) {}
                    } catch (error) {
                        console.log(stu, error);
                    }

                    resolve();
                })
            );
        }

        await Promise.all(promises);

        del.sync([
            path.join(settings.paths.tempPath, '**'),
            '!' + settings.paths.tempPath,
            '!' + path.join(settings.paths.tempPath, '.gitignore')
        ]);
        log('success', `Deployment is completed!`);
        process.exit(0);
    }
};

function template(str, data) {
    // Use custom template delimiters.
    _.templateSettings.escape = /{{([\s\S]+?)}}/g;
    _.templateSettings.interpolate = /{_([\s\S]+?)}}/g;
    _.templateSettings.evaluate = /{#([\s\S]+?)}}/g;

    let compiled = _.template(str);

    return compiled(data);
}
