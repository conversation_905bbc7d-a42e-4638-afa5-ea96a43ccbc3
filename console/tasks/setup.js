const path = require('path');
const {log} = require('../utils');
const ssh = new (require('node-ssh').NodeSSH)();

module.exports = {
    name: 'setup',
    description: 'Setup remote server.',
    async action() {
        const sshConfig = require('../../config.json').ssh;
        let result = null;

        // Connect to server.
        log('info', 'Please wait! Connecting to remote server..');
        try {
            await ssh.connect({
                host: sshConfig.host,
                port: sshConfig.port || '22',
                username: sshConfig.username,
                privateKeyPath: sshConfig.privateKey
            });
        } catch (e) {
            log('error', `Could not connect to the server! Reason: ${e.message}`);

            process.exit(1);
        }
        await ssh.execCommand('export DEBIAN_FRONTEND=noninteractive');
        log('success', 'Successfully connected to the remote server.');

        // Cleanup.
        log('info', 'Updating and cleaning existing packages..');
        result = await ssh.execCommand('apt update');
        if (result.code > 0) exitWithError();
        result = await ssh.execCommand('apt upgrade -y');
        if (result.code > 0) exitWithError();
        result = await ssh.execCommand('apt --purge autoremove -y');
        if (result.code > 0) exitWithError();
        result = await ssh.execCommand('apt autoclean');
        if (result.code > 0) exitWithError();

        // Update and install required initial packages.
        log('info', 'Updating and installing required initial packages..');
        result = await ssh.execCommand(
            'apt install -y software-properties-common build-essential gnupg curl wget ufw openssl nginx unzip gconf-service libasound2 libatk1.0-0 libc6 libcairo2 libcups2 libdbus-1-3 libexpat1 libfontconfig1 libgcc1 libgconf-2-4 libgdk-pixbuf2.0-0 libglib2.0-0 libgtk-3-0 libnspr4 libpango-1.0-0 libpangocairo-1.0-0 libstdc++6 libx11-6 libx11-xcb1 libxcb1 libxcomposite1 libxcursor1 libxdamage1 libxext6 libxfixes3 libxi6 libxrandr2 libxrender1 libxss1 libxtst6 ca-certificates fonts-liberation libappindicator1 libnss3 lsb-release xdg-utils libgbm-dev whois'
        );
        if (result.code > 0) exitWithError();
        result = await ssh.execCommand('ln -fs /usr/share/zoneinfo/Europe/Istanbul /etc/localtime');
        if (result.code > 0) exitWithError();
        result = await ssh.execCommand('apt install -y tzdata');
        if (result.code > 0) exitWithError();
        result = await ssh.execCommand('dpkg-reconfigure --frontend noninteractive tzdata');
        if (result.code > 0) exitWithError();
        result = await ssh.execCommand('locale-gen tr_TR.UTF-8');
        if (result.code > 0) exitWithError();
        result = await ssh.execCommand('update-locale');
        if (result.code > 0) exitWithError();
        result = await ssh.execCommand('fallocate -l 4G /swapfile');
        if (result.code > 0) exitWithError();
        result = await ssh.execCommand('chmod 600 /swapfile');
        if (result.code > 0) exitWithError();
        result = await ssh.execCommand('mkswap /swapfile');
        if (result.code > 0) exitWithError();
        result = await ssh.execCommand('swapon /swapfile');
        if (result.code > 0) exitWithError();
        result = await ssh.execCommand(`echo '/swapfile swap swap defaults 0 0' >> /etc/fstab`);
        if (result.code > 0) exitWithError();
        log('success', 'Successfully installed required initial packages.');

        // Install certbot.
        log('info', 'Installing certbot.');
        result = await ssh.execCommand('apt install certbot python3-certbot-nginx -y');
        if (result.code > 0) exitWithError();
        log('success', 'Successfully installed certbot.');

        // UFW defaults
        log('info', 'Updating firewall settings..');
        result = await ssh.execCommand(
            `ufw --force enable && ufw allow ssh && ufw allow http && ufw allow https && ufw allow 'Nginx Full'`
        );
        if (result.code > 0) exitWithError();
        log('success', 'Successfully updated firewall settings.');

        // Install yarn
        log('info', 'Installing yarn..');
        result = await ssh.execCommand('curl -sS https://dl.yarnpkg.com/debian/pubkey.gpg | apt-key add -');
        if (result.code > 0) exitWithError();
        result = await ssh.execCommand(
            'echo "deb https://dl.yarnpkg.com/debian/ stable main" | tee /etc/apt/sources.list.d/yarn.list'
        );
        if (result.code > 0) exitWithError();
        result = await ssh.execCommand('apt update && apt install -y yarn');
        log('success', 'Successfully installed yarn.');

        // Install nodejs
        // apt remove nodejs -y && apt --purge autoremove -y && apt autoclean && curl -sL https://deb.nodesource.com/setup_20.x | sudo -E bash - && apt install -y nodejs
        log('info', 'Installing nodejs..');
        result = await ssh.execCommand('apt remove nodejs -y');
        result = await ssh.execCommand('apt --purge autoremove -y');
        result = await ssh.execCommand('apt autoclean');
        result = await ssh.execCommand('curl -sL https://deb.nodesource.com/setup_20.x | sudo -E bash -');
        if (result.code > 0) exitWithError();
        result = await ssh.execCommand('apt install -y nodejs');
        if (result.code > 0) exitWithError();
        result = await ssh.execCommand('yarn global add pm2');
        if (result.code > 0) exitWithError();
        result = await ssh.execCommand('pm2 startup');
        if (result.code > 0) exitWithError();
        log('success', 'Successfully installed nodejs.');

        // Install redis
        log('info', 'Installing redis..');
        result = await ssh.execCommand('curl -O http://download.redis.io/redis-stable.tar.gz', {cwd: '/tmp'});
        if (result.code > 0) exitWithError();
        await ssh.execCommand('tar xzvf redis-stable.tar.gz', {cwd: '/tmp'});
        result = await ssh.execCommand('make', {cwd: '/tmp/redis-stable'});
        if (result.code > 0) exitWithError();
        result = await ssh.execCommand('make install', {cwd: '/tmp/redis-stable'});
        if (result.code > 0) exitWithError();
        await ssh.execCommand('mkdir /etc/redis');
        await ssh.execCommand('cp /tmp/redis-stable/redis.conf /etc/redis');
        await ssh.execCommand('sed -i "s/^supervised no/supervised systemd/" /etc/redis/redis.conf');
        await ssh.execCommand('sed -i "s/^dir \\.\\//dir \\/var\\/lib\\/redis/" /etc/redis/redis.conf');
        result = await ssh.execCommand(
            'wget -O /etc/systemd/system/redis.service https://gist.githubusercontent.com/clzola/2b98f0b128d94811e43f86412dc375a1/raw/d2b1475ba3c44d6b3341a724166004e366a7304b/redis.service'
        );
        if (result.code > 0) exitWithError();
        await ssh.execCommand('adduser --system --group --no-create-home redis');
        await ssh.execCommand('mkdir /var/lib/redis');
        await ssh.execCommand('chown redis:redis /var/lib/redis');
        await ssh.execCommand('chmod 770 /var/lib/redis');
        await ssh.execCommand('systemctl daemon-reload');
        await ssh.execCommand('systemctl start redis');
        await ssh.execCommand('systemctl enable redis');
        await ssh.execCommand('rm -rf /tmp/redis-stable');
        await ssh.execCommand('rm /tmp/redis-stable.tar.gz');
        log('success', 'Successfully installed redis.');

        // Install mongodb
        log('info', 'Installing mongodb..');
        result = await ssh.execCommand(
            'wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -'
        );
        if (result.code > 0) exitWithError();
        result = await ssh.execCommand(
            'echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list'
        );
        if (result.code > 0) exitWithError();
        await ssh.execCommand('apt update');
        result = await ssh.execCommand('apt install -y mongodb-org');
        if (result.code > 0) exitWithError();
        await ssh.execCommand('systemctl daemon-reload');
        await ssh.execCommand('systemctl start mongod');
        await ssh.execCommand('systemctl enable mongod');
        log('success', 'Successfully installed mongodb.');

        // Install mongosqld
        // log('info', 'Installing mongosql..');
        // await ssh.execCommand('mkdir /root/mongosql');
        // await ssh.execCommand('mkdir /var/log/mongosqld');
        // await ssh.execCommand('touch /var/log/mongosqld/mongosqld.log');
        // try {
        //     await ssh.putFiles([
        //         {local: path.join(__dirname, 'templates/mongosql/mongodrdl'), remote: '/root/mongosql/mongodrdl'},
        //         {local: path.join(__dirname, 'templates/mongosql/mongosqld'), remote: '/root/mongosql/mongosqld'},
        //         {local: path.join(__dirname, 'templates/mongosql/mongotranslate'), remote: '/root/mongosql/mongotranslate'},
        //         // {local: path.join(__dirname, 'templates/mongosql/mongosqld.conf'), remote: '/root/mongosql/mongosqld.conf'}
        //     ]);
        // } catch (e) {
        //     log(
        //         'error',
        //         `Could not put file to remote destination! Reason: ${e.message}`
        //     );
        //
        //     process.exit(1);
        // }
        // await ssh.execCommand('install -m755 /root/mongosql/mongodrdl /usr/local/bin/');
        // await ssh.execCommand('install -m755 /root/mongosql/mongosqld /usr/local/bin/');
        // await ssh.execCommand('install -m755 /root/mongosql/mongotranslate /usr/local/bin/');
        // await ssh.execCommand('rm -rf /root/mongosql');
        // await ssh.execCommand('mongosqld install --config /root/mongosql/mongosqld.conf');
        // await ssh.execCommand('systemctl start mongosql');
        // await ssh.execCommand('systemctl enable mongosql');
        // log('success', 'Successfully installed mongosql.');

        // Install PostgreSQL
        log('info', 'Installing PostgreSQL..');
        result = await ssh.execCommand('apt install postgresql postgresql-contrib -y');
        if (result.code > 0) exitWithError();
        result = await ssh.execCommand("sudo -i -u postgres createdb entererp -l 'tr_TR.UTF-8' -T template0");
        if (result.code > 0) exitWithError();
        await ssh.execCommand('systemctl restart postgresql');
        await ssh.execCommand('systemctl enable postgresql');
        // result = await ssh.execCommand(`echo -e 'host    entererp        all             127.0.0.1/32            trust\n$(cat /etc/postgresql/12/main/pg_hba.conf)' > /etc/postgresql/12/main/pg_hba.conf`);
        // if (result.code > 0) exitWithError();
        // await ssh.execCommand('systemctl restart postgresql');
        log('success', 'Successfully installed PostgreSQL.');

        // Prepare nginx.
        await ssh.execCommand('rm -rf /etc/nginx/sites-enabled/default');
        await ssh.execCommand('systemctl stop nginx');
        await ssh.execCommand('systemctl start nginx');
        await ssh.execCommand('systemctl enable nginx');

        // Restore db.
        log('info', 'Restoring DB..');
        try {
            await ssh.putFile(path.join(__dirname, 'templates/db.gz'), '/root/db.gz');
        } catch (e) {
            log('error', `Could not put file to remote destination! Reason: ${e.message}`);

            process.exit(1);
        }
        result = await ssh.execCommand('mongorestore -d entererp --archive=/root/db.gz --gzip');
        if (result.code > 0) exitWithError();
        await ssh.execCommand('rm /root/db.gz');
        log('success', 'Successfully restored DB.');

        process.exit(0);
    }
};

function exitWithError() {
    log('error', 'Setup failed!');

    process.exit(1);
}
