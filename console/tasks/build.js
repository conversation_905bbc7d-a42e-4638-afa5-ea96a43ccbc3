const webpack = require('webpack');
const {clean, log, timeDiff} = require('../utils');

module.exports = {
    name: 'build',
    description: 'Build application.',
    action() {
        process.env.NODE_ENV = 'production';

        log('info', 'Please wait! Building for production..');

        // Clean build results.
        clean(true, true);

        const startTime = new Date();
        webpack([
            require('../compiler/config/backend'),
            require('../compiler/config/frontend'),
            require('../compiler/config/vendors')
        ], (error, stats) => {
            if (stats.hasErrors()) {
                const jsonStats = stats.toJson();

                console.log(jsonStats);

                return;;
            }

            if (error) {
                log('error', error.message);

                return;
            }

            const endTime = new Date();

            log('success', `Build process is completed in ${timeDiff(startTime, endTime)}`);
        });
    }
};
