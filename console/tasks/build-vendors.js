const webpack = require('webpack');
const {clean, log, timeDiff} = require('../utils');

module.exports = {
    name: 'build:vendors',
    description: 'Build vendors.',
    action() {
        process.env.NODE_ENV = 'production';

        log('info', 'Please wait! Building vendors..');

        // Clean build results.
        clean(true, true);

        const startTime = new Date();
        webpack(require('../compiler/config/vendors'), (error, stats) => {
            if (error) {
                log('error', error.message);

                return;
            }

            const endTime = new Date();

            log('success', `Build process is completed in ${timeDiff(startTime, endTime)}`);
        });
    }
};
