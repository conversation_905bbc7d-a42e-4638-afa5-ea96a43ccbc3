const path = require('path');
const appConfig = require('../config.json');

const rootPath = path.resolve(process.cwd());
const srcPath = path.join(rootPath, 'kernel');
const backendSrcPath = path.join(srcPath, 'backend');
const frontendSrcPath = path.join(srcPath, 'frontend');
const buildPath = path.join(rootPath, 'storage/build');
const backendBuildPath = path.join(buildPath, 'backend');
const frontendBuildPath = path.join(buildPath, 'frontend');
const vendorsBuildPath = path.join(frontendBuildPath, 'vendors');
const staticPath = path.join(rootPath, 'static');
const tempPath = path.join(rootPath, 'storage/temp');
const cachePath = path.join(rootPath, 'storage/cache');
const userNodeModulesPath = path.join(rootPath, 'node_modules');

module.exports = {
    // App
    app: {
        title: appConfig.app.title,
        version: appConfig.app.version
    },

    // Server
    server: {
        host: appConfig.server.host,
        port: appConfig.server.port
    },

    // Paths
    paths: {
        rootPath,
        srcPath,
        backendSrcPath,
        frontendSrcPath,
        buildPath,
        backendBuildPath,
        frontendBuildPath,
        vendorsBuildPath,
        staticPath,
        tempPath,
        cachePath,
        userNodeModulesPath
    },

    bundledFrontendPackages: [
        'vue',
        'react',
        'react-dom',
        'element-ui',
        'jquery',
        'perfect-scrollbar',
        'resize-observer-polyfill',
        'masonry-layout',
        '@riophae/vue-treeselect',
        'element-resize-detector',
        '@ag-grid-community/all-modules',
        '@ag-grid-community/vue',
        '@ag-grid-enterprise/all-modules',
        // '@amcharts/amcharts4',
        // 'fusioncharts',
        'highcharts',
        '@fullcalendar/core',
        '@fullcalendar/daygrid',
        '@fullcalendar/interaction',
        '@fullcalendar/list',
        '@fullcalendar/luxon',
        '@fullcalendar/rrule',
        '@fullcalendar/timegrid',
        '@interactjs/interactjs',
        '@interactjs/auto-start',
        '@interactjs/actions',
        '@interactjs/modifiers',
        '@interactjs/dev-tools',
        'quill',
        'quill-delta',
        'mousetrap',
        'perfect-scrollbar',
        'highcharts',
        'codeflask',
        '@cubejs-client/core'
    ],
    frontEndPackages: [
        '@feathersjs/socketio-client',
        '@riophae/vue-treeselect',
        'element-resize-detector',
        'element-ui',
        'vue',
        'vue-class-component',
        'vue-deepset',
        'vue-property-decorator',
        'vue-router',
        'vuex',
        'vue-easy-dnd'
    ]
};
