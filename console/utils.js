const path = require('path');
const colors = require('chalk');
const del = require('del');
const settings = require('./settings');
const DateTime = require('luxon').DateTime;
const utils = {};

utils.log = function(level, message) {
    const time = DateTime.local().toFormat('HH:mm:ss');

    switch (level) {
        case 'success':
            console.log(`${colors.gray(`[${time}]`)} ${colors.bold.green('SUCCESS:')} ${message}`);
            break;
        case 'info':
            console.log(`${colors.gray(`[${time}]`)} ${colors.bold.blue('INFO:')} ${message}`);
            break;
        case 'warning':
            console.log(`${colors.gray(`[${time}]`)} ${colors.bold.yellow('WARNING:')} ${message}`);
            break;
        case 'error':
            console.log(`${colors.gray(`[${time}]`)} ${colors.bold.red('ERROR:')} ${message}`);
            break;
    }
};

utils.clean = function(cleanCache = false, cleanAll = false) {
    if (cleanCache) {
        del.sync([
            path.join(settings.paths.userNodeModulesPath, '.cache'),
        ]);

        del.sync([
            path.join(settings.paths.cachePath, '**'),
            '!' + settings.paths.cachePath,
            '!' + path.join(settings.paths.cachePath, '.gitignore')
        ]);
    }

    del.sync([
        path.join(settings.paths.backendBuildPath, '**'),
        '!' + settings.paths.backendBuildPath,
        '!' + path.join(settings.paths.backendBuildPath, '.gitignore')
    ]);
    if (cleanAll) {
        del.sync([
            path.join(settings.paths.frontendBuildPath, '**'),
            '!' + settings.paths.frontendBuildPath,
            '!' + path.join(settings.paths.frontendBuildPath, '.gitignore')
        ]);
    } else {
        del.sync([
            path.join(settings.paths.frontendBuildPath, '**'),
            '!' + settings.paths.frontendBuildPath,
            '!' + settings.paths.vendorsBuildPath,
            '!' + path.join(settings.paths.vendorsBuildPath, '**'),
            '!' + path.join(settings.paths.frontendBuildPath, '.gitignore')
        ]);
    }
    del.sync([
        path.join(settings.paths.tempPath, '**'),
        '!' + settings.paths.tempPath,
        '!' + path.join(settings.paths.tempPath, '.gitignore')
    ]);
};

utils.timeDiff = function(start, end) {
    const diff = end.getTime() - start.getTime();
    const seconds = parseInt(diff / 1000);
    const milliseconds = diff % 1000;

    if (seconds > 0) {
        return `${seconds} ${seconds > 1 ? 'seconds' : 'second'} ${milliseconds} ${milliseconds > 1 ? 'milliseconds' : 'millisecond'}`;
    } else {
        return `${milliseconds} ${milliseconds > 1 ? 'milliseconds' : 'millisecond'}`;
    }
};

module.exports = utils;
