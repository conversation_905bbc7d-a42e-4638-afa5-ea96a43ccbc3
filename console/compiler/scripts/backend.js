const path = require('path');
const _ = require('lodash');
const webpack = require('webpack');
const nodemon = require('nodemon');
const settings = require('../../settings');
const config = require('../config/backend');

process.on('SIGINT', process.exit);

const compiler = webpack(config);

compiler.hooks.watchRun.tapAsync('EnterERPWatchRunPlugin', (compiler, callback) => {
    process.send('compiling');

    callback();
});
compiler.hooks.done.tap('EnterERPDonePlugin', stats => {
    if (!stats.hasErrors()) {
        process.send('compiled');
    } else {
        const jsonStats = stats.toJson();

        process.send({
            hasErrors: true,
            error: jsonStats.errors[0]
        });
    }
});

const startServer = () => {
    let serverEntry = path.join(settings.paths.backendBuildPath, 'app.js');

    nodemon({
        script: serverEntry,
        watch: serverEntry
    }).on('restart', () => {
        process.send('restarted');
    }).on('quit', process.exit);
};

const startServerOnce = _.once((error, stats) => {
    if (error) return;

    startServer();

    process.send('started');
});

compiler.watch({}, startServerOnce);

