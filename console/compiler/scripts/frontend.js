const express = require('express');
const cors = require('cors');
const compress = require('compression');
const webpack = require('webpack');
const config = require('../config/frontend');

const server = express();
const compiler = webpack(config);

compiler.hooks.watchRun.tapAsync('EnterERPWatchRunPlugin', (compiler, callback) => {
    process.send('compiling');

    callback();
});
compiler.hooks.done.tap('EnterERPDonePlugin', stats => {
    if (!stats.hasErrors()) {
        process.send('compiled');
    } else {
        const jsonStats = stats.toJson();

        process.send({
            hasErrors: true,
            error: jsonStats.errors[0].message
        });
    }
});

const devMiddleware = require('webpack-dev-middleware')(compiler, {
    publicPath: config.output.publicPath,
    headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, PATCH, OPTIONS",
        "Access-Control-Allow-Headers": "x-requested-with, content-type, authorization, cache-control"
    },
    stats: false
});

const hotMiddleware = require('webpack-hot-middleware')(compiler, {
    noInfo: true,
    quiet: true,
    reload: true,
    log: false,
    heartbeat: 2000
});

server.use(cors({
    origin: '*',
    allowedHeaders: [
        'content-type',
        'cache-control',
        'access-control-allow-headers',
        'authorization',
        'x-requested-with'
    ]
}));

// Enable compression.
server.use(compress());

// Serve webpack bundle output.
server.use(devMiddleware);

// Enable hot-reload and state-preserving compilation error display.
server.use(hotMiddleware);

server.listen(3001);
