const path = require('path');
const webpack = require('webpack');
const nodeExternals = require('webpack-node-externals');
const settings = require('../../settings');

const isProduction = process.env.NODE_ENV === 'production';

const config = {
    entry: {
        app: [path.join(settings.paths.backendSrcPath, 'index.js')]
    },

    output: {
        path: settings.paths.backendBuildPath,
        filename: '[name].js',
        libraryTarget: 'commonjs2',
        sourceMapFilename: '[name].map',
        webassemblyModuleFilename: 'erp.wasm'
    },

    resolve: {
        extensions: ['.js'],
        alias: {
            '@': settings.paths.rootPath,
            framework: path.join(settings.paths.rootPath, 'framework')
        },
        symlinks: false
    },

    module: {
        rules: [
            {
                test: /\.js$/,
                use: [
                    {
                        loader: 'esbuild-loader',
                        options: {
                            loader: 'jsx',
                            target: 'esnext'
                        }
                    }
                ],
                exclude: [/node_modules/, settings.paths.buildPath]
            },
            {
                test: /programs.json$/,
                type: 'javascript/auto',
                use: [
                    {
                        loader: 'esbuild-loader',
                        options: {
                            loader: 'jsx',
                            target: 'esnext'
                        }
                    },
                    path.join(__dirname, '../loaders/backend-loader.js')
                ],
                exclude: [/node_modules/]
            },
            {
                test: /\.html/,
                type: 'asset/source'
            },
            {
                test: /\.txt$/i,
                type: 'asset/source'
            },
            {
                test: /\.md$/i,
                type: 'asset/source'
            },
            {
                test: /\.wasm$/,
                type: 'webassembly/async'
            }
        ]
    },

    externalsPresets: {node: true},
    externals: nodeExternals({
        allowlist: [
            /\.(eot|woff|woff2|ttf|otf)$/,
            /\.(svg|png|jpg|jpeg|gif|ico|webm)$/,
            /\.(mp4|mp3|ogg|swf|webp)$/,
            /\.(css|scss|sass|less|styl)$/
        ]
    }),

    target: 'node',

    node: {
        __filename: true,
        __dirname: true
    },

    experiments: {
        asyncWebAssembly: true
    },

    // cache: {
    //     type: 'filesystem',
    //     cacheDirectory: path.join(settings.paths.rootPath, 'node_modules/.cache/webpack/backend')
    // },

    plugins: [new webpack.NoEmitOnErrorsPlugin()]
};

if (!isProduction) {
    config.mode = 'development';
    config.devtool = 'eval';

    const env = {
        'process.env.NODE_ENV': JSON.stringify('development')
    };

    config.plugins.push(new webpack.DefinePlugin(env));
} else {
    config.mode = 'production';

    const env = {
        'process.env.NODE_ENV': JSON.stringify('production')
    };

    config.plugins.push(new webpack.DefinePlugin(env));

    // config.devtool = 'source-map';
    // config.plugins.push(
    //     new webpack.BannerPlugin({
    //         raw: true,
    //         entryOnly: false,
    //         banner: `require('${
    //             // Is source-map-support installed as project dependency, or linked?
    //             require.resolve('source-map-support').indexOf(process.cwd()) === 0
    //                 ? // If it's resolvable from the project root, it's a project dependency.
    //                   'source-map-support/register'
    //                 : // It's not under the project, it's linked via lerna.
    //                   require.resolve('source-map-support/register')
    //         }')`
    //     })
    // );
}

module.exports = config;
