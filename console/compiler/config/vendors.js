const path = require('path');
const webpack = require('webpack');
const AssetsPlugin = require('assets-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const {EsbuildPlugin} = require('esbuild-loader');
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
const settings = require('../../settings');

module.exports = {
    mode: 'production',
    entry: {
        bundle: path.join(settings.paths.frontendSrcPath, 'vendors.js')
    },
    output: {
        path: settings.paths.frontendBuildPath,
        filename: path.posix.join('vendors', '[name].[contenthash].js'),
        publicPath: '/'
    },
    resolve: {
        extensions: ['.js', '.vue']
    },
    module: {
        rules: [
            {
                test: /\.css$/,
                use: [MiniCssExtractPlugin.loader, 'css-loader']
            },
            {
                test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
                type: 'asset/resource',
                generator: {
                    filename: 'assets/images/[hash][ext][query]'
                }
            },
            {
                test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/,
                type: 'asset/resource',
                generator: {
                    filename: 'assets/media/[hash][ext][query]'
                }
            },
            {
                test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
                type: 'asset/resource',
                generator: {
                    filename: 'assets/fonts/[hash][ext][query]'
                }
            }
        ]
    },
    devtool: false,
    // cache: {
    //     type: 'filesystem',
    //     cacheDirectory: path.join(settings.paths.rootPath, 'node_modules/.cache/webpack/vendors')
    // },
    plugins: [
        new webpack.DefinePlugin({
            'process.env.NODE_ENV': JSON.stringify('production')
        }),
        new AssetsPlugin({
            filename: 'manifest.json',
            path: path.join(settings.paths.vendorsBuildPath),
            includeAllFileTypes: false,
            fileTypes: ['css', 'js'],
            entrypoints: true,
            prettyPrint: true
        }),
        new MiniCssExtractPlugin({
            filename: path.posix.join('vendors', 'bundle.[contenthash].css')
        }),
        new webpack.NoEmitOnErrorsPlugin()
        // new BundleAnalyzerPlugin()
    ],
    optimization: {
        minimizer: [
            new EsbuildPlugin({
                target: 'esnext',
                legalComments: 'none',
                css: true
            })
            // new TerserPlugin({
            //     terserOptions: {
            //         ecma: 6,
            //         compress: {
            //             warnings: false
            //         },
            //         output: {
            //             comments: false
            //         }
            //     },
            //     parallel: true
            // }),
            // new OptimizeCSSPlugin()
        ]
    }
};
