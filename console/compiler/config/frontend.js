const path = require('path');
const webpack = require('webpack');
const {VueLoaderPlugin} = require('vue-loader');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const {EsbuildPlugin} = require('esbuild-loader');
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
const AssetsPlugin = require('assets-webpack-plugin');
const settings = require('../../settings');

const isProduction = process.env.NODE_ENV === 'production';
const isAnalyzing = process.env.IS_ANALYZING === 'yes';

const config = {
    output: {
        path: settings.paths.frontendBuildPath
    },

    externals: {
        vue: 'vendorGlobals.Vue',
        react: 'vendorGlobals.React',
        'react-dom': 'vendorGlobals.ReactDOM',
        lodash: 'vendorGlobals.lodash',
        sift: 'vendorGlobals.sift',
        'object-hash': 'vendorGlobals.objectHash',
        'fast-copy': 'vendorGlobals.fastCopy',
        jquery: 'vendorGlobals.jQuery',
        // 'simpl-schema': 'vendorGlobals.SimpleSchema',
        'element-ui': 'vendorGlobals.ElementUI',
        'element-ui/lib/locale/lang/en': 'vendorGlobals.ElementUILangEN',
        'element-ui/lib/locale/lang/tr': 'vendorGlobals.ElementUILangTR',
        'element-ui/lib/locale': 'vendorGlobals.ElementUILocale',
        'jwt-decode': 'vendorGlobals.jwtDecode',
        '@feathersjs/feathers': 'vendorGlobals.feathers',
        '@feathersjs/errors': 'vendorGlobals.feathersErrors',
        '@feathersjs/authentication-client': 'vendorGlobals.featherAuthenticationClient',
        '@feathersjs/transport-commons/client': 'vendorGlobals.featherTransportCommonsClient',
        'socket.io-client': 'vendorGlobals.io',
        'big.js': 'vendorGlobals.bigJS',
        traverse: 'vendorGlobals.traverse',
        'deep-diff': 'vendorGlobals.DeepDiff',
        uberproto: 'vendorGlobals.uberproto',
        sift: 'vendorGlobals.sift',
        luxon: 'vendorGlobals.luxon',
        rrule: 'vendorGlobals.rrule',
        'javascript-time-ago': 'vendorGlobals.TimeAgo',
        'perfect-scrollbar': 'vendorGlobals.PerfectScrollbar',
        'ag-grid-vue': 'vendorGlobals.AgGridVue',
        '@riophae/vue-treeselect': 'vendorGlobals.TreeSelect',
        'resize-observer-polyfill': 'vendorGlobals.ResizeObserver',
        'masonry-layout': 'vendorGlobals.Masonry',
        '@interactjs/interactjs': 'vendorGlobals.InteractJs',
        'element-resize-detector': 'vendorGlobals.elementResizeDetector',
        '@cubejs-client/core': 'vendorGlobals.cubejs',
        quill: 'vendorGlobals.Quill',
        'quill-delta': 'vendorGlobals.QuillDelta',
        fullcalendar: 'vendorGlobals.fullcalendar',
        mousetrap: 'vendorGlobals.Mousetrap',
        // '@amcharts/amcharts4/core': 'vendorGlobals.am4core',
        // '@amcharts/amcharts4/charts': 'vendorGlobals.am4charts',
        // '@amcharts/amcharts4/themes/animated': 'vendorGlobals.am4themesAnimated',
        // 'fusioncharts': 'vendorGlobals.FusionCharts',
        // 'fusioncharts/fusioncharts.charts': 'vendorGlobals.FusionChartsCharts',
        // 'fusioncharts/fusioncharts.widgets': 'vendorGlobals.FusionChartsChartsWidgets',
        // 'fusioncharts/fusioncharts.powercharts': 'vendorGlobals.FusionChartsChartsPowerCharts',
        // 'fusioncharts/themes/fusioncharts.theme.fusion': 'vendorGlobals.FusionTheme',
        highcharts: 'vendorGlobals.Highcharts',
        codeflask: 'vendorGlobals.CodeFlask'
    },

    resolve: {
        extensions: ['.js', '.jsx', '.vue'],
        alias: {
            '@': settings.paths.rootPath,
            framework: path.join(settings.paths.rootPath, 'framework')
        },
        symlinks: false
    },

    module: {
        rules: [
            {
                test: /\.worker\.js$/,
                use: {
                    loader: 'worker-loader',
                    options: isProduction ? {name: 'assets/js/[contenthash].worker.js'} : {inline: true}
                }
            },
            {
                test: /\.(js|jsx)$/,
                use: [
                    {
                        loader: 'esbuild-loader',
                        options: {
                            loader: 'jsx',
                            target: 'esnext'
                        }
                    }
                ],
                exclude: [/node_modules/, settings.paths.buildPath]
            },
            {
                test: /programs.json$/,
                type: 'javascript/auto',
                use: [
                    {
                        loader: 'esbuild-loader',
                        options: {
                            loader: 'jsx',
                            target: 'esnext'
                        }
                    },
                    path.join(__dirname, '../loaders/frontend-loader.js')
                ],
                exclude: [/node_modules/]
            },
            {
                test: /\.vue$/,
                use: [
                    {
                        loader: 'vue-loader',
                        options: {
                            prettify: false
                        }
                    }
                ],
                exclude: [/node_modules/, settings.paths.buildPath]
            },
            {
                test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
                type: 'asset/resource',
                generator: {
                    filename: 'assets/images/[hash][ext][query]'
                }
            },
            {
                test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/,
                type: 'asset/resource',
                generator: {
                    filename: 'assets/media/[hash][ext][query]'
                }
            },
            {
                test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
                type: 'asset/resource',
                generator: {
                    filename: 'assets/fonts/[hash][ext][query]'
                }
            },
            {
                test: /\.wasm$/,
                type: 'webassembly/async'
            }
        ].concat(
            styleLoaders({
                sourceMap: !isProduction,
                usePostCSS: isProduction,
                extract: isProduction
            })
        )
    },

    experiments: {
        asyncWebAssembly: true
    },

    // cache: {
    //     type: 'filesystem',
    //     cacheDirectory: path.join(settings.paths.rootPath, 'node_modules/.cache/webpack/frontend')
    // },

    plugins: [
        new VueLoaderPlugin(),
        new AssetsPlugin({
            filename: 'manifest.json',
            path: path.join(settings.paths.frontendBuildPath, 'assets'),
            includeAllFileTypes: false,
            fileTypes: ['css', 'js', 'wasm'],
            entrypoints: true,
            prettyPrint: true
        }),
        new webpack.NoEmitOnErrorsPlugin()
    ]
};

if (!isProduction) {
    config.mode = 'development';

    config.entry = {
        app: [
            !!process.env.DEV_SERVER
                ? 'webpack-hot-middleware/client?path=http://**************:3001/__webpack_hmr&noInfo=tru&quiet=truee&log=false&reload=true&heartbeat=2000'
                : 'webpack-hot-middleware/client?path=http://localhost:3001/__webpack_hmr&noInfo=tru&quiet=truee&log=false&reload=true&heartbeat=2000',
            path.join(settings.paths.frontendSrcPath, 'index.js')
        ]
    };

    config.output.filename = path.posix.join('assets', 'js/[name].js');
    config.output.publicPath = 'http://localhost:3001/';
    if (!!process.env.DEV_SERVER) {
        config.output.publicPath = 'http://**************:3001/';
    }
    config.output.globalObject = 'this';

    if (!!process.env.DEV_SERVER) {
        config.devServer = {
            host: '**************',
            port: 3001,
            disableHostCheck: true
        };
    }

    const env = {};
    env['process.env.NODE_ENV'] = JSON.stringify('development');
    env['process.env.IS_PACKAGED'] = JSON.stringify('false');
    env['process.env.IS_BUNDLED'] = JSON.stringify('false');
    if (!!process.env.DEV_SERVER) {
        env['process.env.DEV_SERVER'] = JSON.stringify('true');
    }
    config.plugins.push(new webpack.DefinePlugin(env));

    config.plugins.push(new webpack.HotModuleReplacementPlugin());
} else {
    config.mode = 'production';

    config.entry = {
        app: [path.join(settings.paths.frontendSrcPath, 'index.js')]
    };

    config.output.filename = path.posix.join('assets', 'js/[name].[contenthash].js');
    config.output.chunkFilename = path.posix.join('assets', 'js/[name].[contenthash].js');
    config.output.publicPath = '/';
    config.output.webassemblyModuleFilename = path.posix.join('assets', 'wasm/erp.wasm');

    config.devtool = false;

    const env = {};
    env['process.env.NODE_ENV'] = JSON.stringify('production');

    config.plugins.push(new webpack.DefinePlugin(env));
    config.plugins.push(
        new MiniCssExtractPlugin({
            filename: path.posix.join('assets', 'css/[name].[contenthash].css')
        })
    );

    // Optimization - Chunks and minimizer
    config.optimization = {
        minimizer: [
            new EsbuildPlugin({
                target: 'esnext',
                legalComments: 'none',
                css: true
            })
            // new TerserPlugin({
            //     terserOptions: {
            //         ecma: 6,
            //         compress: {
            //             warnings: false
            //         },
            //         output: {
            //             comments: false
            //         }
            //     },
            //     parallel: true
            // }),
            // new OptimizeCSSPlugin()
        ],
        splitChunks: {
            minSize: 0
        }
    };
}

if (isAnalyzing) {
    config.plugins.push(new BundleAnalyzerPlugin());
}

function cssLoaders(options = {}) {
    const cssLoader = {
        loader: 'css-loader',
        options: {
            sourceMap: options.sourceMap,
            importLoaders: 1,
            modules: {
                mode: 'icss'
            }
        }
    };

    const postcssLoader = {
        loader: 'postcss-loader',
        options: {
            sourceMap: options.sourceMap
        }
    };

    // Generate loader string to be used with extract text plugin.
    function generateLoaders(loader, loaderOptions) {
        const loaders = options.usePostCSS ? [cssLoader, postcssLoader] : [cssLoader];

        if (loader) {
            if (loader === 'sass') {
                loaders.push({
                    loader: loader + '-loader',
                    options: Object.assign({}, loaderOptions, {
                        implementation: require('sass'),
                        sourceMap: options.sourceMap,
                        sassOptions: {
                            includePaths: [path.join(settings.paths.frontendSrcPath, 'assets/scss')]
                        }
                    })
                });
            } else {
                loaders.push({
                    loader: loader + '-loader',
                    options: Object.assign({}, loaderOptions, {
                        sourceMap: options.sourceMap,
                        sassOptions: {
                            includePaths: [path.join(settings.paths.frontendSrcPath, 'assets/scss')]
                        }
                    })
                });
            }
        }

        // Extract CSS when that option is specified.
        // (which is the case during production build)
        if (options.extract) {
            return [MiniCssExtractPlugin.loader].concat(loaders);
        } else {
            return ['vue-style-loader'].concat(loaders);
        }
    }

    // https://vue-loader.vuejs.org/en/configurations/extract-css.html
    return {
        css: generateLoaders(),
        postcss: generateLoaders(),
        scss: generateLoaders('sass')
    };
}

function styleLoaders(options) {
    const output = [];
    const loaders = cssLoaders(options);

    for (const extension in loaders) {
        const loader = loaders[extension];

        output.push({
            test: new RegExp('\\.' + extension + '$'),
            use: loader
        });
    }

    return output;
}

module.exports = config;
