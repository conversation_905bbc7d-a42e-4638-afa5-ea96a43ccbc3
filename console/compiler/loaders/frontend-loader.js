const path = require('path');
const fs = require('fs');

module.exports = function(source) {
    const isProduction = process.env.NODE_ENV === 'production';
    const programs =  JSON.parse(source);
    let code = '';

    code += `import _ from 'lodash';`;
    code += `import Vue from 'vue';`;
    code += `import LoadingComponent from '@/kernel/frontend/components/elements/loading.vue';`;

    code += '(function(g) {';
    code += 'const programs = [];';
    code += 'let program, resolvedItems;';
    programs.forEach(programName => {
        const programPath = path.join(process.cwd(), 'programs', programName);
        const viewsPath = path.join(programPath, 'views');
        const componentsPath = path.join(programPath, 'components');
        const widgetsPath = path.join(programPath, 'widgets');
        const routesPath = path.join(programPath, 'routes.js');
        const extendPath = path.join(programPath, 'extend-frontend.js');
        const initPath = path.join(programPath, 'frontend.js');

        // Load program
        code += `program = {name: require('@/programs/${programName}/program.json').name, routes: [], components: [], widgets: {}, extend: null, init: null};`;
        if (fs.existsSync(viewsPath)) {
            code += `resolvedItems = require.context('@/programs/${programName}/views/', true, /([\\w-_]+)\\.vue/, 'lazy');`;
            code += `for (const key of resolvedItems.keys()) {
                    let viewPath = key.replace(/^\\.\\//, '').replace(/\\.vue/, '');
                    let viewName = '${programName}-views-' + viewPath.replace(/\\//g, '-');
                    if (viewName.indexOf('-_') !== -1) continue;
                    program.components.push(viewName);
                    Vue.component(viewName, () => ({
                        component: import('@/programs/${programName}/views/' + viewPath + '.vue'),
                        loading: LoadingComponent,
                        delay: 0
                    }));
                }`;
        }
        if (fs.existsSync(componentsPath)) {
            code += `resolvedItems = require.context('@/programs/${programName}/components/', false, /^\\.\\/([\\w-_]+)\\.vue/, 'lazy');`;
            code += `for (const key of resolvedItems.keys()) {
                    let componentPath = key.replace(/^\\.\\//, '').replace(/\\.vue/, '');
                    let componentName = '${programName}-components-' + componentPath.replace(/\\//g, '-');
                    if (componentName.indexOf('-_') !== -1) continue;
                    program.components.push(componentName);
                    Vue.component(componentName, () => import('@/programs/${programName}/components/' + componentPath + '.vue'));
                }`;
        }
        if (fs.existsSync(widgetsPath)) {
            code += `resolvedItems = require.context('@/programs/${programName}/widgets/', true, /^\\.\\/([\\w-_]+)\\/widget\\.json/, 'lazy');`;
            code += `for (const key of resolvedItems.keys()) {
                    let widgetName = key.replace(/^\\.\\//, '').replace(/\\/widget\\.json/, '');
                    let widgetComponentName = '${programName}-widgets-' + widgetName;
                    Vue.component(widgetComponentName, () => import('@/programs/${programName}/widgets/' + widgetName + '/main.vue'));
                }`;
        }
        // if (fs.existsSync(viewsPath)) {
        //     code += `resolvedItems = require.context('@/programs/${programName}/views/', true, /([\\w-_]+)\\.vue/, 'lazy');`;
        //     code += `for (const key of resolvedItems.keys()) {
        //             let viewPath = key.replace(/^\\.\\//, '').replace(/\\.vue/, '');
        //             let viewName = '${programName}-views-' + viewPath.replace(/\\//g, '-');
        //             if (viewName.indexOf('-_') !== -1) continue;
        //             program.components.push(viewName);
        //             Vue.component(viewName, require('@/programs/${programName}/views/' + viewPath + '.vue').default);
        //         }`;
        // }
        // if (fs.existsSync(componentsPath)) {
        //     code += `resolvedItems = require.context('@/programs/${programName}/components/', false, /^\\.\\/([\\w-_]+)\\.vue/, 'lazy');`;
        //     code += `for (const key of resolvedItems.keys()) {
        //             let componentPath = key.replace(/^\\.\\//, '').replace(/\\.vue/, '');
        //             let componentName = '${programName}-components-' + componentPath.replace(/\\//g, '-');
        //             if (componentName.indexOf('-_') !== -1) continue;
        //             program.components.push(componentName);
        //             Vue.component(componentName, require('@/programs/${programName}/components/' + componentPath + '.vue').default);
        //         }`;
        // }

        if (fs.existsSync(routesPath)) {
            code += `program.routes = require('@/programs/${programName}/routes.js').default;`;
        }
        if (fs.existsSync(extendPath)) {
            code += `program.extend = require('@/programs/${programName}/extend-frontend.js').default;`;
        }
        if (fs.existsSync(initPath)) {
            code += `program.init = require('@/programs/${programName}/frontend.js').default;`;
        }
        code += `programs.push(program);`;
    });

    code += `_.merge(g.entererp.programs, programs);`;
    code += '})(window);';

    return code;
};
