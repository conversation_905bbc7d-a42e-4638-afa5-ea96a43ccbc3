const path = require('path');
const fs = require('fs');
const crypto = require('crypto');

module.exports = function(source) {
    const programs = JSON.parse(source);

    let code = '(function(g) {';
    let clientVersion = null;

    if (process.env.NODE_ENV === 'production') {
        const currentDate = (new Date()).valueOf().toString();
        const randomNumber = Math.random().toString();

        clientVersion = crypto.createHash('sha1')
            .update(currentDate + randomNumber)
            .digest('hex')
            .substr(0, 20);
    } else {
        clientVersion = '';
    }

    code += `const _ = require('lodash');`;
    code += 'const programs = [];';
    code += 'let program, resolvedItems;';
    programs.forEach(programName => {
        const programPath = path.join(process.cwd(), 'programs', programName);
        const collectionsPath = path.join(programPath, 'collections');
        const migrationsPath = path.join(programPath, 'migrations');
        const schemasPath = path.join(programPath, 'schemas');
        const methodsPath = path.join(programPath, 'methods');
        const jobsPath = path.join(programPath, 'jobs');
        const translationsPath = path.join(programPath, 'translations');
        const templatesPath = path.join(programPath, 'templates');
        const widgetsPath = path.join(programPath, 'widgets');
        const extendPath = path.join(programPath, 'extend-backend.js');
        const initPath = path.join(programPath, 'backend.js');

        // Load program
        code += `program = _.assign({permissions: []}, require('@/programs/${programName}/program.json'), {collections: [], migrations: [], schemas: [], methods: [], jobs: [], translations: {}, records: [], seeds: [], templates: [], widgets: [], extend: null, init: null});`;
        if (fs.existsSync(collectionsPath)) {
            code += `resolvedItems = require.context('@/programs/${programName}/collections/', false, /^\\.\\/([\\w-_]+)\\.js/);`;
            code += `_.each(resolvedItems.keys(), key => program.collections.push(resolvedItems(key).default));`;
        }
        if (fs.existsSync(migrationsPath)) {
            code += `resolvedItems = require.context('@/programs/${programName}/migrations/', false, /^\\.\\/([\\w-_]+)\\.js/);`;
            code += `_.each(resolvedItems.keys(), key => program.migrations.push(resolvedItems(key).default));`;
        }
        if (fs.existsSync(schemasPath)) {
            code += `resolvedItems = require.context('@/programs/${programName}/schemas/', false, /^\\.\\/([\\w-_]+)\\.js/);`;
            code += `_.each(resolvedItems.keys(), key => program.schemas.push(resolvedItems(key).default));`;
        }
        if (fs.existsSync(methodsPath)) {
            code += `resolvedItems = require.context('@/programs/${programName}/methods/', false, /^\\.\\/([\\w-_]+)\\.js/);`;
            code += `_.each(resolvedItems.keys(), key => program.methods.push(resolvedItems(key).default));`;
        }
        if (fs.existsSync(jobsPath)) {
            code += `resolvedItems = require.context('@/programs/${programName}/jobs/', false, /^\\.\\/([\\w-_]+)\\.js/);`;
            code += `_.each(resolvedItems.keys(), key => program.jobs.push(resolvedItems(key).default));`;
        }
        if (fs.existsSync(translationsPath)) {
            code += `resolvedItems = require.context('@/programs/${programName}/translations/', false, /^\\.\\/([\\w-_]+)\\.json/);`;
            code += `_.each(resolvedItems.keys(), key => { let locale = key.replace(/^\\.\\//, '').replace(/\\.json/, ''); program.translations[locale] = resolvedItems(key); });`;
        }
        if (fs.existsSync(templatesPath)) {
            code += `resolvedItems = require.context('@/programs/${programName}/templates/', false, /^\\.\\/([\\w-_]+)\\.js/);`;
            code += `_.each(resolvedItems.keys(), key => program.templates.push(resolvedItems(key).default));`;
        }
        if (fs.existsSync(widgetsPath)) {
            code += `resolvedItems = require.context('@/programs/${programName}/widgets/', true, /^\\.\\/([\\w-_]+)\\/widget\\.json/);`;
            code += `_.each(resolvedItems.keys(), key => {
            let widgetName = key.replace(/^\\.\\//, '').replace(/\\/widget\\.json/, '');
            program.widgets.push({
                name: widgetName,
                definition: resolvedItems(key),
                method: null
            });
            });`;

            code += `resolvedItems = require.context('@/programs/${programName}/widgets/', true, /^\\.\\/([\\w-_]+)\\/method\\.js/);`;
            code += `_.each(resolvedItems.keys(), key => {
            let widgetName = key.replace(/^\\.\\//, '').replace(/\\/method\\.js/, '');
            let index = program.widgets.findIndex(w => w.name === widgetName);
            program.widgets[index].method = resolvedItems(key).default;
            });`;
        }
        if (fs.existsSync(extendPath)) {
            code += `program.extend = require('@/programs/${programName}/extend-backend.js').default;`;
        }
        if (fs.existsSync(initPath)) {
            code += `program.init = require('@/programs/${programName}/backend.js').default;`;
        }
        code += `programs.push(program);`;
    });
    code += `g.__programs = programs;`;
    code += `g.entererpClientVersion = "${clientVersion}"`;
    code += '})(global);';

    return code;
};
